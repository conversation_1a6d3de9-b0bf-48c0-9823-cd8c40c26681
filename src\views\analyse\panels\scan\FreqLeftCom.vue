<script setup name="FreqLeftCom">
  import AmRuler from './AmRuler.vue'
  import RefLine from './RefLine.vue'
  import OverLap from './OverLap.vue'
  import useChartsStore from '@/store/modules/charts'
  import { ElMessage } from 'element-plus'

  const chartsStore = useChartsStore()

  // 获取频谱文件列表
  const freqFiles = computed(() => chartsStore.freqFiles)

  // 定义父组件传递过来的自定义事件，用于触发分析
  const emit = defineEmits(['analysis'])

  // 参考线和幅度标尺面板
  const panels = [
    {
      title: '重叠处理',
      value: 'OverLap'
    },
    {
      title: '参考线',
      value: 'RefLine'
    },
    {
      title: '幅度标尺',
      value: 'AmRuler'
    }
  ]

  // 组件引用映射
  const dom = shallowReactive({
    OverLap,
    AmRuler,
    RefLine
  })

  // 当前展开的面板项
  const activeItems = ref(panels.map(panel => panel.value))

  // 存储用户选择的文件
  const selectedFiles = ref([])

  // el-table 的 ref，用于操作表格方法
  const tableRef = ref(null)

  // 当表格的选择发生变化时，更新 selectedFiles
  const handleSelectionChange = selection => {
    selectedFiles.value = selection
  }

  // 点击分析按钮时，触发父组件的分析事件并传递选中的文件
  const handleAnalysis = () => {
    if (selectedFiles.value.length === 0) {
      // 如果没有选中文件，提示用户选择
      ElMessage.warning('请选择文件进行分析')
      return
    }
    emit('analysis', selectedFiles.value) // 触发分析事件
  }

  onMounted(() => {
    if (freqFiles.value.length > 0) {
      tableRef.value?.toggleAllSelection() // 确保组件挂载后，表格的默认选中生效
    }
  })
</script>

<template>
  <div>
    <!-- 文件选择表格 -->
    <el-table
      ref="tableRef"
      border
      :data="freqFiles"
      height="300"
      @selection-change="handleSelectionChange"
    >
      <!-- 文件选择复选框 -->
      <el-table-column type="selection" width="50"></el-table-column>
      <!-- 文件名列，超出悬浮显示 -->
      <el-table-column label="文件名" prop="fileName" show-overflow-tooltip />
      <el-table-column label="中心频率" prop="centerFreqIn" />
      <el-table-column label="带宽" prop="intermediateFrequencyBandwidth" />
      <el-table-column label="步长" prop="steplen" width="80" />
    </el-table>

    <!-- 分析按钮 -->
    <el-button class="w-full my-2 h-full" size="large" @click="handleAnalysis">分析</el-button>
  </div>

  <!-- 可折叠面板 -->
  <el-collapse v-model="activeItems" class="collapse-panels">
    <el-collapse-item
      v-for="panel in panels"
      :key="panel.value"
      :title="panel.title"
      :name="panel.value"
    >
      <!-- 动态加载组件 -->
      <component :is="dom[panel.value]" />
    </el-collapse-item>
  </el-collapse>
</template>

<style scoped lang="scss">
  .panels-leave-active,
  .panels-enter-active {
    transition: all 0.8s ease-in-out;
  }

  .panels-enter-from {
    transform: translateY(-200px);
    opacity: 0;
  }
  .panels-leave-to {
    transform: translateY(-200px);
    opacity: 0;
  }
</style>
