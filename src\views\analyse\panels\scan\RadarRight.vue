<template>
  <el-collapse v-model="activeItems" class="collapse-panels">
    <el-collapse-item
      v-for="panel in panels"
      :key="panel.value"
      :title="panel.title"
      :name="panel.value"
    >
      <component :is="dom[panel.value]" @update:chartData="$event => emit('update:chartData', $event)" />
    </el-collapse-item>
  </el-collapse>
</template>

<script setup name="RadarRight">

import RadarAnalyseConfig from './RadarAnalyseConfig.vue'
import RadarScanPick from './RadarScanPick.vue'
import AmRuler from './AmRuler.vue'
import RefLine from './RefLine.vue'

const emit = defineEmits(['update:chartData'])
const panels = [
  {
    title: '分析设置',
    value: 'RadarAnalyseConfig',
  },
  {
    title: '信号选取',
    value: 'RadarScanPick'
  },
  {
    title: '参考线',
    value: 'RefLine'
  },
  {
    title: '幅度标尺',
    value: 'AmRuler'
  }
]

const dom = shallowReactive({
  RadarAnalyseConfig, RadarScanPick, AmRuler,RefLine
})

const activeItems = ref(panels.map(panel => panel.value))

</script>

<style scoped lang="scss">
.panels-leave-active,
.panels-enter-active {
  transition: all 0.8s ease-in-out;
}

.panels-enter-from {
  transform: translateY(-200px);
  opacity: 0;
}
.panels-leave-to {
  transform: translateY(-200px);
  opacity: 0;
}
</style>
