<template>
  <div v-if="switchVisible" class="theme-toggler-content">
    <CommonThemeToggler :aria-checked="isDark" @click="() => toggleDark()" />
  </div>
</template>

<script setup>
  import CommonThemeToggler from './theme-toggler.vue'
  import useSettingsStore from '@/store/modules/settings'
  import { useDark, useToggle } from '@vueuse/core'
  import { settings as globalSettings } from '@/utils/settings'

  const isDark = useDark({
    storageKey: 'el-theme-appearance'
  })
  const switchVisible = ref(true)
  const settingsStore = useSettingsStore()
  const toggle = useToggle(isDark)
  const route = useRoute()

  const toggleDark = () => {
    toggle()
    if (isDark.value) {
      settingsStore.mode = 'dark'
      settingsStore.theme = '#fff'
    } else {
      settingsStore.mode = 'light'
      settingsStore.theme = '#1c3052'
    }
  }

  onMounted(() => {
    if (globalSettings.VITE_GLOB_UPPER_COMPUTER_MODE === '1' && !isDark.value) {
      toggleDark()
    }
  })
  // watchEffect(() => {
  //   if (route?.path.includes('analyse')) {
  //     switchVisible.value = true
  //   } else {
  //     switchVisible.value = false
  //     isDark.value && toggleDark()
  //   }
  // })
</script>

<style scoped lang="scss">
  .theme-toggler-content {
    display: flex;
    align-items: center;
    float: right;
    margin-top: 12px;
  }
  .theme-toggler-content {
    background-color: var(--background-color);
    color: var(--text-color);
    transition: border-color var(--el-transition-duration),
      background-color var(--el-transition-duration-fast);
    background-color: transparent;
    border-radius: 50%;
    height: 24px;
    padding: 0 12px;
  }
</style>
