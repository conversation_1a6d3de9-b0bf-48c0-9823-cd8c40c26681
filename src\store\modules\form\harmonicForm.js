import { defineStore } from 'pinia'
import useChartsStore from '../charts'

const { global } = useChartsStore()
export default defineStore('dmFormStore', {
  state: () => {
    return {
      // 幅度
      showRefLevel: global.refLevel,
      // 频率
      step: 0.0,
      span: 200000,
      rbw: 1000,
      vbw: 1000,
      // 测量
      num: 5,
      measureType: 0,
      traceType: 0,
      isPeakTrack: false
    }
  },
  getters: {
    centerFreqIn() {
      return global.centerFreqIn
    },
    debugMode() {
      return global.debugMode
    },
    samplingRate() {
      return global.samplingRate
    },
    bitRate() {
      return global.bitRate
    },
    intermediateFrequencyBandwidth() {
      return global.intermediateFrequencyBandwidth
    },
    refLevel() {
      return global.refLevel
    },
    scale() {
      return global.scale
    }
  }
})
