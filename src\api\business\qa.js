import request from '@/utils/request'

// 查询答疑互动记录列表
export function listQa (query) {
  return request({
    url: '/business/qa/list',
    method: 'get',
    params: query
  })
}

// 查询答疑互动记录详细
export function getQa (qaId) {
  return request({
    url: '/business/qa/' + qaId,
    method: 'get'
  })
}

// 新增答疑互动记录
export function addQa (data) {
  return request({
    url: '/business/qa',
    method: 'post',
    data: data
  })
}
// // 新增答疑互动记录
// export function addQa (data) {
//   return request({
//     url: '/business/answer',
//     method: 'post',
//     data: data
//   })
// }

// 修改答疑互动记录
export function updateQa (data) {
  return request({
    url: '/business/qa',
    method: 'put',
    data: data
  })
}

// 删除答疑互动记录
export function delQa (qaId) {
  return request({
    url: '/business/qa/' + qaId,
    method: 'delete'
  })
}
// // 回答
// export function updateSrQaByTeacher (data) {
//   return request({
//     url: '/business/qa/updateSrQaByTeacher',
//     method: 'put',
//     data
//   })
// }
// 回答
export function updateSrQaByTeacher (data) {
  return request({
    url: '/business/answer',
    method: 'post',
    data
  })
}
