<template>
  <data-form :fields="fields" :model="model" />
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData'
  import { rateUnits } from '@/constant/units'
  import useScanFormStore from '@/store/modules/form/scanForm'
  import useChartsStore from '@/store/modules/charts'

  const emit = defineEmits(['operation'])
  const file = useChartsStore().getFileInfo()
  const fields = ref([
    {
      target: 'fileCenterFreIn',
      name: '中心频率',
      options: rateUnits,
      appendSelect: true,
      disabled: true
    },
    {
      target: 'fileIfbw',
      name: '中频带宽',
      options: rateUnits,
      appendSelect: true,
      disabled: true
    },
    {
      target: 'fileSamplingRate',
      name: '采样率',
      options: rateUnits,
      appendSelect: true,
      disabled: true
    },
    // {
    //   target: 'fileBitRate',
    //   name: '码速率',
    //   options: rateUnits,
    //   appendSelect: true,
    //   disabled: true
    // },
    { target: 'iqNum', name: 'iQ点数', disabled: true },
    {
      target: 'iqReverse',
      name: 'IQ颠倒',
      isCheckbox: true,
      // disabled: computed(() => file.fileType === '1'),
      change(val) {
        emit('operation')
      }
    }
    // {
    //   target: 'logarithm',
    //   name: '取对数',
    //   isCheckbox: true,
    //   disabled: computed(() => file.fileType === '1')
    // }
  ])
  const model = reactive(new ModelData(fields.value, 'scan'))
  const scanFormStore = useScanFormStore()
  watch(
    () => scanFormStore,
    () => {
      model.update()
    },
    { deep: true }
  )
</script>
