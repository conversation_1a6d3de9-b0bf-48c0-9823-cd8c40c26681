<template>
  <div ref="slider" class="slider" @click.stop="handleClickSlider">
    <div class="process" :style="{ width: processWidth }" />
    <div ref="thunk" class="thunk" :style="{ left: thunkLeft }">
      <div ref="dot" class="block" :style="{ width: viewWidth }" />
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue'

  const props = defineProps({
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 100
    },
    modelValue: {
      type: Number,
      default: 0
    },
    viewNum: {
      type: Number,
      default: 0
    },
    isDrag: {
      type: Boolean,
      default: true
    }
  })

  const emit = defineEmits(['update:modelValue', 'change:startOffset'])

  const slider = ref(null)
  const thunk = ref(null)
  const dot = ref(null)
  const current = ref(props.modelValue)

  const scale = computed(() => {
    const scale = Number((current.value - props.min) / (props.max - props.min))
    return props.max === 1 ? 1 : scale ? scale : 0
  })

  const processWidth = computed(() => {
    if (!slider.value || !thunk.value) return '0%'
    return `${scale.value * 100}%`
  })

  const viewWidth = computed(() => {
    if (!slider.value) return '0%'
    return props.viewNum === 1 && props.max === 1 ? '100%' : `${(props.viewNum / props.max) * 100}%`
  })

  const thunkLeft = computed(() => {
    if (!slider.value) return '0%'
    return `${scale.value * 100}%`
  })

  watch(
    () => props.modelValue,
    val => {
      if (val > props.max - props.viewNum) {
        current.value = props.max - props.viewNum
        return
      }
      current.value = val
    }
  )

  const handleClickSlider = event => {
    if (!props.isDrag || event.target === dot.value) return
    const width = slider.value.offsetWidth
    const newScale = event.offsetX / width
    current.value = newScale * (props.max - props.min) + props.min
    emit('update:modelValue', current.value)
    emit('change:startOffset', current.value)
  }

  onMounted(() => {
    if (!props.isDrag) return

    const handleMouseMove = (event, initialWidth, initialX) => {
      const newWidth = event.clientX - initialX + initialWidth
      const newScale = newWidth / slider.value.offsetWidth
      current.value = Math.ceil((props.max - props.min) * newScale + props.min)
      current.value = Math.max(current.value, props.min)
      current.value = Math.min(current.value, props.max - props.viewNum)
      emit('update:modelValue', current.value)
    }

    thunk.value.onmousedown = event => {
      const initialWidth = parseInt(processWidth.value)
      const initialX = event.clientX
      document.onmousemove = e => handleMouseMove(e, initialWidth, initialX)
      document.onmouseup = () => {
        document.onmousemove = document.onmouseup = null
        emit('change:startOffset', current.value)
      }
    }
  })
</script>

<style scoped>
  .clear:after {
    content: '';
    display: block;
    clear: both;
  }

  .slider {
    position: relative;
    width: 100%;
    height: 6px;
    background: var(--slider-bg);
    border: 1px solid var(--play-bar-active);
    border-radius: 5px;
    cursor: pointer;
    z-index: 5;
  }

  .slider .process {
    position: absolute;
    left: 0;
    top: 0;
    height: 6px;
    border-radius: 5px;
    background: var(--play-bar-active); /* 你可以在这里调整为更合适的颜色 */
    border: 1px solid #444; /* 对于浅色背景，选择较深的边框颜色 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 轻微的阴影增加深度感 */
    z-index: 111;
  }

  .slider .thunk {
    position: absolute;
    top: -4px;
    width: 10px;
    height: 4px;
    z-index: 122;
  }

  .slider .block {
    height: 12px;
    border-radius: 6px;
    background: #383838;
    border: 1px solid var(--play-bar-slider-border);
    transition: 0.2s all;
  }

  .slider .block:hover {
    transform: scale(1.1);
    opacity: 0.8;
  }
</style>
