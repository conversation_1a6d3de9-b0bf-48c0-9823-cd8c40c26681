<script setup name="SignalAnalyse">
  import DataChart from '@/components/DataChart' //频谱图组件
  import PlayBack from '@/components/PlayBack' //播放组件
  import useChartsStore from '@/store/modules/charts'
  import useScanFormStore from '@/store/modules/form/scanForm'
  import useChartOptions from '@/common/hooks/chartOptions'
  import { usePlaybackStore } from '@/store/modules/playback'
  import { queryFileFft } from '@/api/charts'
  import { numToPlot, plotToNum } from '@/utils/utils'
  import { cloneDeep, round } from 'lodash'

  let oldFileName = null
  const leftForm = defineAsyncComponent(() => import('@/views/analyse/panels/scan/LeftForm.vue'))
  const rightForm = defineAsyncComponent(() => import('@/views/analyse/panels/scan/RightForm.vue'))
  const chartsStore = useChartsStore()
  const scanFormStore = useScanFormStore()
  const chartData = ref([])
  const config = ref({})
  const loaded = ref(false)
  const activeName = ref('first')
  const signResults = ref([])
  const pickTable = ref(null)
  const fileName = computed(() => chartsStore.file.fileName)
  const { global } = chartsStore
  // 点击信号分选同步表单
  const form = reactive({
    centerFreqIn: 0,
    samplingRate: 0,
    ifBw: 0,
    bitRate: 0,
    iqReserve: 0,
    logarithm: 0
  })

  const lastDccCF = ref(null) // 上一次下变频中心频率
  //test-播放控制接口测试
  const playbackStore = usePlaybackStore()
  const bodyInfo = ref(null)

  const refreshPage = () => {
    window.location.reload()
  }
  const maxList = computed(() => {
    const temp = cloneDeep(chartData.value)
    if (!temp || temp.length === 0) {
      return []
    }
    temp.sort((a, b) => a - b)
    const maxAm = temp.pop()
    const maxAmMapPlot = transToPlot(chartData.value.indexOf(maxAm))
    const result = [
      {
        num: 1,
        am: maxAm + 'dB',
        rate: numToPlot(maxAmMapPlot),
        ratediff: numToPlot(0),
        amdiff: '0dB'
      }
    ]
    while (result.length < 8 && temp.length > 0) {
      const last = result[result.length - 1]
      const lastAm = parseInt(last.am)
      const item = temp.pop()
      if (item === lastAm) {
        continue
      }
      const rate = transToPlot(chartData.value.indexOf(item))
      if (Math.abs(rate - plotToNum(last.rate)) > 100) {
        result.push({
          num: result.length + 1,
          am: item + 'dB',
          rate: numToPlot(rate),
          amdiff: maxAm - item + 'dB',
          ratediff: numToPlot(maxAmMapPlot - rate)
        })
      }
    }
    return result
  })
  // 从下标转化为对应频率
  const transToPlot = num => {
    const { global } = chartsStore
    const left = global.centerFreqIn - global.intermediateFrequencyBandwidth
    return (num / chartData.value.length) * global.intermediateFrequencyBandwidth + left
  }
  const paramsRsp = ref([
    { label: '载频', value: '', key: 'cf_Out' },
    { label: '调制类型', value: '', key: 'type_Out' },
    { label: '功率', value: '', key: 'power_Out' },
    { label: '3DB带宽', value: '', key: 'xbw_Out' },
    { label: 'Beta带宽（99%功率）', value: '', key: 'betaBW_Out' }
  ])
  const formSync = () => {
    form.centerFreqIn = scanFormStore.fileCenterFreIn
    form.ifBw = scanFormStore.fileIfbw
    form.bitRate = scanFormStore.fileBitRate
    form.samplingRate = scanFormStore.fileSamplingRate
    form.iqReserve = scanFormStore.iqReserve
    form.logarithm = scanFormStore.logarithm
  }
  const updateChartData = data => {
    chartData.value = data
    scanFormStore.endPoint = chartData.value.length - 1
    scanFormStore.allLen = chartData.value.length
    chartsStore.settings.IQNum = chartData.value.length
  }
  const filterSign = async () => {
    scanFormStore.setCenterFre()
    const body = generateBody()
    bodyInfo.value = Object.assign(bodyInfo.value, body)
    await playbackStore.clearTimer()
    await playbackStore.play(pollingPlayBack, playbackStore.startOffset) // 初始化时启动播放
    scanFormStore.setFileParams(body)
    scanFormStore.init(body.intermediateFrequencyBandwidth, body.centerFreqIn)
  }
  const generateBody = () => {
    const { fileName, uploadTime } = chartsStore.getFileInfo()
    const { fileCenterFreIn, fileSamplingRate, fileIfbw, fileBitRate, iqReverse, logarithm } =
      scanFormStore
    return {
      fileName,
      centerFreqIn: fileCenterFreIn,
      samplingRate: fileSamplingRate,
      intermediateFrequencyBandwidth: fileIfbw,
      bitRate: fileBitRate,
      iqReverse: Number(iqReverse),
      logarithm: Number(logarithm),
      path: 0,
      uploadTime
    }
  }

  /**
   * test-播放控制接口测试
   * 轮询播放回调
   * @param body 请求体对象
   * @returns Promise<void>
   */
  const pollingPlayBack = async startOffset => {
    oldFileName = bodyInfo.value.fileName
    bodyInfo.value.startPoint = undefined
    bodyInfo.value.endPoint = undefined
    bodyInfo.value.startOffset = startOffset ? startOffset : 0
    // 下变频、滤波开关传参
    bodyInfo.value.ddcFlag = scanFormStore.ddcFlag
    bodyInfo.value.filterFlag = scanFormStore.filterFlag
    bodyInfo.value.downCenterFreIn = scanFormStore.pickCf
    bodyInfo.value.filterorder = scanFormStore.filterorder
    bodyInfo.value.coefficientDown = scanFormStore.coefficientDown
    bodyInfo.value.isITU = scanFormStore.isITU
    bodyInfo.value.isSignalSort = scanFormStore.isSignalSort
    // 回放时间
    bodyInfo.value.playBackTime = playbackStore.playBackTime

    if (lastDccCF.value !== scanFormStore.pickCf) {
      chartsStore.setGlobal('centerFreqIn', scanFormStore.pickCf || 0)
      scanFormStore.init(global.intermediateFrequencyBandwidth, global.centerFreqIn)
      lastDccCF.value = scanFormStore.pickCf
      generateConfig()
    }

    // 获取fft数据，频谱数据
    const { data } = await queryFileFft(bodyInfo.value)
    playbackStore.setStartOffset(data.startOffset)
    playbackStore.setMaxOffset(data.maxOffset)
    // 更新频谱数据
    updateChartData(data.valueList)
    scanFormStore.iqNum = data.pIQ_In
    scanFormStore.capital = false
    signResults.value = data.sig_results
      ? data.sig_results.map(item => {
          return {
            bandwidth: numToPlot(item.bw),
            centerRate: numToPlot(item.cf),
            am: round(item.amp, 2),
            ...item
          }
        })
      : []
    formSync()
    setSignalEntity(data)
  }

  /**
   * 参数估计
   * @param data
   */
  const setSignalEntity = data => {
    const val = data?.signalITUEntity
    if (val) {
      const rates = ['cf_Out', 'xbw_Out', 'betaBW_Out']
      const dbm = 'power_Out'
      const type = 'type_Out'
      paramsRsp.value.forEach(item => {
        const p = val[item.key]
        if (rates.includes(item.key)) {
          item.value = numToPlot(p)
        } else if (item.key === dbm) {
          item.value = round(p, 2) + 'dbm'
        } else {
          item.value = p
        }
      })
    } else {
      paramsRsp.value = [
        { label: '载频', value: '', key: 'cf_Out' },
        { label: '调制类型', value: '', key: 'type_Out' },
        { label: '功率', value: '', key: 'power_Out' },
        { label: '3DB带宽', value: '', key: 'xbw_Out' },
        { label: 'Beta带宽（99%功率）', value: '', key: 'betaBW_Out' }
      ]
    }
  }
  /**
   * 生成配置对象
   * @returns 配置对象
   */
  const generateConfig = () => {
    const { chartOptions } = useChartOptions('spectrum')
    config.value = {
      type: 'static',
      chart: computed(() => {
        const options = cloneDeep(chartOptions.value)
        options.chart.height =
          document.body.clientHeight - 416 > 500 ? document.body.clientHeight - 516 : 500
        const left = scanFormStore.pickCf - scanFormStore.pickBw / 2
        const axis = options.xAxis
        axis.max = scanFormStore.len - 1
        axis.tickInterval = (scanFormStore.len - 1) / 10
        axis.labels.formatter = function () {
          const val = (this.value / (scanFormStore.len - 1)) * scanFormStore.pickBw + left
          return numToPlot(val)
        }
        const yAxis = options.yAxis
        yAxis.max = scanFormStore.refLevel
        yAxis.min = scanFormStore.refLevel - scanFormStore.amScale * 10
        yAxis.tickInterval = scanFormStore.amScale
        yAxis.tickAmount = 11
        if (form.logarithm || scanFormStore.capital) {
          options.yAxis.max = undefined
          options.yAxis.min = undefined
          yAxis.tickInterval = undefined
        }
        return options
      }),
      viewNum: 'all',
      start: computed(() => scanFormStore.startPoint),
      hideBar: true
    }
  }

  const init = async () => {
    const body = cloneDeep(chartsStore.getFileInfo())
    bodyInfo.value = body
    if (loaded.value || oldFileName === body.fileName) {
      return
    }
    loaded.value = true
    //test-播放控制接口测试
    await playbackStore.play(pollingPlayBack, playbackStore.startOffset) // 初始化时启动播放
    // 设置文件信息和参数
    scanFormStore.setFileParams(bodyInfo.value)

    // 设置全局变量（中心频率和中频带宽）
    chartsStore.setGlobal('centerFreqIn', bodyInfo.value.centerFreqIn || 0)
    chartsStore.setGlobal(
      'intermediateFrequencyBandwidth',
      bodyInfo.value.intermediateFrequencyBandwidth || 0
    )
    // 初始化分析设置的中心频率和中频带宽
    scanFormStore.init(global.intermediateFrequencyBandwidth, global.centerFreqIn)
    // 记录最新一次的频谱图中心频率
    lastDccCF.value = global.centerFreqIn || 0

    const { file } = chartsStore
    scanFormStore.fileCenterFreIn = scanFormStore.pickCf = file.centerFreqIn
    scanFormStore.fileDebugMode = file.debugMode
    scanFormStore.fileSamplingRate = file.samplingRate
    scanFormStore.fileBitRate = file.bitRate

    scanFormStore.fileIfbw = file.intermediateFrequencyBandwidth
    scanFormStore.iqReverse = !!file.iqReverse
    scanFormStore.logarithm = !!file.logarithm
    const params = chartsStore.getParamsEstimate()
    setSignalEntity(params)
    generateConfig()
  }

  onMounted(async () => {
    await init()
  })
  onBeforeMount(() => {
    // 将本地缓存写入store里面的内容
    chartsStore.readStorage()
  })
  onUnmounted(() => {
    // 将store里面的内容写入本地缓存
    chartsStore.writeStorage()
  })
  onActivated(async () => {
    console.log('onActivated')
    await init()
  })
  onDeactivated(() => {
    loaded.value = false
  })
  window.onunload = function () {
    chartsStore.writeStorage()
  }
</script>

<template>
  <div class="analyse-main">
    <div class="charts-body">
      <el-row style="height: 100%" :gutter="12">
        <!-- 左边表单，按钮触发改成参数触发 -->
        <el-col :span="4" class="form-main">
          <div class="file-name">
            <el-tooltip :content="fileName">
              <span class="file-wrapper">{{ fileName }}</span>
            </el-tooltip>
            <el-button style="margin-left: 10px" @click="refreshPage">重新加载</el-button>
          </div>
          <left-form @filter="filterSign" />
        </el-col>
        <!-- 表格项 -->
        <el-col :span="16" class="chart-item">
          <!-- test-播放控制接口测试 -->
          <!--  播放控制栏 -->
          <PlayBack :pollingFunctions="pollingPlayBack" />
          <!-- 频谱图 -->
          <div class="chart-container" style="flex: 1">
            <data-chart
              v-if="config.chart"
              id="signalAnalyse"
              title="频谱图"
              :data="chartData"
              :config="config"
            />
          </div>
          <div class="picker-list">
            <el-tabs v-model="activeName" type="border-card" class="tabs">
              <el-tab-pane label="信号列表" name="first">
                <el-table :ref="pickTable" border :data="signResults" height="300">
                  <el-table-column label="序号" type="index" width="60">
                    <template #default="{ $index }">
                      {{ $index + 1 }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="载频"
                    sortable
                    prop="centerRate"
                    :sort-by="['cf', 'bw']"
                  />
                  <el-table-column label="带宽" sortable prop="bandwidth" :sort-by="['bw', 'cf']" />
                  <el-table-column label="幅度" sortable prop="am" />
                  <!-- <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                      <el-button size="samll" @click="pick(row)">选取</el-button>
                    </template>
                  </el-table-column> -->
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="峰值表" name="second">
                <el-table border :data="maxList" height="300">
                  <el-table-column label="峰值" prop="num" width="60" />
                  <el-table-column label="频率" prop="rate" />
                  <el-table-column label="幅度" prop="am" />
                  <el-table-column label="频率差值" prop="ratediff" />
                  <el-table-column label="幅度差值" prop="amdiff" />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="参数估计">
                <el-table border :data="paramsRsp" height="300">
                  <el-table-column label="参数名" prop="label" width="200" />
                  <el-table-column label="参数值" prop="value" width="400" />
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
        <!-- 右边表单，先不动 -->
        <el-col class="form-main" :span="4">
          <right-form @update:chartData="updateChartData" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped lang="scss">
  .analyse-main {
    height: 100%;
    display: flex;
    flex-direction: column;
    .charts-body {
      flex: 1;
      .show-table {
        height: 100%;
        border-right: 1px solid grey;
      }
      .form-main {
        box-shadow: 0px 0px 1rem 0px rgba(82, 137, 136, 0.08);
        padding: 12px;
        .file-name {
          display: flex;
          align-items: center;
          font-size: 20px;
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          padding-bottom: 16px;
          white-space: nowrap;
        }
      }
    }
  }
  .file-wrapper {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }
</style>
