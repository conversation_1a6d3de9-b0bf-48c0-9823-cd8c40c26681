<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData'
  import useScanFormStore from '@/store/modules/form/scanForm'

  const fields = ref([
    {
      target: 'isITU',
      name: 'ITU参数测量',
      isCheckbox: true
    },
    {
      target: 'isSignalSort',
      name: '信号分选',
      isCheckbox: true
    }
  ])

  const emit = defineEmits(['operation'])
  const model = reactive(new ModelData(fields.value, 'scan'))
  const scanFormStore = useScanFormStore()

  watch(
    () => scanFormStore,
    () => {
      model.update()
    },
    { deep: true }
  )
</script>

<template>
  <data-form :fields="fields" :model="model" />
  <!-- <el-button style="width: 40%; margin-left: 10%" @click="ITUmeasure">ITU参数测量</el-button>
  <el-button style="width: 40%" @click="emit('operation')">信号分选</el-button> -->
</template>
