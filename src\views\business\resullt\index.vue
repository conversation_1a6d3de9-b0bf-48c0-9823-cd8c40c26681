<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="8em"
    >
      <el-form-item label="实验名称" prop="expName">
        <el-input
          v-model="queryParams.expName"
          placeholder="请输入实验名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="年份" prop="years">
        <el-input
          v-model="queryParams.years"
          placeholder="请输入年份"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:resullt:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:resullt:edit']"
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:resullt:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:resullt:export']"
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar v-model:show-search="showSearch" @queryTable="getList" />
    </el-row>
    <div class="fit-table">
      <el-table
        v-loading="loading"
        border
        stripe
        :data="resulltList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="实验名称" align="center" prop="expName" />
        <el-table-column label="实验人编码" align="center" prop="expUser" />
        <el-table-column label="虚拟资源" align="center" prop="virtualResources" />
        <el-table-column label="阶段成果" align="center" prop="stageResult" />
        <el-table-column label="实验成绩" align="center" prop="expScore" />
        <el-table-column label="实验阶段" align="center" prop="expPace" />
        <el-table-column label="年份" align="center" prop="years" />
        <el-table-column label="创建人" align="center" prop="creatBy" />
        <el-table-column label="创建时间" align="center" prop="creatTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.creatTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <el-button
              v-hasPermi="['business:resullt:edit']"
              type="text"
              icon="Edit"
              @click="handleUpdate(scope.row)"
            >
              修改
            </el-button>
            <el-button
              v-hasPermi="['business:resullt:remove']"
              type="text"
              icon="Delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改实验记录对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="8em">
        <el-form-item label="实验名称" prop="expName">
          <el-input v-model="form.expName" placeholder="请输入实验名称" />
        </el-form-item>
        <el-form-item label="实验人编码" prop="expUser">
          <el-input v-model="form.expUser" placeholder="请输入实验人编码" />
        </el-form-item>
        <el-form-item label="虚拟资源" prop="virtualResources">
          <el-input v-model="form.virtualResources" placeholder="请输入虚拟资源" />
        </el-form-item>
        <el-form-item label="阶段成果" prop="stageResult">
          <el-input v-model="form.stageResult" placeholder="请输入阶段成果" />
        </el-form-item>
        <el-form-item label="实验成绩" prop="expScore">
          <el-input v-model="form.expScore" placeholder="请输入实验成绩" />
        </el-form-item>
        <el-form-item label="实验阶段" prop="expPace">
          <el-input v-model="form.expPace" placeholder="请输入实验阶段" />
        </el-form-item>
        <el-form-item label="年份" prop="years">
          <el-input v-model="form.years" placeholder="请输入年份" />
        </el-form-item>
        <el-form-item label="创建人" prop="creatBy">
          <el-input v-model="form.creatBy" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="创建时间" prop="creatTime">
          <el-date-picker
            v-model="form.creatTime"
            clearable
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择创建时间"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import {
    listResullt,
    getResullt,
    delResullt,
    addResullt,
    updateResullt
  } from '@/api/business/resullt'

  export default {
    name: 'Resullt',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 实验记录表格数据
        resulltList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          expId: null,
          expName: null,
          expUser: null,
          virtualResources: null,
          stageResult: null,
          expScore: null,
          expPace: null,
          years: null,
          creatBy: null,
          creatTime: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {}
      }
    },
    created() {
      this.getList()
    },
    methods: {
      /** 查询实验记录列表 */
      getList() {
        this.loading = true
        listResullt(this.queryParams).then(response => {
          this.resulltList = response.rows
          this.total = response.total
          this.loading = false
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          objId: null,
          expId: null,
          expName: null,
          expUser: null,
          virtualResources: null,
          stageResult: null,
          expScore: null,
          expPace: null,
          years: null,
          creatBy: null,
          creatTime: null,
          updateBy: null,
          updateTime: null,
          remark: null
        }
        this.resetForm('form')
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryForm')
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.objId)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset()
        this.open = true
        this.title = '添加实验记录'
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset()
        const objId = row.objId || this.ids
        getResullt(objId).then(response => {
          this.form = response.data
          this.open = true
          this.title = '修改实验记录'
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.objId != null) {
              updateResullt(this.form).then(response => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
            } else {
              addResullt(this.form).then(response => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const objIds = row.objId || this.ids
        this.$modal
          .confirm('是否确认删除实验记录编号为"' + objIds + '"的数据项？')
          .then(function () {
            return delResullt(objIds)
          })
          .then(() => {
            this.getList()
            this.$modal.msgSuccess('删除成功')
          })
          .catch(() => {})
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download(
          'business/resullt/export',
          {
            ...this.queryParams
          },
          `resullt_${new Date().getTime()}.xlsx`
        )
      }
    }
  }
</script>
