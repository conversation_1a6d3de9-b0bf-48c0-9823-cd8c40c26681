module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: [
    // add more generic rulesets here, such as:
    'eslint:recommended',
    'plugin:vue/vue3-essential',
    'plugin:vue/vue3-recommended',
    'plugin:prettier/recommended'
    // "plugin:@typescript-eslint/recommended",
    // 'standard',
    // 'plugin:vue/recommended' // Use this if you are using Vue.js 2.x.
  ],
  parser: 'vue-eslint-parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    parser: '@typescript-eslint/parser'
  },
  plugins: ['vue'],
  rules: {
    'vue/v-on-event-hyphenation': 'off',
    'vue/multi-word-component-names': 'off',
    'vue/valid-define-props': 'off',
    'vue/singleline-html-element-content-newline': 'off',
    'vue/no-mutating-props': 'off',
    'no-undef': 0,
    'no-unused-vars': 0,
    'vue/max-attributes-per-line': [
      'warn',
      {
        singleline: {
          max: 3
        },
        multiline: {
          max: 1,
          allowFirstLine: false // 第一个属性是否可以与标签名在同一行
        }
      }
    ],
    'vue/require-default-prop': 'off'
  }
}
