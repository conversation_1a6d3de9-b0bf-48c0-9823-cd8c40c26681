<template>
  <!-- <data-form :fields="fields" :model="model" /> -->
  <el-button style="width: 40%; margin: 5% 0 0 4%" @click="getMspectrum(1)">1次方谱</el-button>
  <el-button style="width: 40%; margin: 5% 0 0 4%" @click="getMspectrum(2)">2次方谱</el-button>
  <el-button style="width: 40%; margin: 5% 0 0 4%" @click="getMspectrum(4)">4次方谱</el-button>
  <el-button style="width: 40%; margin: 5% 0 0 4%" @click="getMspectrum(8)">8次方谱</el-button>
  <el-button style="width: 40%; margin: 5% 0 0 4%" @click="getFSK">FSK波特率估计</el-button>
  <el-button style="width: 40%; margin: 5% 0 0 4%" @click="getPSK">PSK波特率估计</el-button>
  <el-button style="width: 40%; margin: 5% 0 0 4%" @click="signAnalyse">信号分析</el-button>
  <el-button style="width: 40%; margin: 5% 0 0 4%" @click="signDemodulate">信号解调</el-button>
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData'
  import useScanFormStore from '@/store/modules/form/scanForm'
  import useChartsStore from '@/store/modules/charts'
  import { postFSKEstimate, postPSKEstimate, postMspectrum } from '@/api/charts'
  import { cloneDeep, round } from 'lodash'
  import { ElMessage, ElMessageBox } from 'element-plus'

  const emit = defineEmits(['update:chartData'])
  const router = useRouter()
  const scanFormStore = useScanFormStore()
  const chartsStore = useChartsStore()
  // const fields = ref([
  //   {
  //     target: 'startPoint',
  //     name: '起始点',
  //     int: true,
  //     change (val) {
  //       scanFormStore.setRateByPoint()
  //       scanFormStore.setCfAndBwByRate()
  //     }
  //   },
  //   {
  //     target: 'endPoint',
  //     name: '终止点',
  //     int: true,
  //     change (val) {
  //       scanFormStore.setRateByPoint()
  //       scanFormStore.setCfAndBwByRate()
  //     }
  //   },
  //   {
  //     target: 'len',
  //     name: '长度',
  //     disabled: true,
  //     int: true
  //   },
  // ])
  // const model = reactive(new ModelData(fields.value, 'scan'))
  const setFilePoint = () => {
    chartsStore.file.startPoint = scanFormStore.startPoint
    chartsStore.file.endPoint = scanFormStore.endPoint
  }
  const signAnalyse = () => {
    setFilePoint()
    router.push({
      name: 'ZeroSpan'
    })
  }
  const signDemodulate = () => {
    setFilePoint()
    router.push({
      name: 'DigitalModulation'
    })
  }
  const getMspectrum = val => {
    const body = scanFormStore.getFileParams()
    const value = round(scanFormStore.endPoint) - round(scanFormStore.startPoint) + 1
    body.m_in = val
    postMspectrum(body).then(res => {
      if (res.code === 200) {
        chartsStore.setSperctum(res.data)
        emit('update:chartData', res.data)
        scanFormStore.capital = true
      }
    })
  }
  const getFSK = () => {
    const body = scanFormStore.getFileParams()
    const value = round(scanFormStore.endPoint) - round(scanFormStore.startPoint) + 1
    body.value = value
    postFSKEstimate(body).then(res => {
      ElMessageBox.alert(`${res.msg}, 分析结果：${res.data}`, '提示', {
        confirmButtonText: '确定'
      })
    })
  }
  const getPSK = () => {
    const body = scanFormStore.getFileParams()
    const value = round(scanFormStore.endPoint) - round(scanFormStore.startPoint) + 1
    body.value = value
    postPSKEstimate(body).then(res => {
      ElMessageBox.alert(`${res.msg}, 分析结果：${res.data}`, '提示', {
        confirmButtonText: '确定'
      })
    })
  }

  // watch(
  //   () => scanFormStore,
  //   () => {
  //     model.update()
  //   },
  //   { deep: true }
  // )
</script>
