.online-container {
  --el-border-color: var(--play-bar-active);
  .el-button {
    background-color: var(--main-color);
    border: 1px solid var(--main-border-color);
    // width: 70px;
    height: 22px;
    border-radius: 0;
    color: var(--scan-text-color);
    &:hover {
      background-color: var(--main-color);
      color: var(--scan-text-color);
    }
    &:focus {
      background-color: var(--main-color);
      color: var(--scan-text-color);
    }
  }
  .el-checkbox {
    height: 22px;
    margin-right: 2px;
    &.is-bordered {
      border-radius: 0;
      padding: 0;
      border: 1px solid var(--main-border-color);
      &.is-checked {
        border: 1px solid var(--main-border-color);
        .el-checkbox__label {
          color: var(--main-color) !important;
        }
      }
    }
    .el-checkbox__input {
      height: 19px;
      width: 19px;
      &.is-checked {
        .el-checkbox__inner {
          background: url(@/assets/icons/svg/scanchecked.svg) var(--main-color) center center no-repeat;
          &::after {
            display: none;
          }
        }
      }
      .el-checkbox__inner {
        height: 19px;
        width: 19px;
        margin-left: 1px;
        background-color: var(--scan-text-color);
      }
      &:not(.is-checked) .el-checkbox__inner:hover {
        background: url(@/assets/icons/svg/scancheckedhover.svg) center center #fff no-repeat;
      }
    }
    .el-checkbox__label {
      padding: 0 4px;
    }
  }
  .el-radio {
    --el-color-primary: var(--scan-text-color);
    .el-radio__input .el-radio__inner {
      background-color: var(--main-border-color);
      width: 19px;
      height: 19px;
      border: 2px solid var(--main-color);
      opacity: 0.8;
      &::after {
        width: 10px;
        height: 10px;
        background-color: var(--main-color);
      }
    }
    .el-radio__input.is-checked .el-radio__inner {
      opacity: 1;
    }
  }
  .el-select {
    .select-trigger[aria-describedby] {
      background-color: var(--main-color);
    }
    .el-input__wrapper {
      background: transparent;
      height: 22px;
      border: 1px solid var(--main-border-color);
      color: var(--scan-text-color);
      .el-input__inner::placeholder {
        color: #fff;
      }
    }
    .el-input__suffix-inner {
      .el-icon {
        color: #fff;
      }
    }
    .el-select__popper {
      margin-top: -10px;
      background-color: var(--list-color);
      .el-select-dropdown__item {
        background-color: transparent;
        color: var(--scan-text-color);
        &.selected {
          background-color: var(--main-color);
          color: var(--scan-text-color);
          &::after {
            background-color: var(--scan-text-color);
          }
          &.hover {
            background-color: var(--main-color);
          }
        }
        &:hover {
          background-color: var(--main-color);
        }
      }
    }
    .el-popper__arrow {
      display: none;
    }
    .el-tag {
      background: var(--toolbar-color);
      color: var(--scan-text-color);
      height: 19px;
    }
  }
  .highcharts-reset-zoom {
    display: none;
  }
  .el-tabs {
    .el-tabs__nav-scroll {
      border-bottom: 2px solid var(--main-color);
      padding: 4px 16px;
    }
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .tabs-button {
      width: 140px;
      height: 30px;
      background: linear-gradient(90deg, rgba(2, 125, 180, 1) 20%, rgba(0, 0, 0, 1) 102%);
      border: none;
      border-radius: 5px;
      box-shadow: 3px 3px 3px rgba(128, 255, 255, 0.349019607843137);
      opacity: 0.5;
    }
    .is-active {
      .tabs-button {
        opacity: 1;
      }
    }
    .el-tabs__item {
      padding: 0 2px;
    }
    .el-tabs__content {
      padding: 4px;
    }
    .el-table {
      background: transparent;
      --el-table-border: 1px solid #555555;
      --el-table-border-color: #555555;
      --el-border-color: #555555;
      --el-table-row-hover-bg-color: var(--list-color);
      --el-table-tr-bg-color: transparent;
      tr {
        background: transparent;
        th.el-table__cell {
          background: url(@/assets/images/tabletrbg.png) no-repeat;
          background-size: 100% 100%;
          font-weight: 500;
          color: var(--scan-text-color);
        }
        td.el-table__cell {
          color: var(--scan-text-color);
          background-color: var(--table-bg);
          &.el-table_1_column_1 {
            background-color: var(--table-index-bg);
          }
          &:last-of-type {
            padding: 0;
            .cell {
              height: 38px;
              padding: 0;
              line-height: 38px;
            }
          }
          .el-button.cover {
            background: url(@/assets/images/tableopbtn.png) no-repeat;
            background-size: cover;
            width: 100%;
            height: 100%;
            border: none;
          }
        }
      }
    }
    &.el-tabs--left {
      .is-left {
        margin-right: 0;
      }
      .el-tabs__nav-scroll {
        border-bottom: none;
        padding: 4px 0 4px 20px;
      }
      .el-tabs__active-bar {
        display: none;
      }
      .el-tabs__item {
        justify-content: flex-start;
        margin: 10px 0;
        padding-left: 0 !important;
      }
    }
  }
  .ctl-btn {
    width: 140px;
    height: 40px;
    background: linear-gradient(142.835deg, rgba(2, 167, 240, 1) 45%, rgba(1, 84, 120, 1) 69%);
    border: none;
    border-radius: 5px;
    box-shadow: 3px 3px 5px rgba(0, 255, 255, 0.349);
    font-family: "微软雅黑 Bold", "微软雅黑 Regular", "微软雅黑";
    font-weight: 700;
    font-style: normal;
    font-size: 16px;
    &.close {
      background: linear-gradient(-56deg, rgba(170, 170, 170, 1) 55%, rgba(85, 85, 85, 1) 70%);
    }
  }
  .el-input.common-input {
    padding: 0 !important;
    --el-input-focus-border-color: var(--play-bar-active);
    --el-input-border-color: var(--play-bar-active);
    --el-select-input-focus-border-color: var(--play-bar-active);
    --el-input-hover-border-color: var(--play-bar-active);
    .el-input__wrapper {
      height: 30px;
      border-radius: 0;
      background-color: var(--scan-bg);
      border-color: transparent;
      border: none;
    }
    width: 300px;
    height: 31px;
    padding: 3px 2px 3px 2px;
    font-family: "Arial Normal", "Arial";
    font-weight: 400;
    font-style: normal;
    font-size: 13px;
    letter-spacing: normal;
    color: var(--scan-text-color);
    vertical-align: none;
    text-align: left;
    text-transform: none;
    border-radius: 0;
    .el-input-group__append {
      padding: 0;
      .el-select {
        width: 100%;
      }
    }
  }
  .el-form {
    .el-form-item {
      margin-bottom: 10px;
      .el-form-item__label {
        width: 120px;
        color: var(--scan-text-color);
        font-weight: 400;
        justify-content: flex-start;
      }
      .el-input {
        padding: 0 !important;
        --el-input-focus-border-color: var(--main-border-color);
        --el-input-border-color: var(--main-border-color);
        --el-select-input-focus-border-color: var(--main-border-color);
        --el-input-hover-border-color: var(--main-border-color);
        --el-text-color-regular: var(--scan-text-color);
        .el-input__wrapper {
          height: 30px;
          border-radius: 0;
          background-color: var(--table-bg);
          border-color: transparent;
          border: none;
          input {
            height: 28px;
            line-height: 28px;
          }
        }
        font-family: "Arial Normal", "Arial";
        font-weight: 400;
        font-style: normal;
        font-size: 13px;
        letter-spacing: normal;
        color: var(--scan-text-color);
        vertical-align: none;
        text-align: left;
        text-transform: none;
        border-radius: 0;
        .el-input-group__append {
          padding: 0;
          line-height: 30px;
          color: var(--scan-text-color);
          background-color: transparent;
          .el-select {
            width: 75px;
            margin: 0;
          }
        }
      }
    }
  }
  /* input 类型为 number 时去掉数字加减上下箭头 */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }

  input[type="number"] {
    -moz-appearance: textfield; /* 此处写不写都可以 */
  }
}
.el-popper {
  --el-bg-color-overlay: var(--list-color);
  --el-text-color-regular: var(--scan-text-color);
  --el-fill-color-light: var(--main-color);
  --el-color-primary: var(--scan-text-color);
}
.ucnav-item .avatar-container {
  height: 100%;
  .avatar-wrapper {
    color: #fff;
    font-size: 16px;
    line-height: 72px;
  }
}
