import request from '@/utils/request'

// 查询课程章节信息列表
export function listChapter(query) {
  return request({
    url: '/business/chapter/list',
    method: 'get',
    params: query
  })
}

// 查询课程章节信息详细
export function getChapter(chapterId) {
  return request({
    url: '/business/chapter/' + chapterId,
    method: 'get'
  })
}

// 新增课程章节信息
export function addChapter(data) {
  return request({
    url: '/business/chapter',
    method: 'post',
    data: data
  })
}

// 修改课程章节信息
export function updateChapter(data) {
  return request({
    url: '/business/chapter',
    method: 'put',
    data: data
  })
}

// 删除课程章节信息
export function delChapter(chapterId) {
  return request({
    url: '/business/chapter/' + chapterId,
    method: 'delete'
  })
}
