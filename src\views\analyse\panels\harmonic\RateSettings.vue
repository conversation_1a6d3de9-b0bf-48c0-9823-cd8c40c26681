<template>
  <data-form :model="model" :fields="fields" />
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData'

  const rateOptions = ['GHz', 'MHz', 'kHz', 'Hz']

  const fields = [
    { target: 'centerFreqIn', name: '中心频率', options: rateOptions, appendSelect: true },
    { target: 'step', name: '步进', options: rateOptions, appendSelect: true },
    { target: 'span', name: '扫宽', options: rateOptions, appendSelect: true },
    { target: 'rbw', name: 'RBW', options: rateOptions, appendSelect: true },
    { target: 'vbw', name: 'VBW', options: rateOptions, appendSelect: true }
  ]

  const model = reactive(new ModelData(fields, 'harmonic'))
</script>
