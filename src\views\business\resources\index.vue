<template>
  <div class="app-container">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      style="height: 60%"
      @row-click="rowClick"
      @selectionChange="rows => (selection = rows)"
    />
    <table-form v-bind="tableFormConfigrepairs" ref="refsTableFormrepairs" style="height: 40%" />
    <!-- <div style="height: 400px">asdasdasd</div> -->
    <!-- 虚拟机详情 -->
    <ResDetailInfo ref="refsMtrepairs" @actSuccess="getList()" />
    <!-- 设置密码 -->
    <ResPwd ref="setPwdRef" @actSuccess="getList()" />
    <!-- 设置班级 -->
    <ResClazz ref="setClazzRef" @actSuccess="getList()" />
    <!-- 设置学生 -->
    <ResStu ref="setStuRef" @actSuccess="getList()" />
    <!-- 报修 -->
    <ResWx ref="setWxRef" @actSuccess="getList()" />
    <!-- 导入 -->
    <ResImport ref="setImportRef" @actSuccess="getList()" />
    <mtDetailInfo ref="refsMt" @actSuccess="getList()" />
  </div>
</template>

<script>
  import * as mtApirepairs from '@/api/business/fault'
  import * as resApi from '@/api/business/resources'
  import * as mtApi from '@/api/business/resources'
  import ResDetailInfo from './detail.vue'
  import ResPwd from './setPwd.vue'
  import mtDetailInfo from './tmpInfo.vue'
  import ResClazz from './setClazz.vue'
  import ResImport from './setImport.vue'
  import ResStu from './setStu.vue'
  import ResWx from './setWx.vue'
  export default {
    name: 'ResourceList', // 开启分享参数模板缓存
    components: { ResDetailInfo, ResPwd, ResImport, ResClazz, ResStu, ResWx, mtDetailInfo },
    data() {
      return {
        selection: [],
        tableFormConfig: {
          title: '虚拟机资源',
          searchConfig: {
            labelWidth: '8em',
            formConfig: [
              { title: '虚拟机IP', name: 'ip' },
              { title: '虚拟机名称', name: 'name' }
            ],
            loadData: p => {
              return mtApi.listResources(p)
            }
          },
          actions: [
            // {
            //   label: '新增',
            //   type: 'primary',
            //   icon: 'el_Plus',
            //   permission: ['business:resources:add'],
            //   click: () => {
            //     this.$refs.refsMtrepairs.show()
            //   }
            // },
            {
              label: '虚拟机更新',
              icon: 'el_Top',
              permission: ['business:resources:setimport'],
              click: () => {
                resApi.importResources({}).then(response => {
                  this.$modal.msgSuccess('操作成功')
                  this.getList()
                })
              }
              // this.$refs.setImportRef.show(() => {
              //   this.getList()
              // })
            },
            {
              label: '密码设置',
              icon: 'el_Key',
              disabled: () => !this.selection.length,
              permission: ['business:resources:setlogin'],
              click: () =>
                this.$refs.setPwdRef.show(this.selection.map(v => v.id).join(), () => {
                  this.getList()
                })
            },
            {
              label: '分配期班',
              icon: 'el_Avatar',
              disabled: () => !this.selection.length,
              permission: ['business:resources:setclazz'],
              click: () =>
                // rows.map(v => v.resId)
                this.$refs.setClazzRef.show(this.selection.map(v => v.resId).join(), () => {
                  this.getList()
                })
            },
            {
              label: '分配学生',
              icon: 'el_User',
              permission: ['business:resources:setstudent'],
              disabled: () => !this.selection.length,
              click: () =>
                this.$refs.setStuRef.show(this.selection.map(v => v.resId).join(), () => {
                  this.getList()
                })
            },
            {
              label: '报修',
              icon: 'el_Tools',
              disabled: () => !this.selection.length,
              permission: ['business:resources:setwx'],
              click: () =>
                this.$refs.setWxRef.show(this.selection.map(v => v.resId).join(), () => {
                  this.getList()
                })
            },
            // {
            //   label: '导入',
            //   icon: 'el_Top',
            //   permission: ['business:resources:setimport'],
            //   click: () =>
            //     this.$refs.setImportRef.show(() => {
            //       this.getList()
            //     })
            // },
            {
              label: '导出',
              icon: 'el_upload',
              type: 'warning',
              permission: ['business:resources:export'],
              click: () =>
                this.download(
                  'business/resources/export',
                  {},
                  `虚拟机资源_${new Date().getTime()}.xlsx`
                )
            },
            {
              label: '删除',
              type: 'danger',
              isPlain: true,
              disabled: () => !this.selection.length,
              icon: 'cus_delete',
              click: () =>
                this.act(
                  this.selection.map(v => v.resId),
                  '删除',
                  'delResources'
                )
            }
          ],
          tableConfig: {
            isChecked: true,
            cols: [
              { label: '虚拟机IP', prop: 'ip' },
              { label: '虚拟机名称', prop: 'name', minWidth: 200 },
              // { label: 'UUID', prop: 'uuid', minWidth: 200 },
              { label: '所属期班', renderFun: (h, { row }) => h('span', row.srClazz?.claName) },
              { label: '所属学生', renderFun: (h, { row }) => h('span', row.srStudents?.stuName) },
              // { label: '主机名称', prop: 'hostName', minWidth: 250 },
              { label: '主机编号', prop: 'hostId', width: 170 },
              { label: 'cup核数', prop: 'vcpu', width: 170 },
              { label: 'cpu频率', prop: 'cpuRate', width: 170 },
              { label: '内存', prop: 'memory', width: 170 },
              { label: '磁盘大小', prop: 'diskSize', width: 170 },
              { label: '系统盘大小', prop: 'sysDiskSize', width: 170 },
              // { label: '操作系统', prop: 'osType', width: 170 },

              { label: '分配状态', prop: 'resStatus', type: 'dict', dictName: 'res_vm_status' },
              // { label: '是否加密', prop: 'isEncrypt', type: 'dict', dictName: 'sys_yes_no' },
              {
                label: '报修状态',
                prop: 'repairStatus',
                type: 'dict',
                dictName: 'vm_repair_status'
              },
              {
                label: '机器状态',
                prop: 'status',
                type: 'dict',
                dictName: 'vm_status',
                fixed: 'right',
                width: '100'
              },
              {
                label: '操作',
                type: 'action',
                width: '160',
                actions: [
                  {
                    icon: 'el_Edit',
                    title: '编辑',
                    permission: ['business:resources:edit'],
                    click: row => this.$refs.refsMtrepairs.show(row.resId, 'edit')
                  },
                  {
                    title: '重启',
                    icon: 'el_SwitchButton',
                    permission: ['business:resources:restart'],
                    disabled: row =>
                      row.status == 0 || row.status !== 1 || row.status !== 3 || row.status !== 2,
                    click: row => this.act(row.id, '重启', 'restartResources')
                  },
                  {
                    title: '休眠',
                    icon: 'el_WarnTriangleFilled',
                    permission: ['business:resources:relax'],
                    disabled: row => row.status == 0 || row.status !== 1 || row.status !== 2,
                    click: row => this.act(row.id, '休眠', 'relaxResources')
                  },
                  {
                    title: '关闭',
                    icon: 'el_CircleClose',
                    permission: ['business:resources:close'],
                    disabled: row => row.status == 1 || row.status !== 0 || row.status !== 3,
                    click: row => this.act(row.id, '关闭', 'closeResources')
                  },
                  {
                    title: '删除',
                    icon: 'el_Delete',
                    permission: ['business:resources:remove'],
                    click: row => this.act(row.resId, '删除', 'delResources')
                  }
                ]
              }
            ]
          },
          tableConfigSublevel: {
            isChecked: true,
            cols: [
              {
                label: '报修资源IP',
                prop: 'repairResIp'
              },
              { label: '资源名称', prop: 'repairResName', minWidth: 200 },
              { label: '报修人', prop: 'repairUserName' },
              {
                label: '故障描述',
                prop: 'repairBiz',
                renderFun: (h, { row }) => {
                  return h('span', {
                    innerHTML: row.repairBiz
                  })
                }
              },
              {
                label: '故障原因',
                prop: 'repairReason',
                showAll: false,
                width: 90,
                renderFun: (h, { row }) => {
                  return h('span', {
                    innerHTML: row.repairReason
                  })
                }
              },
              { label: '报修时间', prop: 'repairTime' },
              { label: '计划修理时间', prop: 'repairPlanTime' },
              { label: '修复完成时间', prop: 'repairDoneTime' },
              {
                label: '报修状态',
                prop: 'repairStatus',
                type: 'dict',
                dictName: 'vm_repair_status'
              },
              { label: '备注', prop: 'remark', width: 300 },
              {
                label: '操作',
                type: 'action',
                width: '100',
                actions: [
                  {
                    title: '报修处理',
                    icon: 'el_Edit',
                    permission: ['business:fault:edit'],
                    click: row => this.$refs.refsMt.show(row.faultId)
                  },
                  {
                    title: '删除',
                    icon: 'el_Delete',
                    permission: ['business:fault:remove'],
                    click: row => this.deleteAll(row.faultId)
                  }
                ]
              }
            ]
          }
        },
        tableFormConfigrepairs: {
          title: '报修处理',
          searchConfig: {
            labelWidth: '8em',
            hideBtn: true,
            formConfig: [],
            loadData: p => {
              return mtApirepairs.listFault(p)
            }
          },
          actions: [],
          tableConfig: {
            isChecked: true,
            cols: [
              {
                label: '报修资源IP',
                prop: 'repairResIp'
              },
              { label: '资源名称', prop: 'repairResName', minWidth: 200 },
              { label: '报修人', prop: 'repairUserName' },
              {
                label: '故障描述',
                prop: 'repairBiz'
                // renderFun: (h, { row }) => {
                //   return h('span', {
                //     innerHTML: row.repairBiz
                //   })
                // }
              },
              {
                label: '故障原因',
                prop: 'repairReason'
                // showAll: false,
                // width: 300,
                // renderFun: (h, { row }) => {
                //   return h('span', {
                //     innerHTML: row.repairReason
                //   })
                // }
              },
              { label: '报修时间', prop: 'repairTime' },
              { label: '计划修理时间', prop: 'repairPlanTime' },
              { label: '修复完成时间', prop: 'repairDoneTime' },
              {
                label: '报修状态',
                prop: 'repairStatus',
                type: 'dict',
                dictName: 'vm_repair_status'
              },
              { label: '备注', prop: 'remark', width: 300 },
              {
                label: '操作',
                type: 'action',
                width: '100',
                actions: [
                  {
                    title: '报修处理',
                    icon: 'el_Edit',
                    permission: ['business:fault:edit'],
                    click: row => this.$refs.refsMt.show(row.faultId)
                  },
                  {
                    title: '删除',
                    icon: 'el_Delete',
                    permission: ['business:fault:remove'],
                    click: row => this.deleteAll(row.faultId)
                  }
                ]
              }
            ]
          },
          tableConfigSublevel: {
            isChecked: true,
            cols: [
              {
                label: '报修资源IP',
                prop: 'repairResIp'
              },
              { label: '资源名称', prop: 'repairResName', minWidth: 200 },
              { label: '报修人', prop: 'repairUserName' },
              {
                label: '故障描述',
                prop: 'repairBiz',
                renderFun: (h, { row }) => {
                  return h('span', {
                    innerHTML: row.repairBiz
                  })
                }
              },
              {
                label: '故障原因',
                prop: 'repairReason',
                showAll: false,
                width: 90,
                renderFun: (h, { row }) => {
                  return h('span', {
                    innerHTML: row.repairReason
                  })
                }
              },
              { label: '报修时间', prop: 'repairTime' },
              { label: '计划修理时间', prop: 'repairPlanTime' },
              { label: '修复完成时间', prop: 'repairDoneTime' },
              {
                label: '报修状态',
                prop: 'repairStatus',
                type: 'dict',
                dictName: 'vm_repair_status'
              },
              { label: '备注', prop: 'remark', width: 300 },
              {
                label: '操作',
                type: 'action',
                width: '100',
                actions: [
                  {
                    title: '报修处理',
                    icon: 'el_Edit',
                    permission: ['business:fault:edit'],
                    click: row => this.$refs.refsMtrepairs.show(row.faultId)
                  },
                  {
                    title: '删除',
                    icon: 'el_Delete',
                    permission: ['business:fault:remove'],
                    click: row => this.deleteAll(row.faultId)
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList() {
        this.$refs.refsTableForm.loadData()
      },
      act(row, title, actName) {
        this.$modal
          .confirm('是否确认' + title + '所选虚拟机资源？')
          .then(function () {
            return mtApi[actName](row)
          })
          .then(() => {
            this.getList()
            this.$modal.msgSuccess('操作成功')
          })
      },
      rowClick(arr) {
        this.$refs.refsTableFormrepairs.loadData({ pageNum: 1, pageSize: 20, repairResIp: arr.ip })
      },
      //批量删除模板
      deleteAll(id = null) {
        if (!this.selection.length && !id) return this.$modal.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选保修单吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.faultId)
          mtApirepairs.delFault(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.$refs.refsTableFormrepairs.loadData()
          })
        })
      }
    }
  }
</script>
