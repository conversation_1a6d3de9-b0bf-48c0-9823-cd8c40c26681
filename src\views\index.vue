<!-- 首页 -->
<template>
  <div class="online-container app-container">
    <!-- 主机 -->
    <el-row :gutter="20">
      <el-col v-for="item in hostList" :key="item.id" class="chart-form" :span="12">
        <chartCom
          :usekey="'hostSpectrum'"
          :device-id="item.id"
          :data-list="getDeviceData(item)"
          :model="hostSpectrum"
          :enum-list="enumList"
          :status="hostStatus"
          :device-data="item"
          @start="startWebsocket"
          @stop="closeScan"
          @initEquip="getList"
        />
      </el-col>
    </el-row>
    <!-- 子机 -->
    <el-row v-if="handsetList" :gutter="20">
      <el-col v-for="(item, index) in handsetList" :key="item.id" class="chart-form" :span="12">
        <chartCom
          :usekey="'handsetSpectrum' + (index + 1)"
          :device-id="item.id"
          :data-list="getDeviceData(item)"
          :model="handsetSpectrumList[index]"
          :enum-list="enumList"
          :status="handsetStatusList[index]"
          :device-data="item"
          @start="startWebsocket"
          @stop="closeScan"
          @initEquip="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script setup>
  import useScanStore from '@/store/modules/scanMonitor'
  import useLinkWsHook from '@/common/hooks/linkWsDynamic'
  import chartCom from '@/components/chartCom/index.vue'
  import { getEquipList } from '@/api/system/equipment'
  import { SCAN_CODE, DISPERSED_CODE } from '@/constant/funCodes'
  import { spectrumType, webSocketStatus } from '@/utils/enum'

  /** 枚举值获取 */
  const { proxy } = getCurrentInstance()
  // 字典项
  const {
    measure_mode,
    polarization_type,
    detection_mode,
    attenuation_mode,
    gain_mode,
    threshold_type,
    frequency_step
  } = proxy.useDict(
    'measure_mode',
    'polarization_type',
    'detection_mode',
    'attenuation_mode',
    'gain_mode',
    'threshold_type',
    'frequency_step'
  )
  // 字典枚举
  const enumList = {
    measure_mode,
    polarization_type,
    detection_mode,
    attenuation_mode,
    gain_mode,
    threshold_type,
    frequency_step
  }

  const hostList = ref([]) // 主机数据
  const handsetList = ref([]) // 子机数据
  // 查询参数
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10
  })

  const scanStore = useScanStore()

  const { hostSpectrum, handsetSpectrum1, handsetSpectrum2 } = scanStore // 存储在store中的频谱数据主机、子机1、子机2
  const hostStatus = computed(() => hostSpectrum.status) //计算主机的ws状态
  //计算子机的状态
  const handsetStatusList = computed(() => {
    return [handsetSpectrum1.status, handsetSpectrum2.status]
  })
  const handsetSpectrumList = computed(() => {
    return [handsetSpectrum1, handsetSpectrum2]
  })

  const idMapData = ref({})

  // 动态链接ws
  const { linkScan, closeScan, pageData } = useLinkWsHook()

  /**
   * 获取设备数据
   * @param deviceData 设备信息
   * @returns 返回一个计算属性，包含当前幅度值、平均值、最大值、最小值、门限值和占用率等信息的对象
   */
  const getDeviceData = deviceData => {
    return computed(() => {
      const devicePageData = pageData.value[deviceData.id] || {}
      let initData = {
        current: [], // 幅度值
        average: [], // 平均值
        max: [], // 最大值
        min: [], // 最小值
        limit: [], // 门限值
        occupancy: []
      }
      // 如果没有数据，则返回空数据
      if (!devicePageData) {
        return initData
      }
      if (deviceData.deviceScan?.type === spectrumType.CONTINUOUS) {
        const data = devicePageData.subtaskResultBody
        if (!data) {
          return initData
        }
        data.frequencyListFromSrList.forEach(item => {
          initData.current.push(item.level)
          initData.average.push(item.average)
          initData.max.push(item.maximum)
          initData.min.push(item.minimum)
          initData.limit.push(item.threshold)
          initData.occupancy.push(item.occupancy)
        })
      } else if (deviceData.deviceScan?.type === spectrumType.DISCRETE) {
        const data = devicePageData.discreteChannelResult?.frequencyListFromDcList
        if (!data) {
          return initData
        }
        data.sort((a, b) => a.frequency - b.frequency)
        data.forEach(item => {
          initData.current.push([item.frequency, item.fieldStrength])
          initData.average.push([item.frequency, item.average])
          initData.max.push([item.frequency, item.maximum])
          initData.min.push([item.frequency, item.minimum])
          initData.limit.push([item.frequency, item.fieldStrength])
          initData.occupancy.push([item.frequency, item.occupancy])
        })
      }
      return initData
    })
  }

  // 获取设备列表,渲染主机/子机
  const getList = async () => {
    handsetList.value = []
    hostList.value = []
    await getEquipList(queryParams.value).then(response => {
      response.data.list.forEach((item, index) => {
        if (item.type === 1) {
          handsetList.value.push(item)
          idMapData.value[item.id] = handsetSpectrumList[index]
        } else {
          hostList.value.push(item)
          idMapData.value[item.id] = hostSpectrum
        }
      })
    })
  }

  /**
   * 生成参数对象
   * @param deviceData 设备数据
   * @returns 返回参数对象
   */
  const generateParams = deviceData => {
    // 频谱扫描
    if (deviceData.deviceScan.type === spectrumType.CONTINUOUS) {
      return {
        dataType: 1,
        // thresholdType: deviceData.devicePara.thresholdType,
        // threshold: deviceData.devicePara.threshold,
        thresholdType: deviceData.deviceScan.thresholdType,
        threshold: deviceData.deviceScan.threshold,
        terminationDirection: 90,
        azimuthStep: 10,
        polarization: parseInt(deviceData.devicePara.polarizationType, 16),
        startFrequency: deviceData.deviceScan.startFreq,
        endFrequency: deviceData.deviceScan.endFreq,
        frequencyStep: deviceData.deviceScan.step
      }
    }
    // 离散扫描
    else if (deviceData.deviceScan.type === spectrumType.DISCRETE) {
      const discrete = deviceData.deviceScan.freqBandStr.split(',').map(item => {
        const [centerFreq, bandwidth] = item.split('|')
        return {
          frequency: Number(centerFreq),
          analysisBandwidth: Number(bandwidth)
        }
      })
      return {
        dataType: 1,
        // thresholdType: deviceData.devicePara.thresholdType,
        // threshold: deviceData.devicePara.threshold,
        thresholdType: deviceData.deviceScan.thresholdType,
        threshold: deviceData.deviceScan.threshold,
        polarization: parseInt(deviceData.devicePara.polarizationType, 16),
        discrete: discrete,
        frequencyCount: discrete.length,
        analyseBandwidth: deviceData.deviceScan.bandwidth
      }
    }
  }

  /**
   * 开启Websocket连接
   * @param deviceData 设备数据
   */
  const startWebsocket = data => {
    const params = generateParams(data.deviceData)
    scanStore.setSpectrumData(data.key, {
      centerFreq: data.deviceData.deviceScan.centerFreq,
      bandwidth: data.deviceData.deviceScan.bandwidth,
      startFreq: data.deviceData.deviceScan.startFreq,
      endFreq: data.deviceData.deviceScan.endFreq,
      step: data.deviceData.deviceScan.step.toString(),
      freqSectionNum: data.deviceData.deviceScan.freqSectionNum,
      freqBandStr: data.deviceData.deviceScan.freqBandStr,
      type: data.deviceData.deviceScan.type
    })
    linkScan(
      params,
      data.deviceData.deviceScan.type ? DISPERSED_CODE : SCAN_CODE,
      data.deviceData.ip,
      data.deviceData.port,
      data.spectrum,
      data.deviceData.id,
      data.deviceData.code
    )
  }

  onMounted(() => {
    getList()
  })
</script>

<style scoped lang="scss">
  @import '@/assets/styles/mixin.scss';

  .online-container {
    @include boxBg('@/assets/images/boxBg.png');
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 1rem;
    overflow-y: auto;
    .chart-form {
      margin: 0 0 60px 0;
      min-width: 789px;
      height: 520px;
      .chart-title {
        display: flex;
        align-items: center;
        color: #fff;
        height: 40px;
        line-height: 40px;
        div {
          margin: 0 10px;
        }
        .equipType {
          width: 160px;
          text-align: center;
          font-size: 20px;
          font-weight: 700;
          position: relative;
          &::after {
            content: '';
            display: inline-block;
            position: absolute;
            right: 20%; /* 调整这个值来改变圆点的水平位置 */
            top: 50%; /* 调整这个值来改变圆点的垂直位置 */
            transform: translate(50%, -50%); /* 使圆点居中 */
            width: 25px;
            height: 25px;
            margin-left: 5px;
            border-radius: 50%;
            background-color: var(--dot-color); /* 使用 CSS 变量来设置颜色 */
          }
        }
        .inputClass {
          width: 50px;
          height: 25px;
          --el-input-focus-border-color: var(--main-border-color);
          --el-input-border-color: var(--main-border-color);
          --el-select-input-focus-border-color: var(--main-border-color);
          --el-input-hover-border-color: var(--main-border-color);
          --el-text-color-regular: var(--scan-text-color);
        }
        .protClass {
          width: 80px;
        }
        .saveBtn {
          width: 60px;
          height: 25px;
          background: linear-gradient(
            142.835deg,
            rgba(2, 167, 240, 1) 45%,
            rgba(1, 84, 120, 1) 69%
          );
          border: none;
          border-radius: 5px;
          box-shadow: 3px 3px 5px rgba(0, 255, 255, 0.349);
          font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑';
          font-weight: 700;
          font-style: normal;
          font-size: 14px;
        }
      }
    }
    .main {
      height: 100%;
    }
  }
</style>
