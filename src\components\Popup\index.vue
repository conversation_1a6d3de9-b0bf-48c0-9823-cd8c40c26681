<template>
  <div>
    <vxe-modal
      :model-value="message"
      :title="title"
      :min-height="660"
      :width="width"
      :height="height"
      position="center"
      @close="closeModal"
    >
      <template #default>
        <vxe-form :data="demo.formData">
          <vxe-form-item title="数据类型" field="fileType" span="18" class="title-name">
            <template #default="{ data }">
              <vxe-radio
                v-for="item in filetypeList"
                v-model="data.fileType"
                name="fileType"
                :label="item.value"
                :content="item.label"
              />
            </template>
          </vxe-form-item>
          <vxe-form-item title="中心频率" field="centerfregIn" :item-render="{}" span="18">
            <template #default="{ data }">
              <input
                v-model="data.centerfregIn"
                class="center-name"
                clearable
                maxlength="20"
                @input="
                  data.centerfregIn = data.centerfregIn.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="中频带宽"
            field="intermediateFrequencyBandwidth"
            :item-render="{}"
            span="18"
            class="title-name"
          >
            <template #default="{ data }">
              <input
                v-model="data.intermediateFrequencyBandwidth"
                class="center-name"
                maxlength="20"
                @input="
                  data.intermediateFrequencyBandwidth = data.intermediateFrequencyBandwidth.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="步长"
            field="steplen"
            :item-render="{}"
            span="18"
            class="title-name"
          >
            <template #default="{ data }">
              <input
                v-model="data.steplen"
                class="center-name"
                maxlength="20"
                @input="
                  data.steplen = data.steplen.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="采样率"
            field="samplerateIn"
            :item-render="{}"
            span="18"
            class="title-name"
          >
            <template #default="{ data }">
              <input
                v-model="data.samplerateIn"
                class="sampleratein-name"
                maxlength="20"
                @input="
                  data.samplerateIn = data.samplerateIn.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            v-if="demo.formData.fileType == 1"
            title="功率倍数"
            field="powerMultiple"
            :item-render="{}"
            span="18"
            class="title-name"
          >
            <template #default="{ data }">
              <input
                v-model="data.powerMultiple"
                class="sampleratein-name"
                maxlength="20"
                @input="
                  data.powerMultiple = data.powerMultiple.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="IQ数据起始字节"
            field="startOffset"
            :item-render="{}"
            span="18"
            class="title-name"
          >
            <template #default="{ data }">
              <input
                v-model="data.startOffset"
                class="sampleratein-name"
                maxlength="20"
                @input="
                  data.startOffset = data.startOffset.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="文件末尾截断字节数"
            field="CutoffLength"
            :item-render="{}"
            span="18"
            class="title-name"
          >
            <template #default="{ data }">
              <input
                v-model="data.CutoffLength"
                class="sampleratein-name"
                maxlength="20"
                @input="
                  data.CutoffLength = data.CutoffLength.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="调制样式"
            field="debugMode"
            :item-render="{}"
            span="18"
            placeholder="请选择调制样式"
            class="title-name"
          >
            <template #default="{ data }">
              <!-- <vxe-select
                v-model="data.debugMode"
                type="text"
                :options="debugModeList"
                transfer
                clearable
              /> -->
              <select v-model="data.debugMode" class="select-style" transfer clearable>
                <option
                  v-for="(item, index) in debugModeList"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                />
              </select>
            </template>
          </vxe-form-item>
          <vxe-form-item
            v-if="demo.formData.fileType == 0"
            title="IQ排列"
            field="IQpermutation"
            span="18"
            class="title-name"
          >
            <template #default="{ data }">
              <vxe-radio
                v-for="item in dataOrganizationList"
                v-model="data.IQpermutation"
                name="IQpermutation"
                :label="item.value"
                :content="item.label"
              />
            </template>
          </vxe-form-item>
          <vxe-form-item v-else title="频谱数据单位" field="unitType" span="18" class="title-name">
            <template #default="{ data }">
              <vxe-radio
                v-for="item in unitTypeList"
                v-model="data.unitType"
                name="unitType"
                :label="item.value"
                :content="item.label"
              />
            </template>
          </vxe-form-item>
          <vxe-form-item title="数据类型" field="dataType" span="18" class="title-name">
            <template #default="{ data }">
              <vxe-radio
                v-for="item in datatypeList"
                v-model="data.dataType"
                name="dataType"
                :label="item.value"
                :content="item.label"
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="IQ文件"
            field="IQfile"
            :item-render="{}"
            span="18"
            class="title-name"
          >
            <template #default="{ data }">
              <vxe-input v-model="data.IQfile" clearable class="file-name" />
            </template>
          </vxe-form-item>
        </vxe-form>
        <vxe-button status="success" content="浏览" class="browse-button" @click="clickEvent1" />
        <div class="flex justify-center items-center mt-3">
          <vxe-button status="info" content="取消" @click="closeModal" />
          <vxe-button status="primary" content="上传" @click="insertEvent(false)" />
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script setup>
  import Cookies from 'js-cookie'
  import { toRefs, ref, onMounted, watch } from 'vue'
  import { VXETable } from 'vxe-table'
  import useDictStore from '@/store/modules/dict'
  //分片上传
  import { upload } from '@/api/tool/request'
  import { fileChunkList, setFileChunkList, clearFileHash } from '@/api/tool/request/file'
  import { plotToNum } from '@/utils/utils'
  const props = defineProps({
    message: {
      type: Boolean
    },
    debugmodemessage: {
      type: Array
    }
  })
  let { message } = toRefs(props)
  //监测父子传值的变化
  watch(
    () => props,
    (newVal, oldVal) => {
      debugModeList.value = oldVal.debugmodemessage
    },
    { deep: true }
  )

  const demo = reactive({
    formData: {
      visiable: 'false',
      fileType: '0',
      centerfregIn: '100MHz',
      intermediateFrequencyBandwidth: '100MHz',
      samplerateIn: '125MHz',
      startOffset: '0',
      CutoffLength: '0',
      IQpermutation: '1',
      debugMode: '0',
      dataType: '1',
      IQfile: '',
      powerMultiple: '1', //功率倍数，
      unitType: '1', //单位类型，
      steplen: '25KHz'
    }
  })
  //上传的文件内容
  const fileContent = ref({})
  const uploadParams = ref({})
  const filetypeList = ref([])
  const dataOrganizationList = ref([])
  const datatypeList = ref([])
  const debugModeList = ref([])
  const unitTypeList = ref([])
  const fatherValue = ref(false)
  const title = ref('上传文件')
  const width = ref(680)
  const height = ref(720)
  const emit = defineEmits(['childcLick'])

  watch(
    () => demo.formData.fileType,
    (newVal, oldVal) => {
      if (newVal == 0) {
        demo.formData.dataType = '1'
      } else {
        demo.formData.dataType = '3'
      }
    }
  )

  const closeModal = () => {
    emit('childcLick', false)
    clearFileHash(false, fileContent.value, uploadParams.value)
  }
  onMounted(() => {
    fatherValue.value = props.message
    const store = useDictStore()
    //获取文件类型
    store.dict
      .filter(item => item.dictType == 'filetype')
      .forEach(item => {
        filetypeList.value.push({ label: item.dictLabel, value: item.dictValue })
      })
    // 获取IQ数据组织方式
    store.dict
      .filter(item => item.dictType == 'data_organization')
      .forEach(item => {
        dataOrganizationList.value.push({ label: item.dictLabel, value: item.dictValue })
      })
    //获取IQ数据类型
    store.dict
      .filter(item => item.dictType == 'data_type')
      .forEach(item => {
        datatypeList.value.push({ label: item.dictLabel, value: item.dictValue })
      })
    //获取IQ数据类型
    store.dict
      .filter(item => item.dictType == 'spec_unit_type')
      .forEach(item => {
        unitTypeList.value.push({ label: item.dictLabel, value: item.dictValue })
      })
  })
  //获取选择的文件名称
  const clickEvent1 = async () => {
    try {
      const { file } = await VXETable.readFile()
      fileContent.value = file
      demo.formData.IQfile = file.name
    } catch (e) {
      /* empty */
    }
  }
  const interfaceUnitConversion = data => {
    if (/[a-zA-Z]/.test(data)) {
      return data
    } else {
      if (data / 1000 >= 0 && data / 1000 < 1000) {
        return data / 1000 + 'KHz'
      } else if (data / 1000000 > 0 && data / 1000000 < 1000) {
        return data / 1000000 + 'MHz'
      } else if (data / 1000000000 > 0) {
        return data / 1000000000 + 'GHz'
      }
      return data
    }
  }
  //上传从本地获取的文件
  const insertEvent = async () => {
    uploadParams.value.fileType = demo.formData.fileType
    uploadParams.value.powerMultiple = demo.formData.powerMultiple ? demo.formData.powerMultiple : 1
    uploadParams.value.unitType = demo.formData.unitType
    uploadParams.value.centerfregInStr = interfaceUnitConversion(demo.formData.centerfregIn)
    uploadParams.value.bwInStr = interfaceUnitConversion(
      demo.formData.intermediateFrequencyBandwidth
    )
    uploadParams.value.samplerateInStr = interfaceUnitConversion(demo.formData.samplerateIn)
    uploadParams.value.steplen = plotToNum(demo.formData.steplen)
    uploadParams.value.steplenStr = interfaceUnitConversion(demo.formData.steplen)
    uploadParams.value.dataOrganization = demo.formData.IQpermutation
    uploadParams.value.debugMode = demo.formData.debugMode
    uploadParams.value.dataType = demo.formData.dataType
    uploadParams.value.startOffset = demo.formData.startOffset
    uploadParams.value.CutoffLength = demo.formData.CutoffLength
    uploadParams.value.coderateStr = '0KHz'
    uploadParams.value.createBy = Cookies.get('username')
    uploadFile(fileContent.value, uploadParams.value)
  }
  const chunkList = reactive(fileChunkList)
  setFileChunkList(chunkList)
  // 总进度条
  const totoalPercentage = computed(() => {
    if (!chunkList.value.length) {
      return 0
    }
    const loaded = chunkList.value
      .map(item => (item.size | 0) * (item.percentage | 0))
      .reduce((curr, next) => curr + next)
    return parseInt(loaded / file.size).toFixed(2)
  })
  // 分块进度
  const onUploadProgress = item => e => {
    // e 是后台的回调
    console.log('event', e)
    item.percentage = parseInt(String(e.loaded / e.total) * 100)
  }
  const uploadFile = async (file, parmas) => {
    console.log(parmas, 123456)
    await upload(file, onUploadProgress, parmas)
    fileContent.value = {}
    demo.formData.IQfile = ''
    closeModal()
  }
</script>

<style scoped>
  .sampleratein-style {
    margin: 0 13px;
    width: 315px;
  }
  .browse-button {
    float: right;
    margin: -43px 30px;
  }
  .cancel-button {
    margin: 11px 400px;
  }
  .upload-button {
    margin: 0px -373px;
  }
  .radio-name {
    margin: 0 20;
  }
  .file-name {
    margin: 0 11px;
  }
  .center-name {
    width: 100%;
    height: 32px;
    border-radius: 4px;
    outline: 0;
    margin: 0;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 0.6em;
    border: 1px solid var(--chart-text-color);
    color: var(--chart-text-color);
    background-color: var(--background-color);
    box-shadow: none;
  }
  .sampleratein-name {
    width: 95%;
    height: 32px;
    border-radius: 4px;
    outline: 0;
    margin: 0 15px;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 0.6em;
    border: 1px solid var(--chart-text-color);
    color: var(--chart-text-color);
    background-color: var(--background-color);
    box-shadow: none;
  }
  :deep(.vxe-modal--header) {
    color: var(--chart-text-color);
    background-color: var(--background-color);
  }
  :deep(.vxe-form--wrapper) {
    color: var(--chart-text-color);
    background-color: var(--background-color);
  }
  :deep(.vxe-modal--content) {
    color: var(--chart-text-color);
    background-color: var(--background-color);
  }
  .title-name {
    margin: 0 50px;
  }
  .select-style {
    background-color: var(--background-color);
    border: 1px solid var(--chart-text-color);
    border-radius: 4px;
    padding: 8px;
    color: var(--chart-text-color);
    width: 100%;
    cursor: pointer;
  }
  :deep() .vxe-modal--header-title {
    text-align: center;
  }
  :deep() .vxe-form--item {
    margin: 0 50px;
  }
</style>
