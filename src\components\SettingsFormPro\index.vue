<template>
  <div class="sidebar-settings">
    <!--  频段设置 -->
    <div class="titleSetting">
      <span class="title-span">频段设置</span>
      <img style="width: 100%; height: 35px" src="@/assets/icons/svg/u246.svg" />
    </div>
    <div class="form-content">
      <slot />
      <el-form v-if="!model.hideTab">
        <el-form-item>
          <el-radio-group
            v-model="model.commandType"
            class="mt-4"
            style="width: 80%; margin: 0 auto"
          >
            <el-radio size="large" :label="0" style="flex: 1">扫描</el-radio>
            <el-radio size="large" :label="1" style="flex: 1" disabled> 测向 </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <!--  谱线设置 -->
    <div class="titleSetting">
      <span class="title-span">谱线设置</span>
      <img style="width: 100%; height: 35px" src="@/assets/icons/svg/u246.svg" />
    </div>
    <div class="form-content">
      <spectralLineSetting v-model="model.refLevel" />
    </div>
    <!--   任务参数 -->
    <div class="titleSetting">
      <span class="title-span">任务参数</span>
      <img style="width: 100%; height: 35px" src="@/assets/icons/svg/u246.svg" />
    </div>
    <div class="form-content">
      <taskParams ref="tpRef" :model="model">
        <template #formItem="{ form }">
          <slot name="formItem" :form="form" />
        </template>
      </taskParams>
    </div>
    <div class="ml-7">
      <el-checkbox
        style="color: #d0d0d0"
        label="频谱数据存盘"
        v-model="model.isSaveFreq"
      ></el-checkbox>
    </div>
    <div style="margin-top: 28px; display: flex; justify-content: center">
      <el-button
        class="ctl-btn mr-10"
        :class="{ close: model.status }"
        @click="emit('start', tpRef.form)"
      >
        <x-icon
          class="mr-3"
          icon="start"
          source="cus"
          :color="!model.status ? '#80ffff' : ''"
          size="16"
        />
        启动
      </el-button>
      <el-button class="ctl-btn ml-10" :class="{ close: !model.status }" @click="emit('stop')">
        <x-icon
          class="mr-3"
          icon="scanstop"
          source="cus"
          :color="model.status ? '#80ffff' : ''"
          size="16"
        />
        停止
      </el-button>
    </div>
  </div>
</template>

<script setup name="ScanRightSettings">
  import spectralLineSetting from './SpectralLineSetting.vue'
  import taskParams from './TaskParams.vue'

  const props = defineProps({
    model: {
      type: Object,
      default: () => ({})
    }
  })
  const tpRef = ref(null)
  const emit = defineEmits(['start', 'stop'])
</script>
<style scoped>
  .titleSetting {
    width: 95%;
    height: 16px;
    margin: 0% 2% 0% 2%;
  }
  .title-span {
    color: var(--scan-text-color);
    position: relative;
    top: 22px;
    left: 2%;
  }
  .form-content {
    width: 100%;
    margin: 50px 0 0 10px;
  }
  :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
    color: #fff !important;
  }
</style>
