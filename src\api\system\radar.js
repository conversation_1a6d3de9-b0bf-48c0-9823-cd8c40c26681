import request from '@/utils/request'
// 新增continueWave配置
export function addRadar(data) {
  return request({
    url: 'radarSignal/radarSignal',
    method: 'post',
    data: data
  })
}

// 删除参数配置
export function delRadar(radarSignalIds) {
  return request({
    url: 'radarSignal/radarSignal',
    method: 'delete',
    data: radarSignalIds
  })
}

// 修改参数配置
export function updateRadar(data) {
  return request({
    url: 'radarSignal/radarSignal',
    method: 'put',
    data: data
  })
}

// 查询Radar列表
export function listRadar(query) {
  return request({
    url: 'radarSignal/radarSignal',
    method: 'get',
    params: query
  })
}
