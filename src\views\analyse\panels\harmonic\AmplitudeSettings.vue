<template>
  <data-form :fields="fields" :model="model" />
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData'

  const fields = [
    { target: 'refLevel', name: '输入电平', appendText: 'dBuv' },
    { target: 'showRefLevel', name: '显示参考电平', appendText: 'dBuv' },
    { target: 'scale', name: '刻度', appendText: 'dB' }
  ]

  const model = reactive(new ModelData(fields, 'harmonic'))
</script>
