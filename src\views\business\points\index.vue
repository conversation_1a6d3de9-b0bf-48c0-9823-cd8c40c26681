<template>
  <el-row class="app-container">
    <left-tree :chapter-id="chapterId" @change="res => (chapterId = res)" @judge="res => (judge = res)" />
    <right-list :key="chapterId" :chapter-id="chapterId" :judge="judge" />
  </el-row>
</template>

<script setup name="Point">
  import LeftTree from './left.vue'
  import RightList from './right.vue'
  import router from '@/router'
  import { onMounted } from 'vue'
  const chapterId = ref('')
  const judge = ref(0)
  const { path, query, matched } = router.currentRoute.value
  // chapterId.value = query.chapterId || ''
  watch(
    () => router.currentRoute.value.query,
    (newValue, oldValue) => {
      console.log('newValue', newValue)
      console.log('oldValue', oldValue)
      // userId.value = newValue.id
      chapterId.value = newValue.chapterId
    },
    { immediate: true }
  )

  computed(() => {
    const { path, query, matched } = router.currentRoute.value
    chapterId.value = query.chapterId
  })

  onMounted(() => {})
</script>
