<template>
  <el-table class="max-h-[270px] h-[270px]" border :height="270" :data="positionList">
    <el-table-column label="序号" type="index" width="60" align="center" />
    <el-table-column label="信号频率" prop="lfreq" width="200" align="center">
      <template #default="{ row }">
        {{ numToPlot(row.lfreq) }}
      </template>
    </el-table-column>
    <el-table-column label="信号带宽" prop="lbw" align="center">
      <template #default="{ row }">
        {{ numToPlot(row.lbw) }}
      </template>
    </el-table-column>
    <el-table-column label="定位时间" prop="locationDate" align="center">
      <!-- <template #default="{ row }">
        {{  }}
        {{ convertTimestampToDateTimeString(row.locationDate) }}
      </template> -->
    </el-table-column>
    <el-table-column label="经度" prop="dlon_out" align="center">
      <template #default="{ row }">
        {{ round(Number(row.dlon_out), 6) }}
      </template>
    </el-table-column>
    <el-table-column label="纬度" prop="dlat_out" align="center">
      <template #default="{ row }">
        {{ round(Number(row.dlat_out), 6) }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
  import { numToPlot, convertTimestampToDateTimeString } from '@/utils/utils'
  import { round } from 'lodash'

  const props = defineProps({
    positionList: {
      type: Array,
      default: () => []
    }
  })
</script>
