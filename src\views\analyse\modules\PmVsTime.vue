<template>
  <div
    v-if="model"
    class="plot-time-container"
  >
    <data-chart
      :id="PM_VS_TIME" 
      :data="model.data"
      title="FM vs 时间"
      :config="model.config"
    />
  </div>
</template>

<script setup>
  // PM vs Time 图表
  import DataChart from '@/components/DataChart'
  import useChartsStore from '@/store/modules/charts'
  import useChartOptions from '@/common/hooks/chartOptions'
  import { queryPlots } from '@/api/charts'
  import { cloneDeep } from 'lodash'
  import { PM_VS_TIME } from '@/constant/chartConsts'

  const chartsStore = useChartsStore()
  const model = ref(null)
  const genearteChartsOptions = (chartOptions) => {
    const currentOptions = cloneDeep(chartOptions)
    currentOptions.yAxis.min = undefined
    currentOptions.yAxis.max = undefined
    currentOptions.yAxis.unit = 'Hz'
    return currentOptions
  }
  const init = async () => {
    const body =  chartsStore.getFileInfo()
    const { data } = await queryPlots(body)
    const xMax = chartsStore.static.enabled ? data.dbm.length - 1 : chartsStore.viewNum - 1
    const { chartOptions } = useChartOptions('sequential', xMax)
    console.log(chartOptions)
    generateModel(data.dbm, chartOptions)
  }
  const generateModel = (data, chartOptions) => {
    model.value = {
      id: PM_VS_TIME,
      data: data,
      config: {
        type: computed(() => chartsStore.static.enabled ? 'static' : 'trend'),
        chart: computed(() => genearteChartsOptions(chartOptions.value)),
        viewNum: computed(() => chartsStore.static.enabled ? 'all' : chartsStore.viewNum)
      }
    }
  }

  onMounted(() => {
    init()
  })
</script>
