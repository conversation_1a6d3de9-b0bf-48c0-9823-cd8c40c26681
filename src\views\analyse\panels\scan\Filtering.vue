<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData'
  import useScanFormStore from '@/store/modules/form/scanForm'

  const fields = ref([
    {
      target: 'filterFlag',
      name: '滤波',
      isCheckbox: true
    },
    {
      target: 'filterorder',
      name: '滤波器阶数',
      int: true,
      change(val) {
        scanFormStore.setFilterorder()
      }
    },
    {
      target: 'coefficientDown',
      name: '滚降系数',
      float: false,
      change(val) {
        scanFormStore.setCoefficientDown(val)
      }
    }
  ])

  const emit = defineEmits(['operation'])
  const model = reactive(new ModelData(fields.value, 'scan'))
  const scanFormStore = useScanFormStore()
  watch(
    () => scanFormStore.filterFlag,
    () => {
      model.update()
    },
    { deep: true }
  )
</script>
<template>
  <data-form :fields="fields" :model="model" />
</template>
