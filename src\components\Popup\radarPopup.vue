<template>
  <div>
    <vxe-modal
      :model-value="message"
      :title="title"
      :width="width"
      :height="height"
      position="center"
      @close="closeModal"
    >
      <template #default>
        <vxe-form :data="demo.formData">
          <vxe-form-item
            title="中频"
            field="intermediateFrequency"
            :item-render="{}"
            span="14"
          >
            <template #default="{ data }">
              <input
                v-model="data.intermediateFrequency"
                class="center-name"
                clearable
                maxlength="20"
                @input="
                  data.intermediateFrequency = data.intermediateFrequency.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              >
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="中心频率"
            field="centerfreqIn"
            :item-render="{}"
            span="14"
            class="title-name"
          >
            <template #default="{ data }">
              <input
                v-model="data.centerfreqIn"
                class="centerzp-name"
                maxlength="20"
                @input="
                  data.centerfreqIn = data.centerfreqIn.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              >
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="采样率"
            field="samplerateIn"
            :item-render="{}"
            span="14"
            class="title-name"
          >
            <template #default="{ data }">
              <input
                v-model="data.samplerateIn"
                class="sampleratein-name"
                maxlength="20"
                @input="
                  data.samplerateIn = data.samplerateIn.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              >
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="采样带宽"
            field="samplingBw"
            :item-render="{}"
            span="14"
            class="title-name"
          >
            <template #default="{ data }">
              <input
                v-model="data.samplingBw"
                class="samplerateinDk-name"
                maxlength="20"
                @input="
                  data.samplingBw = data.samplingBw.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              >
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="文件类型"
            field="fileTypeRadar"
            span="14"
            class="title-name"
          >
            <template #default="{ data }">
              <vxe-radio
                v-model="data.fileTypeRadar"
                name="fileTypeRadar"
                label="0"
                content="IQ文件"
              />
              <vxe-radio
                v-model="data.fileTypeRadar"
                name="fileTypeRadar"
                label="1"
                content="PDW文件"
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="数据类型"
            field="collectionMethod"
            span="14"
            class="title-name"
          >
            <template #default="{ data }">
              <vxe-radio
                v-model="data.collectionMethod"
                name="collectionMethod"
                label="0"
                content="连续"
              />
              <vxe-radio
                v-model="data.collectionMethod"
                name="collectionMethod"
                label="1"
                content="触发"
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="IQ文件"
            field="IQfile"
            :item-render="{}"
            span="14"
            class="title-name"
          >
            <template #default="{ data }">
              <vxe-input v-model="data.IQfile" clearable class="file-name" />
            </template>
          </vxe-form-item>
        </vxe-form>
        <vxe-button
          status="success"
          content="浏览"
          class="browse-button"
          @click="clickEvent1"
        />
        <vxe-button
          status="info"
          content="取消"
          class="cancel-button"
          @click="closeModal"
        />
        <vxe-button
          status="primary"
          content="上传"
          class="upload-button"
          @click="insertEvent(false)"
        />
      </template>
    </vxe-modal>
  </div>
</template>

<script setup>
  import { toRefs, ref, onMounted, watch } from 'vue'
  import { VXETable } from 'vxe-table'
  import Cookies from 'js-cookie'
  //分片上传
  import { uploadRadar } from '@/api/tool/request'
  import { fileChunkList, setFileChunkList, clearFileHash } from '@/api/tool/request/file'
  const props = defineProps({
    message: {
      type: Boolean
    },
    debugmodemessage: {
      type: Array
    }
  })
  let { message } = toRefs(props)
  //监测父子传值的变化
  watch(
    () => props,
    (newVal, oldVal) => {
      debugModeList.value = oldVal.debugmodemessage
    },
    { deep: true }
  )
  const demo = reactive({
    formData: {
      visiable: 'false',
      intermediateFrequency: '100MHz',
      centerfreqIn: '100MHz',
      samplerateIn: '50MHz',
      samplingBw: '50MHz',
      fileTypeRadar: '0',
      collectionMethod: '0',
      IQfile: ''
    }
  })
  //上传的文件内容
  const fileContent = ref({})
  const uploadParams = ref({})
  const debugModeList = ref([])
  const fatherValue = ref(false)
  const title = ref('上传文件(雷达)')
  const width = ref(700)
  const height = ref(500)
  const emit = defineEmits(['childcLick'])
  const closeModal = () => {
    emit('childcLick', false)
    clearFileHash(false, fileContent.value, uploadParams.value)
  }
  onMounted(() => {
    fatherValue.value = props.message
  })
  //获取选择的文件名称   
  const clickEvent1 = async () => {
    try {
      const { file } = await VXETable.readFile()
      fileContent.value = file
      demo.formData.IQfile = file.name
    } catch (e) { /* empty */ }
  }
  //上传从本地获取的文件
  const insertEvent = async () => {
    uploadParams.value.bwInStr = demo.formData.intermediateFrequency
    uploadParams.value.centerfregInStr = demo.formData.centerfreqIn
    uploadParams.value.samplerateInStr = demo.formData.samplerateIn
    uploadParams.value.coderateStr = demo.formData.samplingBw
    uploadParams.value.fileTypeRadar = demo.formData.fileTypeRadar
    uploadParams.value.collectionMethod = demo.formData.collectionMethod
    uploadParams.value.collectionTime = 1
    uploadParams.value.samplingNum = 1
    uploadParams.value.quantizationBits = 0
    uploadParams.value.channelsNum = 1
    uploadParams.value.pulseCount = 0
    uploadParams.value.createBy = Cookies.get('username')
    uploadFile(fileContent.value, uploadParams.value)
  }
  const chunkList = reactive(fileChunkList)
  setFileChunkList(chunkList)
  // 总进度条
  const totoalPercentage = computed(() => {
    if (!chunkList.value.length) {
      return 0
    }
    const loaded = chunkList.value
      .map(item => (item.size | 0) * (item.percentage | 0))
      .reduce((curr, next) => curr + next)
    return parseInt(loaded / file.size).toFixed(2)
  })
  // 分块进度
  const onUploadProgress = item => e => {
    // e 是后台的回调
    console.log('event', e)
    item.percentage = parseInt(String(e.loaded / e.total) * 100)
  }
  const uploadFile = async (file, parmas) => {
    await uploadRadar(file, onUploadProgress, parmas)
    fileContent.value = {}
    demo.formData.IQfile = ''
    closeModal()
  }
</script>

<style scoped>
  .sampleratein-style {
    margin: 0 13px;
    width: 315px;
  }
  .browse-button {
    float: right;
    margin: -43px 130px;
  }
  .cancel-button {
    margin: 11px 400px;
  }
  .upload-button {
    margin: 0px -373px;
  }
  .radio-name {
    margin: 0 20;
  }
  .file-name {
    margin: 0 11px;
  }
  .center-name {
    width: 92%;
    height: 32px;
    border-radius: 4px;
    outline: 0;
    margin: 0 27px;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 0.6em;
    border: 1px solid var(--chart-text-color);
    color: var(--chart-text-color) ;
    background-color: var(--background-color);
    box-shadow: none;
  }
  .centerzp-name {
    width: 100%;
    height: 32px;
    border-radius: 4px;
    outline: 0;
    margin: 0;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 0.6em;
    border: 1px solid var(--chart-text-color);
    color: var(--chart-text-color) ;
    background-color: var(--background-color);
    box-shadow: none;
  }
  .sampleratein-name {
    width: 95%;
    height: 32px;
    border-radius: 4px;
    outline: 0;
    margin: 0 15px;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 0.6em;
    border: 1px solid var(--chart-text-color);
    color: var(--chart-text-color) ;
    background-color: var(--background-color);
    box-shadow: none;
  }
  .samplerateinDk-name {
    width: 100%;
    height: 32px;
    border-radius: 4px;
    outline: 0;
    margin: 0;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 0.6em;
    border: 1px solid var(--chart-text-color);
    color: var(--chart-text-color) ;
    background-color: var(--background-color);
    box-shadow: none;
  }
  :deep(.vxe-modal--header){
    color: var(--chart-text-color) ;
    background-color: var(--background-color);
  }
  :deep(.vxe-form--wrapper){
    color: var(--chart-text-color) ;
    background-color: var(--background-color);
  }
  :deep(.vxe-modal--content){
    color: var(--chart-text-color) ;
    background-color: var(--background-color);
  }
  .title-name {
    margin: 0 50px;
  }
  :deep() .vxe-modal--header-title {
    text-align: center;
  }
  :deep() .vxe-form--item {
    margin: 0 50px;
  }
</style>
