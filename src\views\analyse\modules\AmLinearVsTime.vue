<template>
  <div
    v-if="model"
    class="am-liner-time-container"
  >
    <data-chart
      :id="AM_LINEAR_VS_TIME" 
      :data="model.data"
      :title="title"
      :config="model.config"
    />
  </div>
</template>

<script setup>
  // 调幅 vs Time（线性） 图表
  import { queryFms } from '@/api/charts'
  import DataChart from '@/components/DataChart'
  import useChartsStore from '@/store/modules/charts'
  import useChartOptions from '@/common/hooks/chartOptions'
  import { AM_LINEAR_VS_TIME } from '@/constant/chartConsts'
  import { cloneDeep } from 'lodash'

  const chartsStore = useChartsStore()
  const model = ref(null)
  const isInDm = useRoute().path.includes('digitalmodulation') // 是否在调制解析页面
  const title = computed(() => (isInDm ? '幅度误差 vs 时间' : '调幅 vs 时间'))
  const genearteChartsOptions = (chartOptions) => {
    const currentOptions = cloneDeep(chartOptions)
    currentOptions.yAxis.min = 0
    currentOptions.yAxis.max = undefined
    return currentOptions
  }
  const init = async () => {
    const body =  chartsStore.getFileInfo()
    const { data } = await queryFms(body)
    const { chartOptions } = useChartOptions('sequential', data.dbm.length - 1)
    generateModel(data.dbm, chartOptions)
  }
  const generateModel = (data, chartOptions) => {
    model.value = {
      id: AM_LINEAR_VS_TIME,
      data: data,
      config: {
        type: 'static',
        chart: computed(() => genearteChartsOptions(chartOptions.value)),
        viewNum: 'all'
      }
    }
  }

  onMounted(() => {
    init()
  })
</script>
