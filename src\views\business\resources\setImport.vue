<template>
  <el-dialog v-model="dia" title="导入虚拟机" width="400px" append-to-body>
    <el-form ref="formRef" v-loading="loading" class="mr-4" :model="form" label-width="6em">
      <el-form-item label="UUID" prop="uuid">
        <el-input v-model="form.uuid" maxlength="20" placeholder="请输入UUID" />
      </el-form-item>
      <el-form-item label="虚拟机IP" prop="ip">
        <el-input v-model="form.ipLike" placeholder="请输入虚拟机IP" />
      </el-form-item>
      <el-form-item label="虚拟机名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入虚拟机名称" />
      </el-form-item>
      <el-form-item label="主机编号" prop="ip">
        <el-input v-model="form.hostId" placeholder="请输入虚拟机编号" />
      </el-form-item>
      <el-form-item label="主机名称" prop="name">
        <el-input v-model="form.hostName" placeholder="请输入虚拟机名称" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel(false)">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
  import * as resApi from '@/api/business/resources'
  export default {
    name: 'ResourceDetail',
    emits: ['actSuccess'],
    data() {
      return {
        form: {},
        title: '',
        dia: false,
        loading: false,
        cb: null,
        resid: ''
      }
    },
    methods: {
      show(cb = null) {
        this.cb = cb || null
        this.form = {}
        this.dia = true
      },
      // 取消按钮
      cancel(t = false) {
        this.dia = false
        t && this.$emit('actSuccess')
        this.form = {}
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs['formRef'].validate(valid => {
          if (valid) {
            this.loading = true
            resApi
              .importResources(this.form)
              .then(response => {
                this.$modal.msgSuccess('操作成功')
                this.cancel(true)
              })
              .finally(() => {
                this.loading = false
              })
          }
        })
      }
    }
  }
</script>
