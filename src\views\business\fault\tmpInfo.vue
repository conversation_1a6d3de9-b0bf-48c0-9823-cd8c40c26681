<template>
  <el-dialog v-model="dia" width="800px" append-to-body :title="id ? '报修单详情' : '新增报修'">
    <c-form
      v-bind="formConfig"
      :key="form.faultId"
      ref="refsForm"
      v-model="form"
      v-loading="loading"
      class="mr-4"
    >
      <template #point="{ form }">
        <a @click="$refs.refsPointDetail.show(form.pointId, 'view')">{{
          form.srPoints?.pointName
        }}</a>
      </template>
    </c-form>
    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button type="primary" @click="sub">保存</el-button>
    </template>
    <PointDetail ref="refsPointDetail" />
  </el-dialog>
</template>
<script>
  import { getFault, addFault, updateFault } from '@/api/business/fault'
  import PointDetail from '../points/detail.vue'
  export default {
    components: { PointDetail },
    emits: ['actSuccess', 'close'],
    data() {
      return {
        dia: false,
        loading: false,
        id: '',
        form: {},
        formConfig: {
          splice: 2,
          labelWidth: '8em',
          layout: 'block',
          autoEmit: false,
          hideBtn: true,
          formInfo: [
            { title: '报修资源名称', name: 'repairResName', readonly: true },
            { title: '报修人', name: 'repairUserName', readonly: true },
            {
              isFull: true,
              title: '故障描述',
              name: 'repairBiz',
              type: 'editor',
              readonly: true
            },
            {
              isFull: true,
              title: '故障原因',
              type: 'textarea',
              height: '200px',
              maxLen: 500,
              name: 'repairReason'
            },
            {
              isFull: true,
              title: '修复措施',
              height: '200px',
              maxLen: 500,
              name: 'repairDispose',
              type: 'textarea'
            },
            { title: '报修时间', name: 'repairTime', type: 'date' },
            { title: '计划修理时间', name: 'repairPlanTime', type: 'date' },
            { title: '修复完成时间', name: 'repairDoneTime', type: 'date' },
            { title: '报修状态', name: 'repairStatus', type: 'dict', dictName: 'vm_repair_status' },
            { isFull: true, title: '备注', name: 'remark', type: 'textarea' }
          ]
        }
      }
    },

    methods: {
      show(id) {
        this.id = id
        this.dia = true
        this.form = {}
        id && this.getInfo(id)
      },
      getInfo(id) {
        this.loading = true
        getFault(id)
          .then(res => {
            this.form = res.data
          })
          .finally(() => {
            this.loading = false
          })
      },
      sub() {
        this.$refs.refsForm.subForm(form => {
          let act = this.form.faultId ? updateFault : addFault
          act(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
        })
      }
    }
  }
</script>
<style></style>
