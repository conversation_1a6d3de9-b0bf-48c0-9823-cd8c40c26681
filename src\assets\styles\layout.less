.mb8 {
  margin-bottom: 1rem;
}
svg {
  display: inline-block;
}
.el-dialog__body {
  padding: 10px 20px !important;
}
.el-tree-node__expand-icon.is-leaf {
  width: 0 !important;
}
.el-message-box__message p,
.el-message .el-message__content {
  white-space: pre-line;
  line-height: 1.3;
  max-height: 13em;
  overflow: auto;
}
.el-link .el-icon {
  margin-right: 3px;
}
.el-slider {
  margin: 0 10px;
}
.cus-card {
  border-radius: 10px;
  border: none;
  box-shadow: 0px 0px 1rem 0px rgba(82, 137, 136, 0.08);

  .el-card__header {
    font-size: 1rem;
    font-weight: bold;
    padding: 1rem;
    line-height: 2rem;
    .card-header {
      display: inline-block;
      width: 100%;
      &::before {
        content: '';
        border-left: 4px solid var(--el-color-primary);
        margin-right: 10px;
        height: 1rem;
        vertical-align: middle;
      }
    }
    .moreIcon {
      cursor: pointer;
      color: #cad2d9;
      float: right;
      margin-right: 1rem;
      :hover {
        color: var(--el-color-primary);
      }
    }
  }
}

.text-item-list {
  li {
    border-bottom: 1px solid var(--el-border-color-base);
    display: block;
    padding: 1rem 0;
    word-break: break-all;
    cursor: pointer;
    .center-text {
      padding: 0 5px;
    }
    .flex {
      display: flex;
      flex-direction: row;
      .center-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .left-type {
      color: var(--el-color-primary);
    }
    .right-info {
      color: #a4acb2;
    }
    .desc {
      width: 100%;
      padding-top: 5px;
      flex: 0 0 100%;
      font-size: 12px;
      color: var(--el-color-light);
      span {
        padding-right: 1em;
      }
    }
  }
}
.flex-tabs {
  flex: 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
    .el-tab-pane {
      height: 100%;
      overflow: auto;
    }
  }
}
.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  &.el-row {
    flex-direction: row;
    .el-col {
      height: 100%;
    }
  }
  .flex-main {
    flex: 1;
    overflow: auto;
  }

  .fit-table {
    flex: 1;
    overflow: auto;
    &.el-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;
      .el-tabs__content {
        flex: 1;
        overflow: auto;
        .el-tab-pane {
          height: 100%;
        }
      }
    }
    .el-table,
    .table-wrap {
      height: 100%;
      width: 100%;
      .el-link,
      .el-link__inner {
        line-height: 1.5;
        display: inline;
      }
    }
  }
}

.btn-tabs {
  .el-tabs__nav-wrap::after,
  .el-tabs__active-bar {
    display: none;
  }
  .el-tabs__item {
    border: 1px solid var(--el-border-color-base);
    border-radius: 3em;
    height: 2rem;
    line-height: 30px;
    font-size: 14px;
    padding: 0 20px !important;
    margin-right: 1em;
    color: var(--el-text-color-regular);
    &.is-active {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary);
      color: var(--el-color-white);
      font-weight: bold;
    }
  }
}

.el-row__border-right {
  border-right: 1px solid var(--el-border-color-base);
}

.mini-dialog {
  width: 420px;
}
.medium-dialog {
  width: 840px;
}

.box-item {
  border: 1rem solid #fff;
  background-color: var(--el-color-white);
  box-shadow: 0 0 10px rgba(122, 111, 111, 0.4);
  border-radius: 0.3rem;
  padding: 0 !important;
  box-sizing: border-box;
}
