<template>
  <div class="app-container">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      @selectionChange="rows => (selection = rows)"
    />
    <mtDetailInfo ref="refsMt" @actSuccess="getList()" />
  </div>
</template>
<script>
  import * as mtApi from '@/api/business/clazz'
  import mtDetailInfo from './detail.vue'
  export default {
    name: 'Sctudents',
    components: { mtDetailInfo },
    data() {
      return {
        selection: [],
        tableFormConfig: {
          title: '期班管理',
          searchConfig: {
            formConfig: [
              { title: '期班名称', name: 'claName' },
              { title: '所属专业', name: 'cla<PERSON>ajor', type: 'dict', dictName: 'stu_major' },
              {
                title: '期班负责人',
                name: 'claMaster',
                type: 'cuselect',
                queryInfo: 'business/teacher/list?pageNum=1&pageSize=100',
                replaceFields: { label: 'teaName', value: 'teaId' }
              }
            ],
            loadData: p => {
              return mtApi.listClazz(p)
            }
          },
          actions: [
            {
              label: '新增',
              type: 'primary',
              icon: 'el_Plus',
              permission: ['business:clazz:add'],
              click: () => {
                this.$refs.refsMt.show()
              }
            },
            {
              label: '导出',
              icon: 'el_upload',
              type: 'warning',
              permission: ['business:clazz:export'],
              click: row =>
                this.download(
                  'business/clazz/export',
                  {
                    // ...queryParams.value
                  },
                  `期班管理_${new Date().getTime()}.xlsx`
                )
            },
            {
              type: 'danger',
              label: '删除',
              permission: ['business:clazz:remove'],
              disabled: () => !this.selection.length,
              icon: 'el_Delete',
              click: row => this.deleteAll()
            }
          ],
          tableConfig: {
            isChecked: true,
            cols: [
              { label: '期班编码', prop: 'claCode' },
              { label: '期班名称', prop: 'claName', minWidth: 200 },
              { label: '所属院系', prop: 'claFaculty', type: 'dict', dictName: 'faculty' },
              { label: '所属专业', prop: 'claMajor', type: 'dict', dictName: 'stu_major' },
              { label: '期班负责人', prop: 'claMasterName' },
              { label: '指导老师', prop: 'claInstructorName' },
              {
                label: '操作',
                type: 'action',
                width: '80',
                actions: [
                  {
                    title: '编辑',
                    icon: 'el_Edit',
                    permission: ['business:clazz:edit'],
                    click: row => this.$refs.refsMt.show(row.calId)
                  },
                  {
                    title: '删除',
                    icon: 'el_Delete',
                    permission: ['business:clazz:remove'],
                    click: row => this.deleteAll(row.calId)
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList(data = null) {
        this.$refs.refsTableForm.loadData(data)
      },
      //批量删除模板
      deleteAll(id = null) {
        if (!this.selection.length && !id) return this.$model.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选期班吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.calId)
          mtApi.delClazz(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.getList()
          })
        })
      }
    }
  }
</script>
