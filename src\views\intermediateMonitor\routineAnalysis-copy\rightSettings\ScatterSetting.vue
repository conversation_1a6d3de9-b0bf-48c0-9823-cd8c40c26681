<template>
  <div>
    <el-form class="left">
      <!--  Begin:Mod by xf at 2023-12-05 -->
      <el-form-item label="起始频率">
        <el-input v-model="form.startFre" type="number">
          <template #append>
            <el-select v-model="selectform.startFre">
              <el-option v-for="unit in units" :key="unit" :label="unit" :value="unit" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="终止频率">
        <el-input v-model="form.endFre" type="number">
          <template #append>
            <el-select v-model="selectform.endFre">
              <el-option v-for="unit in units" :key="unit" :label="unit" :value="unit" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="驻留时间">
        <el-input v-model="form.stayTime" type="number">
          <template #append>
            <span class="pl-1 pr-1">ms</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="衰减">
        <el-input v-model="form.attenuation" type="number">
          <template #append>
            <span class="pl-1 pr-1">dBm</span>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup>
  import useScanStore from '@/store/modules/scanMonitor'
  import { numToPlot, plotToNum } from '@/utils/utils'

  const { monitor } = useScanStore()
  const form = ref({ ...monitor })
  const selectform = ref({
    startFre: 'Hz',
    endFre: 'Hz'
  })
  const units = ['GHz', 'MHz', 'KHz', 'Hz']
  const datachangeFun = key => {
    if (key) {
      monitor[key] = plotToNum(form.value[key] + selectform.value[key] || 'Hz')
      formmatValue(key, monitor[key])
      linkage(key)
      return
    }
    Object.keys(form.value).forEach(key => {
      formmatValue(key, monitor[key])
    })
  }
  const linkage = key => {
    const freStore = monitor
    if (key === 'centerFreq' || key === 'bandwidth') {
      freStore.startFre = freStore.centerFreq * 1 - (freStore.bandwidth / 2) * 1
      freStore.endFre = freStore.centerFreq * 1 + (freStore.bandwidth / 2) * 1
      formmatValue('startFre', freStore.startFre)
      formmatValue('endFre', freStore.endFre)
    }
    if (key === 'startFre' || key === 'endFre') {
      freStore.bandwidth = freStore.endFre * 1 - freStore.startFre * 1
      freStore.centerFreq = freStore.startFre * 1 + (freStore.bandwidth / 2) * 1
      formmatValue('centerFreq', freStore.centerFreq)
      formmatValue('bandwidth', freStore.bandwidth)
    }
  }
  const formmatValue = (key, value) => {
    const newFormVal = numToPlot(value)
    if (newFormVal.search('KHz') != -1) {
      form.value[key] = newFormVal.replace('KHz', '')
      selectform.value[key] = 'KHz'
    } else if (newFormVal.search('MHz') != -1) {
      form.value[key] = newFormVal.replace('MHz', '')
      selectform.value[key] = 'MHz'
    } else if (newFormVal.search('GHz') != -1) {
      form.value[key] = newFormVal.replace('GHz', '')
      selectform.value[key] = 'GHz'
    }
  }

  onMounted(() => {
    datachangeFun()
  })
</script>
<!--  End:Mod by xf at 2023-12-05 -->
