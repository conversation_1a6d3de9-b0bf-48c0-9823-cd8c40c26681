<template>
  <div class="constellation-main">
    <div v-if="loaded" class="charts-body">
      <el-row class="chart-row" gutter="16">
        <el-col
          v-for="(comp, index) in chartsStore.dmViews"
          :key="comp.name"
          :span="12"
          :style="generateStyle(index)"
        >
          <div class="chart-module">
            <component :is="comp.comp" v-bind="comp.binding" />
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="DigitalModulation">
  import useChartsStore from '@/store/modules/charts'
  import useDmFormStore from '@/store/modules/form/dmForm'

  const dmFormStore = useDmFormStore()
  const router = useRouter()
  const chartsStore = useChartsStore()
  const loaded = ref(true)

  const generateStyle = index => {
    if (index > 1) {
      return { marginTop: '16px' }
    }
  }

  const refresh = () => {
    loaded.value = false
    nextTick(() => {
      loaded.value = true
    })
  }

  watch(() => dmFormStore.resultLen, () => {
    refresh()
  })
  onBeforeMount(() => {
    // 将本地缓存写入store
    chartsStore.readStorage()
    if (!chartsStore.getFileInfo().fileName) {
      router.push({ path: 'filemanage' })
    }
  })

  onBeforeUnmount(() => {
    // 将store里面的内容写入本地缓存
    chartsStore.writeStorage()
  })
</script>

<style lang="scss" scoped>
  .constellation-main {
    height: 100%;
    position: relative;
    .chart-bar {
      position: absolute;
      top: 0;
      width: 100%;
    }

    .charts-body {
      box-sizing: border-box;
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      overflow-x: hidden;
      overflow-y: auto;
      .chart-row {
        .el-col {
          height: 50%;
        }
        .chart-module {
          border: 1px solid var(--el-border-color);
        }
      }
    }
  }
</style>
