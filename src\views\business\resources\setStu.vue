<template>
  <el-dialog v-model="dia" title="设置学生" width="400px" append-to-body>
    <el-form
      ref="formRef"
      v-loading="loading"
      class="mr-4 common-form"
      :model="form"
      label-width="6em"
      :rules="rules"
    >
      <el-form-item label="所属期班" prop="clazzId">
        <cu-select
          v-model="form.clazzId"
          placeholder="请选择所属期班"
          :query-info="`business/clazz/list?pageNum=1&pageSize=100`"
          :replace-fields="{ label: 'claName', value: 'calId' }"
          @change="changeClazz"
        />
      </el-form-item>
      <el-form-item label="所属学生" prop="stuID">
        <cu-select
          :key="form.clazzId"
          v-model="form.stuID"
          placeholder="请选择所属学生"
          :query-info="`business/students/listAll?calId=${form.clazzId}&pageNum=1&pageSize=100`"
          :replace-fields="{ label: 'stuName', value: 'stuId' }"
          @change="changeStu"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel(false)">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
  import * as resApi from '@/api/business/resources'
  export default {
    name: 'ResourceDetail',
    emits: ['actSuccess'],
    data() {
      return {
        form: {},
        title: '',
        dia: false,
        loading: false,
        cb: null,
        resid: '',
        rules: {
          stuID: [{ required: true, message: '请选择所属学生' }]
        }
      }
    },
    methods: {
      changeClazz({ obj = {} }) {
        this.form.stuID = ''
        this.form.clazzName = obj.claName || ''
      },
      changeStu({ obj = {} }) {
        this.form.stuName = obj.stuName
      },
      show(id, cb = null) {
        this.cb = cb || null
        this.dia = true
        this.form = {
          resIds: id
        }
      },
      // 取消按钮
      cancel(t = false) {
        this.dia = false
        t && this.$emit('actSuccess')
        this.form = {}
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs['formRef'].validate(valid => {
          if (valid) {
            this.loading = true
            resApi
              .setOwnStudentResources(this.form)
              .then(response => {
                this.$modal.msgSuccess('操作成功')
                this.cancel(true)
              })
              .finally(() => {
                this.loading = false
              })
          }
        })
      }
    }
  }
</script>
