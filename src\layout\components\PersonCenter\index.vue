<template>
  <div class="avatar-container">
    <el-dropdown class="right-menu-item hover-effect" trigger="click" @command="handleCommand">
      <div class="avatar-wrapper">
        <!-- <img :src="userStore.avatar" class="user-avatar" /> -->
        {{ userStore.name }}
        <el-icon><caret-bottom /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <!-- <el-dropdown-item command="setLayout">
                <span>布局设置</span>
              </el-dropdown-item> -->
          <el-dropdown-item divided command="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup name="PersonCenter">
  import useUserStore from '@/store/modules/user'
  import { ElMessageBox } from 'element-plus'

  const userStore = useUserStore()
  const router = useRouter()
  function handleCommand(command) {
    switch (command) {
      case 'setLayout':
        setLayout()
        break
      case 'logout':
        logout()
        break
      default:
        break
    }
  }
  function logout() {
    ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        userStore.logOut().then(() => {
          location.reload()
          router.push({ name: '/index' })

          // location.href = "/index";
        })
      })
      .catch(() => {})
  }

  const emits = defineEmits(['setLayout'])

  function setLayout() {
    emits('setLayout')
  }
</script>

<style lang="scss" scoped>
  .avatar-container {
    margin-right: 10px;

    .avatar-wrapper {
      position: relative;

      .user-avatar {
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 10px;
      }

      i {
        cursor: pointer;
        font-size: 12px;
      }
    }
    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      vertical-align: text-bottom;
      line-height: 50px;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
  }
</style>
