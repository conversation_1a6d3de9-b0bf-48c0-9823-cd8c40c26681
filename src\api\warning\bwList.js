import request from '@/utils/request'

// 查询黑白名单列表
export function getBWList(data) {
  return request({
    url: '/alert/blackwhite/list',
    method: 'post',
    data: data
  })
}

// 新增/更新 黑白名单
export function addOrUpdateBW(data) {
  return request({
    url: '/alert/blackwhite/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 获取黑白名单详情
export function getBWDetail(bwId) {
  return request({
    url: '/alert/blackwhite/detail?id=' + bwId,
    method: 'get',
    contentType:'application/x-www-form-urlencoded',
  })
}

// 删除黑白名单
export function delBW(data) {
  return request({
    url: '/alert/blackwhite/del',
    method: 'post',
    data
  })
}
// 批量删除黑白名单
export function batchDelBW(data) {
  return request({
    url: '/alert/blackwhite/batchDel',
    method: 'post',
    data
  })
}