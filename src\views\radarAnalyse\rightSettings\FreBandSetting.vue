<template>
  <el-form>
    <el-form-item label="中心频率">
      <el-input v-model="form.centerFreq" type="number" @change="datachangeFun('centerFreq')">
        <template #append>
          <el-select v-model="selectform.centerFreq" @change="datachangeFun('centerFreq')">
            <el-option v-for="unit in units" :key="unit" :label="unit" :value="unit" />
          </el-select>
        </template>
      </el-input>
    </el-form-item>

    <el-form-item label="中频带宽">
      <el-input v-model="form.bandwidth" type="number" @change="datachangeFun('bandwidth')">
        <template #append>
          <el-select v-model="selectform.bandwidth" @change="datachangeFun('bandwidth')">
            <el-option v-for="unit in units" :key="unit" :label="unit" :value="unit" />
          </el-select>
        </template>
      </el-input>
    </el-form-item>

    <el-form-item label="分辨率">
      <el-input
        v-model="form.resolutionRatio"
        type="number"
        @change="datachangeFun('resolutionRatio')"
      >
        <template #append>
          <el-select
            v-model="selectform.resolutionRatio"
            @change="datachangeFun('resolutionRatio')"
          >
            <el-option v-for="unit in units" :key="unit" :label="un" :value="unit.value" />
          </el-select>
        </template>
      </el-input>
    </el-form-item>
    <!-- <el-form-item>
      <el-radio-group
        v-model="radar.commandType"
        class="mt-4"
        style="width: 80%; margin: 16px auto;"
      >
        <el-radio size="large" :label="0" style="flex: 1;">分析</el-radio>
        <el-radio size="large" :label="1" style="flex: 1;">测向</el-radio>
      </el-radio-group>
    </el-form-item> -->
  </el-form>
</template>
<script setup>
  import useScanStore from '@/store/modules/scanMonitor'
  import { numToPlot, plotToNum } from '@/utils/utils'

  const { radar } = useScanStore()
  const form = ref({ ...radar })
  const selectform = ref({
    centerFreq: 'Hz',
    bandwidth: 'Hz',
    resolutionRatio: 'Hz'
  })
  const units = ['GHz', 'MHz', 'KHz', 'Hz']
  const datachangeFun = key => {
    if (key) {
      radar[key] = plotToNum(form.value[key] + selectform.value[key] || 'Hz')
      formmatValue(key, form.value[key])
      linkage(key)
      return
    }
    Object.keys(form.value).forEach(key => {
      formmatValue(key, radar[key])
    })
  }
  const linkage = key => {
    const freStore = radar
    if (key === 'centerFreq' || key === 'bandwidth') {
      freStore.startFre = freStore.centerFreq * 1 - (freStore.bandwidth / 2) * 1
      freStore.endFre = freStore.centerFreq * 1 + (freStore.bandwidth / 2) * 1
      formmatValue('startFre', freStore.startFre)
      formmatValue('endFre', freStore.endFre)
    }
    if (key === 'startFre' || key === 'endFre') {
      freStore.bandwidth = freStore.endFre * 1 - freStore.startFre * 1
      freStore.centerFreq = freStore.startFre * 1 + (freStore.bandwidth / 2) * 1
      formmatValue('centerFreq', freStore.centerFreq)
      formmatValue('bandwidth', freStore.bandwidth)
    }
  }
  const formmatValue = (key, value) => {
    const newFormVal = numToPlot(value)
    if (newFormVal.search('KHz') != -1) {
      form.value[key] = newFormVal.replace('KHz', '')
      selectform.value[key] = 'KHz'
    } else if (newFormVal.search('MHz') != -1) {
      form.value[key] = newFormVal.replace('MHz', '')
      selectform.value[key] = 'MHz'
    } else if (newFormVal.search('GHz') != -1) {
      form.value[key] = newFormVal.replace('GHz', '')
      selectform.value[key] = 'GHz'
    }
  }

  onMounted(() => {
    datachangeFun()
  })
</script>
