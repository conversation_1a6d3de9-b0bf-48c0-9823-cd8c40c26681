<template>
  <el-dialog v-model="dia" width="800px" append-to-body :title="id ? '知识点详情' : '新增知识点'">
    <c-form
      v-bind="formConfig"
      :key="form.pointId"
      ref="refsForm"
      v-model="form"
      class="mr-4"
      :disabled="!isEdit"
    />
    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button v-if="isEdit" type="primary" @click="sub">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
  import { getPoints, updatePoints, addPoints } from '@/api/business/points'
  export default {
    name: 'PointsDetail',
    props: ['chapterId'],
    emits: ['actSuccess'],
    data () {
      return {
        dia: false,
        loading: false,
        id: '',
        form: {},
        formConfig: {
          splice: 2,
          labelWidth: '7em',
          layout: 'block',
          autoEmit: false,
          hideBtn: true,
          formInfo: [
            {
              title: '知识点名称',
              name: 'pointName',
              rules: { required: true, message: '请输入知识点名称' }
            },
            {
              title: '知识点内容',
              isFull: true,
              name: 'pointBiz',
              type: 'textarea',
            },
            {
              title: '教学文件',
              isFull: true,
              name: 'srFileList',
              type: 'file-upload',
              limit: 100,
              fileType: [],
              fileSize: 200
            }
          ]
        }
      }
    },
    methods: {
      create () {},
      show (id, act = 'edit') {
        this.id = id
        this.dia = true
        this.isEdit = act == 'edit'
        this.form = {
          pointBiz: '',
          srFileList: [],
          chapterId: this.chapterId
        }
        id && this.getInfo(id)
      },
      getInfo (id) {
        this.loading = true
        getPoints(id)
          .then(res => {
            this.form = res.data
          })
          .finally(() => {
            this.loading = false
          })
      },
      sub () {
        this.$refs.refsForm.subForm(form => {
          let act = this.form.pointId ? updatePoints : addPoints
          act(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
        })
      }
    }
  }
</script>
<style></style>
