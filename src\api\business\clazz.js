import request from '@/utils/request'

// 查询期班信息列表
export function listClazz(query) {
  return request({
    url: '/business/clazz/list',
    method: 'get',
    params: query
  })
}

// 查询期班信息详细
export function getClazz(calId) {
  return request({
    url: '/business/clazz/' + calId,
    method: 'get'
  })
}

// 新增期班信息
export function addClazz(data) {
  return request({
    url: '/business/clazz',
    method: 'post',
    data: data
  })
}

// 修改期班信息
export function updateClazz(data) {
  return request({
    url: '/business/clazz',
    method: 'put',
    data: data
  })
}

// 删除期班信息
export function delClazz(calId) {
  return request({
    url: '/business/clazz/' + calId,
    method: 'delete'
  })
}
