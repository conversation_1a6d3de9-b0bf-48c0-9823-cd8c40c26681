<template>
  <el-dialog v-model="dia" size="800px" append-to-body title="项目学生">
    <el-form ref="refsForm" v-model="form" :model="form" :rules="rules" inline>
      <el-form-item label="学生" prop="stuId">
        <cu-select
          v-model="form.stuId"
          :replace-fields="{ label: 'stuName', value: 'stuId' }"
          :query-info="`business/students/listAll`"
        />
      </el-form-item>
      <el-form-item label="项目分数" prop="stuScore">
        <el-input-number v-model="form.stuScore" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="add()">添加</el-button>
      </el-form-item>
    </el-form>
    <table-form v-bind="tableFormConfig" ref="tableForm" :key="t" />
    <template #footer>
      <el-button @click="dia = false">关闭</el-button>
      <!-- <el-button type="primary" @click="add">保存</el-button> -->
    </template>
  </el-dialog>
  <!--修改-->
  <el-dialog v-model="editDia" size="800px" append-to-body title="修改项目分数">
    <el-form ref="refsForm" v-model="editForm" :model="editForm" inline>
      <el-form-item label="学生">
        <cu-select
          v-model="editForm.stuId"
          :replace-fields="{ label: 'stuName', value: 'stuId' }"
          :query-info="`business/students/listAll`"
          disabled
        />
      </el-form-item>
      <el-form-item label="项目分数">
        <el-input-number v-model="editForm.stuScore" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="editDia = false">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
  import {
    expstudentList,
    expstudentAdd,
    expstudentEdit,
    expstudentRemove
  } from '@/api/business/experiment'

  export default {
    name: 'ExperimentDetail',
    emits: ['actSuccess'],
    data() {
      return {
        rules: {
          stuId: [{ required: true, message: '请选择学生', trigger: 'blur' }],
          stuScore: [{ required: true, message: '请填写学生分数' }]
        },
        dia: false,
        editDia: false,
        editForm: {},
        loading: false,
        id: '',
        form: {},
        tableFormConfig: {
          hideForm: true,
          searchConfig: {
            value: { expId: this.expId },
            formConfig: [{ title: '实验项目', name: 'expId', type: 'hidden' }],
            loadData: p => {
              return expstudentList(p)
            }
          },
          tableConfig: {
            cols: [
              {
                label: '学生',
                prop: 'stuName',
                minWidth: 200,
                renderFun: (h, { row }) => {
                  return h('span', row.srStudents.stuName || '')
                }
              },
              { label: '分数', prop: 'stuScore', minWidth: 200 },
              {
                label: '操作',
                actions: [
                  {
                    label: '删除',
                    permission: ['business:student:remove'],
                    icon: 'el_Delete',
                    click: row =>
                      expstudentRemove(row.sctId).then(res => {
                        this.$refs.tableForm.reload()
                      })
                  },
                  {
                    label: '修改',
                    icon: 'el_Edit',
                    click: row => {
                      this.editDia = true
                      this.editForm = row
                      console.log('row', row)
                    }
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      show(expId = '') {
        this.id = expId
        this.t = Date.now()
        this.dia = true
        this.tableFormConfig.searchConfig.value = {
          expId
        }
        this.form = {
          expId,
          stuScore: 0
        }
      },
      add() {
        this.$refs.refsForm.validate(valid => {
          if (!valid) return
          expstudentAdd(this.form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.$refs.tableForm.reload()
            this.form.expId = this.id
            this.form.stuScore = 0
            // this.form = {
            //   expId: this.id,
            //   stuScore: 0
            // }
          })
        })
      },
      sub() {
        this.dia = false
      },
      save() {
        let obj = {
          expId: this.editForm.expId,
          sctId: this.editForm.sctId,
          stuScore: this.editForm.stuScore
        }
        console.log('editForm', this.editForm)
        expstudentEdit(obj).then(res => {
          this.$modal.msgSuccess(res.msg)
          this.editDia = false
          this.$refs.tableForm.reload()
          // this.form = {
          //   expId: this.id,
          //   stuScore: 0
          // }
        })
      }
    }
  }
</script>
<style></style>
