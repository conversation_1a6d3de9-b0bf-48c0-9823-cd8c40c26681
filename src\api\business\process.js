import request from '@/utils/request'

// 查询实验进度记录列表
export function listProcess(query) {
  return request({
    url: '/business/process/list',
    method: 'get',
    params: query
  })
}

// 查询实验进度记录详细
export function getProcess(objId) {
  return request({
    url: '/business/process/' + objId,
    method: 'get'
  })
}

// 新增实验进度记录
export function addProcess(data) {
  return request({
    url: '/business/process',
    method: 'post',
    data: data
  })
}

// 修改实验进度记录
export function updateProcess(data) {
  return request({
    url: '/business/process',
    method: 'put',
    data: data
  })
}

// 删除实验进度记录
export function delProcess(objId) {
  return request({
    url: '/business/process/' + objId,
    method: 'delete'
  })
}
