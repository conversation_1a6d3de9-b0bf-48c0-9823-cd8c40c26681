<template>
  <div class="app-container">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      @selectionChange="rows => (selection = rows)"
    />
    <mtDetailInfo ref="refsMt" @actSuccess="getList()" />
    <c-import ref="refsImport" />
  </div>
</template>
<script>
  import * as mtApi from '@/api/business/teacher'
  import mtDetailInfo from './tmpInfo.vue'
  export default {
    name: 'Sctudents',
    components: { mtDetailInfo  },
    data() {
      return {
        selection: [],
        tableFormConfig: {
          title: '教师管理',
          searchConfig: {
            labelWidth: '6em',
            formConfig: [
              { title: '教师姓名', name: 'teaName' },
              { title: '手机号码', name: 'teaPhone' },
              {
                title: '入职时间',
                type: 'rangNum',
                name: 'teaIndate',
                min: 1949,
                max: new Date().getFullYear(),
                controls: false,
                placeholder: `开始年份,结束年份`,
                childName: ['start', 'end']
              }
            ],
            loadData: p => {
              p.start = p.start 
              p.end = p.end 
              return mtApi.listTeacher(p)
            }
          },
          actions: [
            {
              label: '新增',
              type: 'primary',
              icon: 'el_Plus',
              permission: ['business:teacher:add'],
              click: () => {
                this.$refs.refsMt.show()
              }
            },
            {
              label: '导出',
              icon: 'el_upload',
              type: 'warning',
              permission: ['business:teacher:export'],
              click: row =>
                this.download('business/teacher/export', {}, `教师管理_${new Date().getTime()}.xlsx`)
            },
            {
              type: 'danger',
              label: '删除',
              permission: ['business:teacher:remove'],
              disabled: () => !this.selection.length,
              icon: 'el_Delete',
              click: row => this.deleteAll()
            },
            {
             
              label: '导入',  
              type: 'info',       
              icon: 'el_download',
              click: row => {
                this.importFile()
              }
            }
          ],
          tableConfig: {
            isChecked: true,
            cols: [
              { label: '教师编号', prop: 'teaCode', width: 180 },
              { label: '教师姓名', prop: 'teaName', minWidth: 200 },
              {
                label: '教师性别',
                prop: 'teaSex',
                type: 'dict',
                width: 100,
                dictName: 'sys_user_sex'
              },
              { label: '手机号码', prop: 'teaPhone' },
              { label: '入职时间', prop: 'teaIndate' },
              { label: '专业', prop: 'teaMajor', type: 'dict', dictName: 'stu_major' },
              { label: '学历', prop: 'teaEducation', type: 'dict', dictName: 'teaching_education' },
              {
                label: '操作',
                type: 'action',
                width: '80',
                actions: [
                  {
                    title: '编辑',
                    icon: 'el_Edit',
                    permission: ['business:teacher:edit'],
                    click: row => this.$refs.refsMt.show(row.teaId)
                  },
                  {
                    title: '删除',
                    icon: 'el_Delete',
                    permission: ['business:teacher:remove'],
                    click: row => this.deleteAll(row.teaId)
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList(data = null) {
        this.$refs.refsTableForm.loadData(data)
      },
      //批量删除模板
      deleteAll(id = null) {
        if (!this.selection.length && !id) return this.$model.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选教师吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.teaId)
          mtApi.delTeacher(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.getList()
          })
        })
      },
      importFile(){
        this.$refs.refsImport.show({
         tmpUrl: '/business/teacher/excelTemplate',
         tmpName:'教师管理',
         uploadUrl: '/business/teacher/importData',
          ok: res => {
            this.getList()
          }
        })
      }
    }
  }
</script>
