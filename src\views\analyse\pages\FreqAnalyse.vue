<script setup name="SignalAnalyse">
  import DataChart from '@/components/DataChart' //频谱图组件
  import useChartsStore from '@/store/modules/charts'
  import useScanFormStore from '@/store/modules/form/scanForm'
  import useChartOptions from '@/common/hooks/chartOptions'
  import { numToPlot, plotToNum } from '@/utils/utils'
  import { round, cloneDeep } from 'lodash'
  import { batchFreqAnalysis } from '@/api/charts'
  import { exportToExcelWithCustomHeaders } from '@/utils/utils'

  const FreqLeftCom = defineAsyncComponent(() =>
    import('@/views/analyse/panels/scan/FreqLeftCom.vue')
  )
  const chartsStore = useChartsStore()
  const scanFormStore = useScanFormStore()
  const chartData = ref([])
  const config = ref({})
  const loaded = ref(false)
  const activeName = ref('first')
  const signResults = ref([])
  const filesList = ref([])

  // 字段名与中文名的映射表
  const headerMapping = {
    centerRate: '载频',
    bandwidth: '带宽',
    am: '幅度'
  }

  const updateChartData = data => {
    chartData.value = data
  }

  const maxXAxis = ref(null)
  const minXAxis = ref(null)

  const dealXAxis = () => {
    const tempArr = []
    filesList.value.forEach(item => {
      tempArr.push(
        plotToNum(item.centerFreqIn) - plotToNum(item.intermediateFrequencyBandwidth) / 2
      )
      tempArr.push(
        plotToNum(item.centerFreqIn) + plotToNum(item.intermediateFrequencyBandwidth) / 2
      )
    })
    maxXAxis.value = Math.max(...tempArr)
    minXAxis.value = Math.min(...tempArr)
  }

  /**
   * 生成配置对象
   * @returns 配置对象
   */
  const generateConfig = len => {
    const { chartOptions } = useChartOptions('spectrum')
    config.value = {
      type: 'static',
      chart: computed(() => {
        const options = cloneDeep(chartOptions.value)
        options.chart.height =
          document.body.clientHeight - 416 > 500 ? document.body.clientHeight - 516 : 500
        const left = minXAxis.value
        const axis = options.xAxis
        axis.max = len - 1
        axis.tickInterval = (len - 1) / 10
        axis.labels.formatter = function () {
          const val = (this.value / (len - 1)) * (maxXAxis.value - minXAxis.value) + left
          return numToPlot(val)
        }
        const yAxis = options.yAxis
        yAxis.max = scanFormStore.refLevel
        yAxis.min = scanFormStore.refLevel - scanFormStore.amScale * 10
        yAxis.tickInterval = scanFormStore.amScale
        yAxis.tickAmount = 11
        return options
      }),
      viewNum: 'all',
      start: computed(() => scanFormStore.startPoint),
      hideBar: true
    }
  }

  const freqAnalysis = async files => {
    const reqData = cloneDeep(files)
    reqData.forEach(item => {
      Object.keys(item).forEach(param => {
        if (
          [
            'centerFreqIn',
            'steplen',
            'bitRate',
            'samplingRate',
            'intermediateFrequencyBandwidth'
          ].includes(param)
        ) {
          item[param] = plotToNum(item[param] || '0')
        }
        if (param === 'uploadTime') {
          item[param] = item[param].replace(/[^0-9]/g, '')
        }
      })
    })
    await batchFreqAnalysis({
      specSignalList: reqData,
      overlapType: scanFormStore.overlapType
    }).then(res => {
      signResults.value = res.data.sig_results
        ? res.data.sig_results.map(item => {
            return {
              bandwidth: numToPlot(item.bw),
              centerRate: numToPlot(item.cf),
              am: round(item.amp, 2)
            }
          })
        : []
      updateChartData(res.data.valueList)
    })
    generateConfig(chartData.value.length)
    loaded.value = false
  }

  // 更新 chartData 并生成图表配置
  const init = async () => {
    loaded.value = true
    filesList.value = chartsStore.getFreqFiles()

    dealXAxis()
    await freqAnalysis(filesList.value)
  }

  const exportSignalSort = () => {
    exportToExcelWithCustomHeaders(signResults.value, headerMapping, '信号列表')
  }

  onMounted(async () => {
    await init()
  })
  onBeforeMount(() => {
    chartsStore.readStorageFreqFiles()
  })
  onUnmounted(() => {
    chartsStore.writeStorageFreqFiles()
  })
  onActivated(async () => {
    console.log('onActivated')
  })
  onDeactivated(() => {
    loaded.value = false
  })
  window.onunload = function () {
    chartsStore.writeStorageFreqFiles()
  }
</script>

<template>
  <div class="analyse-main">
    <div class="charts-body">
      <el-row style="height: 100%" :gutter="12">
        <!-- 左边表单，按钮触发改成参数触发 -->
        <el-col :span="6" class="form-main">
          <freq-left-com @analysis="freqAnalysis" />
        </el-col>
        <!-- 表格项 -->
        <el-col :span="18" class="chart-item" v-loading="loaded">
          <!-- 频谱图 -->
          <div class="chart-container" style="flex: 1">
            <data-chart
              v-if="config.chart"
              id="signalAnalyse"
              title="频谱图"
              :data="chartData"
              :config="config"
            />
          </div>
          <div class="picker-list">
            <el-tabs v-model="activeName" type="border-card" class="tabs">
              <el-tab-pane label="信号列表" name="first">
                <el-button @click="exportSignalSort">导出</el-button>
                <el-table ref="tableRef" border :data="signResults" height="300">
                  <el-table-column label="序号" type="index" width="60">
                    <template #default="{ $index }">
                      {{ $index + 1 }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="载频"
                    sortable
                    prop="centerRate"
                    :sort-by="['cf', 'bw']"
                  />
                  <el-table-column label="带宽" sortable prop="bandwidth" :sort-by="['bw', 'cf']" />
                  <el-table-column label="幅度" sortable prop="am" />
                  <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                      <el-button size="small" @click="pick(row)">分析</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped lang="scss">
  .analyse-main {
    height: 100%;
    display: flex;
    flex-direction: column;
    .charts-body {
      flex: 1;
      .show-table {
        height: 100%;
        border-right: 1px solid grey;
      }
      .form-main {
        box-shadow: 0px 0px 1rem 0px rgba(82, 137, 136, 0.08);
        padding: 12px;
        .file-name {
          display: flex;
          align-items: center;
          font-size: 20px;
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          padding-bottom: 16px;
          white-space: nowrap;
        }
      }
    }
  }
  .file-wrapper {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }
</style>
