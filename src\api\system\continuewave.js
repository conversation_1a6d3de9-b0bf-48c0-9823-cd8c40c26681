import request from '@/utils/request'
// 新增continueWave配置
export function addcontinueWave(data) {
  return request({
    url: '/continueWave/add',
    method: 'post',
    data: data
  })
}

// 删除参数配置
export function delcontinueWave(ids) {
  return request({
    url: '/continueWave/' + ids,
    method: 'delete'
  })
}

// 修改参数配置
export function updatecontinueWave(data) {
  return request({
    url: '/continueWave/edit',
    method: 'put',
    data: data
  })
}

// 查询continueWave列表
export function listcontinueWave(query) {
  return request({
    url: '/continueWave/list',
    method: 'get',
    params: query
  })
}