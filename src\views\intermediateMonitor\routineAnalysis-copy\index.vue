<template>
  <div key="monitor-container" class="online-container app-container">
    <el-row key="monitor-main" class="main">
      <el-col :span="18" class="chart-info">
        <ScanSection>
          <div class="charts">
            <AnalyseCharts
              key="monitor-chart"
              usekey="monitor"
              view-type="monitor"
              :data-list="dataList"
              :model="scanStore.monitor"
            />
          </div>
          <div class="tabs">
            <BottomSignalTabs :signal-list="signalList" />
          </div>
        </ScanSection>
      </el-col>
      <el-col :span="6" class="chart-form">
        <ScanSection class="charts">
          <div class="form">
            <RightSettings :model="scanStore.monitor" @start="startWebsocket" @stop="closeScan">
              <template #default>
                <FreBandSetting v-if="!scanStore.monitor.type" />
                <ScatterSetting v-else />
              </template>
            </RightSettings>
          </div>
        </ScanSection>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="SignalMonitor">
  import ScanSection from '@/components/SpectrumSignal/ScanSection.vue'
  import AnalyseCharts from '@/views/analyseCharts'
  import FreBandSetting from './rightSettings/FreBandSetting.vue'
  import ScatterSetting from './rightSettings/ScatterSetting.vue'
  import RightSettings from '@/components/SettingsForm'
  import BottomSignalTabs from './bottomSignalTabs'
  import useScanStore from '@/store/modules/scanMonitor'
  import useLinkWsHook from '@/common/hooks/linkWs'
  import { RADAR_MONITOR_CODE, SIGNAL_MONITOR_CODE } from '@/constant/funCodes'

  const scanStore = useScanStore()
  const taskFunCode = computed(() => {
    const { type, commandType } = scanStore.monitor
    if (commandType && type) {
      // 雷达测向
      return null
    } else if (!commandType && !type) {
      // 信号分析
      return SIGNAL_MONITOR_CODE
    } else if (commandType && !type) {
      // 信号测向
      return null
    } else {
      return RADAR_MONITOR_CODE
    }
  })
  const { linkScan, closeScan, pageData } = useLinkWsHook(scanStore.monitor, taskFunCode)
  const dataList = computed(() => {
    let initData = {
      current: [],
      average: [],
      max: [],
      min: [],
      limit: [],
      occupancy: []
    }
    if (scanStore.playFfts.length > 0 && scanStore.monitor.status === 0) {
      initData.current = scanStore.playFfts
    }
    if (!pageData.value) {
      return initData
    }
    if (taskFunCode.value === RADAR_MONITOR_CODE && pageData.value.radarSignalMonitoringResults) {
      const radarData = pageData.value.radarSignalMonitoringResults
      initData = {
        current: radarData.spectralData,
        average: radarData.average || [],
        max: radarData.maximum || [],
        min: radarData.minimum || [],
        limit: radarData.threshold || [],
        occupancy: radarData.occupancy || []
      }
    }
    if (taskFunCode.value === SIGNAL_MONITOR_CODE && pageData.value.specificSignal) {
      initData.current = pageData.value.specificSignal.spectralData
    }
    return initData
  })
  const signalList = computed(() => {
    if (!pageData.value) {
      return []
    }
    if (taskFunCode.value === RADAR_MONITOR_CODE && pageData.value.radarSignalMonitoringResults) {
      const radarData = pageData.value.radarSignalMonitoringResults
      return radarData.signalsFromRsList
    }
    if (taskFunCode.value === SIGNAL_MONITOR_CODE && pageData.value.specificSignal) {
      const {
        signalType,
        signalFrequency,
        bandwidth1,
        modulation,
        step,
        subcarriers,
        waveForm,
        codeRate
      } = pageData.value.specificSignal
      return [
        {
          signalType,
          signalFrequency,
          bandwidth1,
          modulation,
          step,
          subcarriers,
          waveForm,
          codeRate
        }
      ]
    }
    return []
  })
  const generateParams = form => {
    const { centerFreq, bandwidth, startFre, endFre } = scanStore.monitor
    if (taskFunCode.value === SIGNAL_MONITOR_CODE) {
      return {
        centerFrequency: centerFreq,
        analyseBandwidth: bandwidth,
        signalType: 0
      }
    } else if (taskFunCode.value === RADAR_MONITOR_CODE) {
      return {
        startFrequency: startFre,
        endFrequency: endFre,
        stayTime: form.stayTime
      }
    }
  }
  const startWebsocket = form => {
    const params = generateParams(form)
    linkScan(params)
  }
</script>

<style scoped lang="scss">
  @import '@/assets/styles/mixin.scss';

  .online-container {
    @include boxBg('@/assets/images/specscanbg.png');
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 1rem;
    overflow-y: auto;

    .main {
      height: 100%;
    }
  }

  .chart-form {
    padding-left: 10px;
  }
</style>
