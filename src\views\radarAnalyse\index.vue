<template>
  <div class="online-container app-container">
    <el-row class="main">
      <el-col :span="18" class="chart-info">
        <ScanSection>
          <div class="charts">
            <AnalyseCharts
              view-type="radar"
              :model="scanStore.radar"
              :data-list="{ current: fftList }"
              usekey="radar"
            >
              <template #radar="{ showLines, selectedFremarkers, isActive, isTopFollow }">
                <!-- <Cycle 
                  :show-lines="showLines"
                  :selected-fremarkers="selectedFremarkers"
                  :is-active="isActive"
                  :is-top-follow="isTopFollow"
                  :data-list="cycleList"
                /> -->
                <Fft
                  class="mt-2"
                  :show-lines="showLines"
                  :selected-fremarkers="selectedFremarkers"
                  :is-active="isActive"
                  :is-top-follow="isTopFollow"
                  :data-list="fftList"
                />
              </template>
              <template #default>
                <div class="pl-4" style="width: 280px;">
                  <Radar id="radar-top" :data="truthDirList" />
                  <Radar id="radar-btm" class="mt-6" :data="carDirList" />
                </div>
              </template>
            </AnalyseCharts>
          </div>
          <div class="tabs">
            <BottomSignalTabs :table-list="tableList" />
          </div>
        </ScanSection>
      </el-col>
      <el-col :span="6" class="chart-form">
        <ScanSection class="charts">
          <div class="form">
            <RightSettings :model="scanStore.radar" @start="startWebsocket" @stop="closeScan">
              <template #default>
                <FreBandSetting />
              </template>
            </RightSettings>
          </div>
        </ScanSection>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="ScanRadarAnalyse">
import ScanSection from '@/components/SpectrumSignal/ScanSection.vue'
import AnalyseCharts from '@/views/analyseCharts'
import RightSettings from '@/components/SettingsForm'
import BottomSignalTabs from './bottomSignalTabs'
import FreBandSetting from './rightSettings/FreBandSetting.vue'
import Cycle from './charts/Cycle.vue'
import Fft from './charts/FFT.vue'
import Radar from './charts/Radar.vue'
import useScanStore from '@/store/modules/scanMonitor'
import useLinkWsHook from '@/common/hooks/linkWs'
import { RADAR_ANALYSE_CODE } from '@/constant/funCodes'

const scanStore = useScanStore()
const { north, car, am } = scanStore.radar.directionAmplitudes
const fftList = ref([])
const tableList = ref([])
const taskFunCode = computed(() => {
  return RADAR_ANALYSE_CODE
})
const truthDirList = computed(() => {
  const dirList = handleDirectData(north)
  return dirList.map((item, index) => {
    return [item, am[index]]
  })
})
const carDirList = computed(() => {
  const dirList = handleDirectData(car)
  return dirList.map((item, index) => {
    return [item, am[index]]
  })
})
const { linkScan, closeScan, pageData } = useLinkWsHook(scanStore.radar, taskFunCode)

watch(() => pageData.value, (data) => {
  if (data && data.radarPulseSignal) {
    fftList.value = data.radarPulseSignal.spectralData
    tableList.value = data.radarPulseSignal.pulseSortingRecordList
  }
})

const handleDirectData = (list) => {
  const midIndex = Math.floor(list.length / 2)
  if (list[midIndex] < 0) {
    list = list.map(item => item * -1 * 3.6)
  } else if (list[midIndex] > 360) {
    list = list.map(item => item / 100)
  }
  return list
}

const generateParams = (form) => {
  const { startFre, endFre } = scanStore.radar
  return {
    dataAcquisitionSwitch: 1,
    startFrequency: startFre,
    endFrequency: endFre,
  }
}
const startWebsocket = (form) => {
  const params = generateParams(form)
  linkScan(params)
}
</script>
<style scoped lang="scss">
@import "@/assets/styles/mixin.scss";

.online-container {
  @include boxBg('@/assets/images/specscanbg.png');
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  padding: 1rem;
  overflow-y: auto;

  .main {
    height: 100%;
  }
}

.chart-form {
  padding-left: 10px;
}
</style>