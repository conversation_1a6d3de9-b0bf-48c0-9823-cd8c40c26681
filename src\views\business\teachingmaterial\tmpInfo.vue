<template>
  <el-dialog
    :key="dia"
    v-model="dia"
    width="800px"
    append-to-body
    :title="id ? '教材信息' : '新增教程'"
  >
    <c-form
      v-bind="formConfig"
      :key="form.boId + '' + dia"
      ref="refsForm"
      v-model="form"
      v-loading="loading"
      class="mr-4"
    />

    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button type="primary" @click="sub">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
  import { getBook, addBook, updateBook } from '@/api/business/book'
  export default {
    emits: ['actSuccess', 'close'],
    data () {
      return {
        dia: false,
        loading: false,
        id: '',
        form: {},
        formConfig: {
          splice: 2,
          labelWidth: '8em',
          layout: 'block',
          autoEmit: false,
          hideBtn: true,
          value: {},
          formInfo: [
            // {
            //   title: '课程编码',
            //   name: 'courCode',
            //   rules: { required: true, message: '请输入课程编码' }
            // },
            // {
            //   title: '课程名称',
            //   name: 'courName',
            //   rules: { required: true, message: '请输入课程名称' }
            // },
            {
              title: '教材名称',
              name: 'boName',
              rules: { required: true, message: '请输入课程教材' }
            },
            // {
            //   title: '教材名称',
            //   name: 'BO_ID',
            //   type: 'cuselect',
            //   queryInfo: '/business/book/selectBook?pageNum=1&pageSize=100',
            //   replaceFields: { label: 'BO_NAME', value: 'BO_ID' },
            //   rules: { message: '请选择教材名称', required: true }
            // },

            { title: '教材出版社', name: 'boPublish' },
            { title: '教材编写人', name: 'boWriter' },
            { title: '教材年份', name: 'boYear' },
            { title: '教材版本', name: 'boVersion' }
            // {
            //   title: '课程授课老师',
            //   name: 'teaIds',
            //   type: 'cuselect',
            //   queryInfo: 'business/teacher/list?pageNum=1&pageSize=100',
            //   multi: true,
            //   replaceFields: { label: 'teaName', value: 'teaId' },
            //   rules: { message: '请选择课程授课老师', required: true }
            // },
            // { title: '课程简介', isFull: true, name: 'courBiz', type: 'textarea' }
          ]
        }
      }
    },
    methods: {
      show (id) {
        this.id = id
        this.dia = true
        this.form = { boId: '' }
        id && this.getInfo(id)
      },
      getInfo (id) {
        this.loading = true
        getBook(id)
          .then(res => {
            // res.data.teaIds = res.data.srCourseTeacherList.map(v => v.teaId)
            this.form = res.data
          })
          .finally(() => {
            this.loading = false
          })
      },
      sub () {
        this.$refs.refsForm.subForm(form => {
          let act = this.form.boId ? updateBook : addBook
          // form.srCourseTeacherList = form.teaIds.map(v => {
          //   return { boId: this.form.boId, teaId: v }
          // })
          act(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
        })
      }
    }
  }
</script>
<style></style>
