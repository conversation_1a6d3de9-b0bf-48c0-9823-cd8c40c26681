<template>
  <el-dialog v-model="dia" title="资源报修" width="600px" append-to-body>
    <el-form ref="formRef" v-loading="loading" label-position="top" :model="form">
      <el-form-item
        label="故障现象"
        prop="repairBiz"
        :rules="[{ required: true, message: '请输入故障现象' }]"
      >
        <el-input
          v-model="form.repairBiz"
          type="textarea"
          show-word-limit
          maxlength="500"
          rows="6"
        />
        <!-- <Editor v-model="form.repairBiz" placeholder="请输入故障现象" /> -->
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel(false)">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
  import * as resApi from '@/api/business/resources'
  export default {
    name: 'ResourceDetail',
    emits: ['actSuccess'],
    data() {
      return {
        form: {},
        title: '',
        dia: false,
        loading: false,
        cb: null,
        resid: ''
      }
    },
    methods: {
      show(id, cb = null) {
        this.cb = cb || null
        this.dia = true
        this.form = {
          repairBiz: '',
          repairResCode: id
        }
      },
      // 取消按钮
      cancel(t = false) {
        this.dia = false
        t && this.$emit('actSuccess')
        this.form = {}
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.formRef.validate(valid => {
          if (valid) {
            this.loading = true
            resApi
              .reportResources(this.form)
              .then(response => {
                this.$modal.msgSuccess('操作成功')
                this.cancel(true)
              })
              .finally(() => {
                this.loading = false
              })
          }
        })
      }
    }
  }
</script>
