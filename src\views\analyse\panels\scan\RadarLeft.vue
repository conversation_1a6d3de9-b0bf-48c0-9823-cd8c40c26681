<template>
  <el-collapse v-model="activeItems" class="collapse-panels">
    <el-collapse-item
      v-for="panel in panels"
      :key="panel.value"
      :title="panel.title"
      :name="panel.value"
    >
      <component :is="dom[panel.value]" @operation="emit('filter')" />
    </el-collapse-item>
  </el-collapse>
</template>

<script setup name="LeftForm">

import SignalRadarParams from './SignalRadarParams.vue'
import RefLine from './RefLine.vue'
import RadarDownConversion from './RadarDownConversion.vue'
import RadarFiltering from './RadarFiltering.vue'
import RadarChoose from './RadarChoose.vue'

const emit = defineEmits('filter')
const panels = [
  {
    title: '信号文件参数',
    value: 'SignalRadarParams',
  },
  {
    title: '下变频',
    value: 'RadarDownConversion'
  },
  {
    title: '滤波',
    value: 'RadarFiltering'
  },
  {
    title: '信号分选',
    value: 'RadarChoose'
  },
]

const dom = shallowReactive({
  SignalRadarParams, RefLine,RadarDownConversion, RadarFiltering,RadarChoose
})

const activeItems = ref(panels.map(panel => panel.value))

</script>

<style scoped lang="scss">
.panels-leave-active,
.panels-enter-active {
  transition: all 0.8s ease-in-out;
}

.panels-enter-from {
  transform: translateY(-200px);
  opacity: 0;
}
.panels-leave-to {
  transform: translateY(-200px);
  opacity: 0;
}
</style>
