import { onActivated, onDeactivated } from 'vue'
import { linkWs } from '@/api/upperComputer/scan'
import { DISPERSED_CODE, SIGNAL_MONITOR_CODE } from '@/constant/funCodes'
import useScanStore from '@/store/modules/scanMonitor'
import { settings } from '@/utils/settings'

export default function () {
  let ws = null
  let paramsForm = {}
  const pageData = ref({})
  /**
   * 链接扫描函数
   * @param params 扫描参数
   * @param host 扫描主机地址，可选参数，默认为全局配置中的地址
   * @param port 扫描端口，可选参数，默认为全局配置中的端口
   * @param spectrum pinia中的状态对象，用于控制扫描状态
   */
  const linkScan = async (params, taskFunCode, host, port, spectrum, deviceId, deviceCode) => {
    console.log(spectrum, '=====spectrum=====');
    useScanStore().playFfts = []
    if (ws) {
      ws.close()
    }
    paramsForm = params
    ws = await linkWs({
      host: host ? host : settings.VITE_GLOB_HOST,
      port: port ? port : settings.VITE_GLOB_PORT,
      taskFunCode: taskFunCode,
      sendTime: 10,
      deviceCode: deviceCode,
      data: encodeURIComponent(JSON.stringify(params))
    })
    spectrum.status = 1

    ws.onmessage = event => {
      try {
        if (!pageData.value) {
          console.log(event.data)
        }
        const { data } = JSON.parse(event.data)
        pageData.value[deviceId] = data.result;
        console.log(pageData.value);

      } catch (err) {
        console.log(err)
      }
    }
  }
  const closeScan = (spectrum) => {
    console.log("关闭扫描");
    ws && ws.close()
    ws = null
    spectrum.status = 0

  }
  onActivated(async () => {
    if (ws) {
      await linkScan(paramsForm)
    }
  })
  onDeactivated(() => {
    ws && ws.close()
    useScanStore().playFfts = []
  })
  return {
    linkScan,
    closeScan,
    pageData
  }
}
