import colorSettings from './theme'

export default {
  chart: {
    zoomType: 'x',
    backgroundColor: colorSettings.chartBgColor,
    polar: true,
    type: 'line',
    height: 800,
    spacingTop: 30,
    spacingBottom: 30,
    spacingLeft: 10,
    spacingRight: 20
  },
  exporting: {
    enabled: false
  },
  reflow: true,
  credits: {
    // 版权
    enabled: false
  },
  xAxis: {
    labels: {
      style: {
        color: colorSettings.labelColor
      },
      formatter() {
        return ''
      }
    },
    min: 0,
    max: 10000,
    gridLineWidth: 1,
    tickAmount: 11,
    showLastLabel: true,
    gridLineColor: colorSettings.gridLineColor,
    gridLineDashStyle: 'dash',
    lineColor: colorSettings.axisLineColor,
    showFirstLabel: true
  },
  yAxis: {
    title: {
      enabled: false
    },
    gridLineColor: colorSettings.gridLineColor,
    gridLineDashStyle: 'dash',
    lineColor: colorSettings.axisLineColor,
    tickAmount: 11,
    min: -120,
    max: -20,
    labels: {
      style: {
        color: colorSettings.labelColor
      }
    }
  },
  title: {
    enabled: false,
    text: ''
  },
  boost: {
    useGPUTranslations: true
  },
  legend: {
    enabled: false
  },
  series: [
    {
      color: colorSettings.lineColor,
      marker: {
        enabled: false
      },
      animation: false,
      enableMouseTracking: false,
      type: 'line',
      data: [],
      lineWidth: 0.5
    }
  ]
}
