<template>
  <div class="gather max-h-[270px] h-[270px]">
    <GatherItem />
    <!-- <el-tabs tab-position="left">
      <el-tab-pane>
        <template #label>
          <el-button class="tabs-button" style="height: 40px;">按时间</el-button>
        </template>
        <GatherItem />
      </el-tab-pane>
      <el-tab-pane>
        <template #label>
          <el-button class="tabs-button" style="height: 40px;">按次数</el-button>
        </template>
        <GatherItem type="num" />
      </el-tab-pane>
      <el-tab-pane>
        <template #label>
          <el-button class="tabs-button" style="height: 40px;">按信道</el-button>
        </template>
        <GatherItem type="channel" />
      </el-tab-pane>
    </el-tabs> -->
  </div>
</template>

<script setup>
  import GatherItem from './GatherItem.vue'
</script>
<style scoped lang="scss">
  .gather {
    height: 300px;
  }
</style>
