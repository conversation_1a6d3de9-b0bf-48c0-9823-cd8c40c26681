<template>
  <div>
    <el-form class="left">
      <el-form-item label="频率">
        <el-input v-model="form.centerFreq" type="number" @change="dataChangeFun('centerFreq')">
          <template #append>
            <el-select v-model="selectForm.centerFreq" @change="dataChangeFun('centerFreq')">
              <el-option v-for="unit in units" :key="unit" :value="unit" :label="unit" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="带宽">
        <el-input v-model="form.bandwidth" type="number" @change="dataChangeFun('bandwidth')">
          <template #append>
            <el-select v-model="selectForm.bandwidth" @change="dataChangeFun('bandwidth')">
              <el-option v-for="unit in units" :key="unit" :value="unit" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col :span="4">
        <el-button class="content-button" @click="addItem">添加</el-button>
        <span class="content-font">频率|带宽</span>
        <el-button class="content-button" @click="clear">清空</el-button>
        <el-button class="content-button" @click="save">保存</el-button>
      </el-col>
      <el-col :span="20">
        <textarea v-model="freList" class="content-textarea" readonly rows="7" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import { ref, watch, onMounted, getCurrentInstance } from 'vue'
  import { numToPlot, plotToNum } from '@/utils/utils'
  import { update } from '@/utils/instance'
  import useScanStore from '@/store/modules/scanMonitor'

  const units = ['GHz', 'MHz', 'KHz', 'Hz']
  const scanStore = useScanStore()

  const spectrum = scanStore.spectrumCopy
  const freList = ref('')
  const form = ref(spectrum)
  const selectForm = ref({
    centerFreq: 'Hz',
    bandwidth: 'Hz'
  })

  const dealFreList = () => {
    if (spectrum.type === 1) {
      freList.value =
        (spectrum?.freqBandStr ?? '')
          .split(',')
          .filter(item => item) // 过滤掉空字符串
          .map(item => {
            const [centerFreq, bandwidth] = item.split('|')
            const formattedCenterFreq = centerFreq ? numToPlot(centerFreq) : ''
            const formattedBandwidth = bandwidth ? numToPlot(bandwidth) : ''
            return `${formattedCenterFreq}|${formattedBandwidth}`
          })
          .join('\n') + '\n'
    } else {
      freList.value = ''
    }
  }
  dealFreList()

  /**
   * 数据变更处理函数
   * @param key 字符串类型，表示要处理的字段的键名
   */
  const dataChangeFun = key => {
    if (key) {
      spectrum[key] = plotToNum(form.value[key] + selectForm.value[key])
      formatValue(key, spectrum[key])
      return
    }
    Object.keys(selectForm.value).forEach(key => {
      formatValue(key, spectrum[key])
    })
  }

  const formatValue = (key, value) => {
    const newFormVal = numToPlot(value)
    if (newFormVal.search('KHz') != -1) {
      form.value[key] = newFormVal.replace('KHz', '')
      selectForm.value[key] = 'KHz'
    } else if (newFormVal.search('MHz') != -1) {
      form.value[key] = newFormVal.replace('MHz', '')
      selectForm.value[key] = 'MHz'
    } else if (newFormVal.search('GHz') != -1) {
      form.value[key] = newFormVal.replace('GHz', '')
      selectForm.value[key] = 'GHz'
    }
    spectrum[key + 'Unit'] = selectForm.value[key]
  }

  const addItem = () => {
    const centerFreq = form.value.centerFreq + selectForm.value.centerFreq
    const bandwidth = form.value.bandwidth + selectForm.value.bandwidth
    const itemStr = `${centerFreq}|${bandwidth}\n`
    if (freList.value.includes(itemStr)) {
      return
    }
    freList.value += itemStr
    // console.log(freList.value)
    const items = []
    freList.value.split('\n').forEach(item => {
      if (!item) {
        return
      }
      const [centerFreq, bandwidth] = item.split('|')
      items.push({ frequency: plotToNum(centerFreq), analysisBandwidth: plotToNum(bandwidth) })
    })
    spectrum.discrete = items
    spectrum.freqBandStr += itemStr
  }

  const clear = () => {
    freList.value = ''
    spectrum.discrete = []
    spectrum.freqBandStr = ''
  }

  const save = () => {
    const items = []
    spectrum.discrete = []
    freList.value.split('\n').forEach(item => {
      if (!item) {
        return
      }
      const [centerFreq, bandwidth] = item.split('|')
      items.push({ frequency: plotToNum(centerFreq), analysisBandwidth: plotToNum(bandwidth) })
    })
    spectrum.discrete = items
    spectrum.freqBandStr = freList.value.trim()
  }

  // const getBreakRange = (cf, bw) => {
  //   const bit = scanStore.spectrum.bandwidth / scanStore.resultLen
  //   const startNum = Math.floor((cf - bw / 2 - scanStore.spectrum.startFre) / bit)
  //   const endNum = Math.floor((cf + bw / 2 - scanStore.spectrum.startFre) / bit)
  //   return { startNum, endNum }
  // }

  // // 监听 spectrum 的变化
  // watch(
  //   spectrum,
  //   newVal => {
  //     form.value.centerFreq = newVal.centerFreq
  //     form.value.bandwidth = newVal.bandwidth
  //   },
  //   { deep: true, immediate: true }
  // )

  onMounted(() => {
    dataChangeFun()
  })

  defineExpose({
    selectForm,
    dataChangeFun
  })
</script>

<style scoped>
  .content-button {
    width: 85%;
    margin: 12px 2px;
  }

  .content-font {
    color: var(--scan-text-color);
  }

  .content-textarea {
    width: 79%;
    margin: 13px 8px;
    background: #01171e;
    border: 1px solid #00bfbf;
    color: var(--scan-text-color);
    padding: 0.5rem 1rem;
    resize: none;
  }
</style>
