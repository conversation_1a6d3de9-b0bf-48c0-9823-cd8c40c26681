import { defineStore } from 'pinia'
import { cloneDeep, round } from 'lodash'

const useFileManageStore = defineStore('filemanage', {
  state: () => ({
    // 文件信息
    file: {
      bitRate: '',
      centerFreqIn: '', // 中心频率
      dataOrganization: '',
      dataType: '',
      debugMode: '',
      fftSize: '',
      fileName: '',
      startTime: '',
      endTime: '',
      fileType: '',
      id: '',
      intermediateFrequencyBandwidth: '',
      samplingRate: '',
      uploadTime: ''
    }
  }),
  getters: {
    // 总周期
    totalCycle() {
      const { IQNum, simpleRate } = this.settings
      return round((1e6 * IQNum) / simpleRate, 2)
    },
    // 一帧的点数
    viewNum() {
      const { IQNum } = this.settings
      const { swpTime } = this.global
      return round(IQNum * (swpTime / this.totalCycle))
    }
  },
  actions: {
    setFileQueryInfo(info, path) {
      console.log(info, path)
      this.file = cloneDeep(info)
      this.file.startTime = info.value.startTime
      this.file.endTime = info.value.endTime
      this.file.fileName = info.fileName
    },
    getFileQueryInfo() {
      console.log(2)
      // return this.file
    }
  }
})

export default useFileManageStore
