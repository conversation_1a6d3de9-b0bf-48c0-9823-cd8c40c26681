<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="8em"
    >
      <el-form-item label="课程名称" prop="curriculumName">
        <el-input
          v-model="queryParams.curriculumName"
          placeholder="请输入课程名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学期" prop="stSemester">
        <el-input
          v-model="queryParams.stSemester"
          placeholder="请输入学期"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开课班级" prop="stClass">
        <el-input
          v-model="queryParams.stClass"
          placeholder="请输入开课班级"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课程教师" prop="teaId">
        <el-input
          v-model="queryParams.teaId"
          placeholder="请输入课程教师"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开课时间" prop="stTime">
        <el-date-picker
          v-model="queryParams.stTime"
          clearable
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择开课时间"
        />
      </el-form-item>
      <el-form-item label="开课地点" prop="stAddress">
        <el-input
          v-model="queryParams.stAddress"
          placeholder="请输入开课地点"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:starts:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:starts:edit']"
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:starts:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:starts:export']"
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar v-model:show-search="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="startsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="课程名称" align="center" prop="curriculumName" />
      <el-table-column label="学期" align="center" prop="stSemester" />
      <el-table-column label="开课班级" align="center" prop="stClass" />
      <el-table-column label="课程教师" align="center" prop="teaId" />
      <el-table-column label="开课时间" align="center" prop="stTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.stTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开课地点" align="center" prop="stAddress" />
      <el-table-column label="课程介绍" align="center" prop="courseIntroduce" />
      <el-table-column label="状态" align="center" prop="status" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" fixed="right">
        <template #default="scope">
          <el-button
            v-hasPermi="['business:starts:edit']"
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['business:starts:remove']"
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改开课对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课程名称" prop="curriculumName">
          <el-input v-model="form.curriculumName" placeholder="请输入课程名称" />
        </el-form-item>
        <el-form-item label="学期" prop="stSemester">
          <el-input v-model="form.stSemester" placeholder="请输入学期" />
        </el-form-item>
        <el-form-item label="开课班级" prop="stClass">
          <el-input v-model="form.stClass" placeholder="请输入开课班级" />
        </el-form-item>
        <el-form-item label="课程教师" prop="teaId">
          <el-input v-model="form.teaId" placeholder="请输入课程教师" />
        </el-form-item>
        <el-form-item label="开课时间" prop="stTime">
          <el-date-picker
            v-model="form.stTime"
            clearable
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择开课时间"
          />
        </el-form-item>
        <el-form-item label="开课地点" prop="stAddress">
          <el-input v-model="form.stAddress" placeholder="请输入开课地点" />
        </el-form-item>
        <el-form-item label="课程介绍" prop="courseIntroduce">
          <el-input v-model="form.courseIntroduce" placeholder="请输入课程介绍" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { listStarts, getStarts, delStarts, addStarts, updateStarts } from '@/api/business/starts'

  export default {
    name: 'Starts',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 开课表格数据
        startsList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          curriculumId: null,
          curriculumName: null,
          stSemester: null,
          stClass: null,
          teaId: null,
          stTime: null,
          stAddress: null,
          courseIntroduce: null,
          status: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {}
      }
    },
    created() {
      this.getList()
    },
    methods: {
      /** 查询开课列表 */
      getList() {
        this.loading = true
        listStarts(this.queryParams).then(response => {
          this.startsList = response.rows
          this.total = response.total
          this.loading = false
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          stId: null,
          curriculumId: null,
          curriculumName: null,
          stSemester: null,
          stClass: null,
          teaId: null,
          stTime: null,
          stAddress: null,
          courseIntroduce: null,
          status: '0',
          delFlag: null,
          createBy: null,
          createTime: null,
          updateBy: null,
          updateTime: null,
          remark: null
        }
        this.resetForm('form')
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryForm')
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.stId)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset()
        this.open = true
        this.title = '添加开课'
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset()
        const stId = row.stId || this.ids
        getStarts(stId).then(response => {
          this.form = response.data
          this.open = true
          this.title = '修改开课'
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.stId != null) {
              updateStarts(this.form).then(response => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
            } else {
              addStarts(this.form).then(response => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const stIds = row.stId || this.ids
        this.$modal
          .confirm('是否确认删除开课编号为"' + stIds + '"的数据项？')
          .then(function () {
            return delStarts(stIds)
          })
          .then(() => {
            this.getList()
            this.$modal.msgSuccess('删除成功')
          })
          .catch(() => {})
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download(
          'business/starts/export',
          {
            ...this.queryParams
          },
          `starts_${new Date().getTime()}.xlsx`
        )
      }
    }
  }
</script>
