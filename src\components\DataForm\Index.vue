<template>
  <!-- 渲染表单的占位符组件 -->
  <render-form />
</template>

<script setup>
  // 引入 Vue 的核心 API 和 Element Plus 组件
  import { h } from 'vue'
  import {
    ElInput,
    ElForm,
    ElFormItem,
    ElCheckbox,
    ElButton,
    ElSelect,
    ElOption,
    ElRadio,
    ElRadioGroup
  } from 'element-plus'
  // 定义组件的 props，接收外部传入的字段配置、模型数据和验证规则
  const props = defineProps({
    fields: {
      type: Array,
      default: () => [] // 默认是一个空数组
    },
    model: {
      type: Object,
      default: () => ({}) // 默认是一个空对象
    },
    rules: {
      type: Array,
      default: () => [] // 默认是一个空数组
    }
  })

  // 当表单项发生变化时触发，更新模型数据并调用自定义变化事件
  const changeEvent = item => {
    props.model.setVal(item.target, getValue(props.model, item.target))
    item.change && item.change(props.model[item.target])
  }

  // 设置模型中的值，如果目标字段存在则更新其值
  const setValue = (obj, target, val) => {
    if (obj[target] === undefined) {
      return // 如果目标字段不存在，直接返回
    }
    if (obj[target].value === undefined) {
      obj[target] = val // 如果字段不是 ref 对象，直接赋值
    } else {
      obj[target].value = val // 如果是 ref 对象，更新其 value 属性
    }
  }

  // 获取模型中的值，支持简单值和 ref 对象
  const getValue = (obj, target) => {
    if (obj[target] === undefined) {
      return // 如果目标字段不存在，直接返回 undefined
    }
    if (obj[target].value === undefined) {
      return obj[target] // 如果字段不是 ref 对象，返回值本身
    } else {
      return obj[target].value // 如果是 ref 对象，返回其 value 属性
    }
  }

  // 根据字段类型动态生成表单项组件
  const generateCurrent = field => {
    if (field.isCheckbox) {
      // 如果是复选框
      return h(ElCheckbox, {
        modelValue: getValue(props.model, field.target), // 绑定复选框的值
        disabled: field.disabled, // 是否禁用
        'onUpdate:modelValue'(val) {
          setValue(props.model, field.target, val) // 更新模型值
        },
        onChange: () => changeEvent(field), // 触发变化事件
        label: '启用' // 复选框的标签
      })
    } else if (field.isSelect) {
      // 如果是下拉选择框
      return h(
        ElSelect,
        {
          modelValue: getValue(props.model, field.target), // 绑定选择框的值
          disabled: field.disabled, // 是否禁用
          'onUpdate:modelValue'(val) {
            setValue(props.model, field.target, val) // 更新模型值
          },
          onChange: () => changeEvent(field) // 触发变化事件
        },
        {
          default: () =>
            field.options.map(op =>
              h(ElOption, {
                key: op.value || op, // 选项的 key
                value: op.value || op, // 选项的值
                label: op.label || op // 选项的显示标签
              })
            ),
          append: () =>
            field.appendBtnText
              ? h(
                  ElButton,
                  {
                    onClick: () => field.clickEvent // 按钮的点击事件
                  },
                  { default: () => [field.appendBtnText] } // 按钮显示的文本
                )
              : ''
        }
      )
    } else if (field.isRadio) {
      // 如果是单选框
      return h(
        ElRadioGroup,
        {
          modelValue: getValue(props.model, field.target),
          disabled: field.disabled,
          'onUpdate:modelValue'(val) {
            setValue(props.model, field.target, val)
          },
          onChange: () => changeEvent(field)
        },
        {
          default: () =>
            field.options.map(op =>
              h(
                ElRadio,
                {
                  key: op.value || op,
                  label: op.value || op
                },
                {
                  default: () => [op.label || op]
                }
              )
            )
        }
      )
    } else {
      // 默认情况是输入框
      return h(
        ElInput,
        {
          modelValue: getValue(props.model, field.target), // 绑定输入框的值
          disabled: field.disabled, // 是否禁用
          'onUpdate:modelValue'(val) {
            setValue(props.model, field.target, val) // 更新模型值
          },
          onChange: () => changeEvent(field), // 触发变化事件
          type: 'number', // 输入框的类型
          class: 'input-with-select' // 输入框的自定义类
        },
        {
          append: () => {
            if (field.appendSelect) {
              // 如果有附加的选择框
              return h(
                ElSelect,
                {
                  style: {
                    width: '80px'
                  },
                  disabled: field.disabled, // 是否禁用
                  modelValue: props.model[field.target]?.unit, // 绑定单位值
                  'onUpdate:modelValue'(val) {
                    const item = props.model[field.target]
                    if (!item) {
                      return
                    }
                    item.unit = val // 更新单位值
                  },
                  onChange: () => changeEvent(field) // 触发变化事件
                },
                {
                  default: () =>
                    field.options.map(op =>
                      h(ElOption, {
                        key: op.value || op, // 选项的 key
                        value: op.value || op, // 选项的值
                        label: op.label || op // 选项的显示标签
                      })
                    )
                }
              )
            } else if (field.appendBtnText) {
              // 如果有附加按钮
              return h(
                ElButton,
                {
                  onClick: field.clickEvent // 按钮的点击事件
                },
                { default: () => [field.appendBtnText] } // 按钮显示的文本
              )
            } else if (field.appendText) {
              // 如果有附加文本
              return h('span', {}, { default: () => [field.appendText] }) // 显示附加文本
            }
          }
        }
      )
    }
  }

  // 定义一个渲染函数对象，用于渲染表单
  const renderForm = {
    render: () => {
      return h(
        ElForm,
        {
          rules: props.rules // 绑定表单验证规则
        },
        {
          default: () =>
            props.fields.map(field =>
              h(
                ElFormItem,
                {
                  key: field.name, // 表单项的 key
                  label: field.name // 表单项的标签
                },
                {
                  default: () => [generateCurrent(field)] // 渲染具体的表单项
                }
              )
            )
        }
      )
    }
  }
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    min-width: 100px;
    justify-content: flex-start;
    font-weight: 400;
  }

  :deep(.el-input-group__append) {
    padding: 0;

    & > * {
      margin: 0;
    }
    > span {
      padding: 0 12px;
      display: inline-block;
      width: 80px;
    }
  }
</style>
