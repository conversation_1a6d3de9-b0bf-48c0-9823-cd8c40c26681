import request from '@/utils/request'

// 查询虚拟资源信息列表
export function listResources (query) {
  return request({
    url: '/business/resources/list',
    method: 'get',
    params: query
  })
}

// 查询虚拟资源信息详细
export function getResources (resId) {
  return request({
    url: '/business/resources/' + resId,
    method: 'get'
  })
}

// 新增虚拟资源信息
export function addResources (data) {
  return request({
    url: '/business/resources',
    method: 'post',
    data: data
  })
}

// 修改虚拟资源信息
export function updateResources (data) {
  return request({
    url: '/business/resources',
    method: 'put',
    data: data
  })
}

// 删除虚拟资源信息
export function delResources (resId) {
  return request({
    url: '/business/resources/' + resId,
    method: 'delete'
  })
}

//

/**
 * 重启虚拟资源信息
 * @export
 * @param {*} data.resId -资源id
 * @param {*} data.type -操作类型 “00” 重启  “01” 关机 “02” 休眠
 * @return {*}
 */
export function restartResources (resId) {
  return request({
    url: '/business/resources/power',
    method: 'post',
    params: { resId, type: '00' }
  })
}

// 休眠虚拟资源信息
export function relaxResources (resId) {
  return request({
    url: '/business/resources/power',
    method: 'post',
    params: { resId, type: '02' }
  })
}
// 休眠虚拟资源信息
export function closeResources (resId) {
  return request({
    url: '/business/resources/power',
    method: 'post',
    params: { resId, type: '01' }
  })
}

// 导入虚拟资源信息
export function importResources (data) {
  return request({
    url: '/business/resources/import',
    method: 'post',
    data
  })
}

/**
 * 设置虚拟机用户密码
 * @export
 * @param {*} data.resIds -资源id
 * @param {*} data.login -登录名
 * @param {*} data.password -登录密码
 * @return {*}
 */
export function setLoginResources (data) {
  return request({
    url: '/business/resources/setLogin',
    method: 'post',
    dataType: 'formdata',
    data
  })
}

// 设置虚拟机归属期班
export function setOwnClazzResources (data) {
  return request({
    url: '/business/resources/setOwnClazz',
    method: 'post',
    dataType: 'formdata',
    data
  })
}

/**
 * 分配虚拟机归属学生
 * @export
 * @param {*} data.resIds -资源集合，同批量删除
 * @param {*} data.stuId -学生编号
 * @param {*} data.stuName -学生名称 用户回显
 * @return {*}
 */
export function setOwnStudentResources (data) {
  return request({
    url: '/business/resources/setOwnStudent',
    method: 'post',
    dataType: 'formdata',
    data
  })
}

/**
 * 虚拟机报修
 * @param {*} data.repairResCode -资源id 以逗号分割字符串
 * @param {*} data.repairBiz -故障现象
 * @return {*}
 */
export function reportResources (data) {
  return request({
    url: '/business/fault/report',
    method: 'post',
    data
  })
}
