# 页面标题
VITE_GLOB_APP_TITLE = 监测终端系统
VITE_PUBLIC_PATH = ./
# 生产环境配置
VITE_APP_ENV = 'production'
VITE_GLOB_APP_PREVIEW='https//127.0.0.1:9082'
# VITE_GLOB_APP_PREVIEW='************:9082'
# 管理系统/生产环境
VITE_GLOB_APP_BASE_API = 'https://127.0.0.1:9082'
# VITE_GLOB_APP_BASE_API = 'http://************:9082'
# 地图地址
VITE_GLOB_MAP_BASE_API = 'https://*************'
# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = gzip
# websocket 地址
VITE_GLOB_WS = 'ws://127.0.0.1:9082'
# VITE_GLOB_WS = 'ws://************:9082'

VITE_GLOB_HOST = '*************'
VITE_GLOB_PORT = '4444'
# 上位机模式
VITE_GLOB_UPPER_COMPUTER_MODE = 0