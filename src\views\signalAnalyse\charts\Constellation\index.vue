<template>
  <div id="constellationPlot" />
</template>

<script setup name="Constellation">
  import Highcharts from '@/plugins/highcharts'
  import config from './config'
  import mockFn from './mock'

  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })

  let instance = null

  const updateData = data => {
    if (!instance) {
      return
    }
    // console.log(data, '星座图原始数据')
    const result = []
    data.i.forEach((item, index) => {
      result.push([item, data.q[index]])
    })
    // console.log(result, '星座图数据')
    instance.series[0].setData(result)
  }
  onMounted(() => {
    instance = new Highcharts.Chart('constellationPlot', config)
    // const data = mockFn.getData(100)
    // updateData(data)
  })
  watch(
    () => props.data,
    val => {
      if (val.i.length > 0) {
        updateData(val)
      }
    }
  )
</script>
