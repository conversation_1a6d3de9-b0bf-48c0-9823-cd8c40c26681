<!--
    频谱扫描
 -->
<template>
  <div key="online-container" class="online-container app-container">
    <el-row key="online-main" class="main">
      <el-col :span="18" class="chart-info">
        <ScanSection>
          <!--频谱图  -->
          <div class="charts">
            <AnalyseCharts
              key="online-chart"
              usekey="online"
              :data-list="dataList"
              :model="spectrum"
            />
          </div>

          <!-- 信号、采集、回放功能按钮模块 -->
          <div class="tabs">
            <BottomSignalTabs :signal-list="signalList" />
          </div>
        </ScanSection>
      </el-col>
      <el-col :span="6" class="chart-form">
        <ScanSection class="charts">
          <div class="form">
            <!-- 右侧参数设置模块 -->
            <RightSettings :model="spectrum" @start="startWebsocket" @stop="closeScan">
              <template #default>
                <!-- 离散-->
                <ScatterSetting v-show="spectrum.type" />
                <!-- 连续  -->
                <FreBandSetting v-show="!spectrum.type" />
              </template>
              <template #formItem="{ form }">
                <el-form-item label="设备参数">
                  <el-button :loading="paramsLoading" @click="paramSetting(form)">
                    设备参数设置
                  </el-button>
                </el-form-item>
              </template>
            </RightSettings>
          </div>
        </ScanSection>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Scan">
  import ScanSection from '@/components/SpectrumSignal/ScanSection.vue'
  import AnalyseCharts from '@/views/analyseCharts'
  import RightSettings from '@/components/SettingsForm/index.vue'
  import FreBandSetting from './rightSettings/FreBandSetting.vue'
  import ScatterSetting from './rightSettings/ScatterSetting.vue'
  import BottomSignalTabs from './bottomSignalTabs'
  import useScanStore from '@/store/modules/scanMonitor'
  import useLinkWsHook from '@/common/hooks/linkWs'
  import { ElMessage } from 'element-plus'
  import { linkWs } from '@/api/upperComputer/scan'
  import { SCAN_CODE, DISPERSED_CODE, PARAMS_SETTING } from '@/constant/funCodes'
  import { deviceParams } from '@/constant/types'
  const route = useRoute()

  // console.log(JSON.parse(route.query.data));

  const scanStore = useScanStore()
  const spectrum = scanStore.spectrum
  const paramsLoading = ref(false)
  const taskFunCode = computed(() => {
    const { type, commandType } = spectrum
    if (commandType && type) {
      return null
    } else if (!commandType && !type) {
      return SCAN_CODE
    } else if (!commandType && type) {
      return DISPERSED_CODE
    } else {
      return null
    }
  })
  const { linkScan, closeScan, pageData } = useLinkWsHook(spectrum, taskFunCode)
  const dataList = computed(() => {
    let initData = {
      current: [],
      average: [],
      max: [],
      min: [],
      limit: [],
      occupancy: []
    }
    if (scanStore.playFfts.length > 0 && spectrum.status === 0) {
      return { current: scanStore.playFfts }
    }
    if (!pageData.value) {
      return initData
    }
    if (taskFunCode.value === SCAN_CODE) {
      const data = pageData.value.subtaskResultBody
      if (!data) {
        return initData
      }
      data.frequencyListFromSrList.forEach(item => {
        initData.current.push(item.fieldStrength)
        initData.average.push(item.average)
        initData.max.push(item.maximum)
        initData.min.push(item.minimum)
        initData.limit.push(item.fieldStrength)
        initData.occupancy.push(item.occupancy)
      })
    } else if (taskFunCode.value === DISPERSED_CODE) {
      const data = pageData.value.discreteChannelResult?.frequencyListFromDcList
      if (!data) {
        return initData
      }
      data.sort((a, b) => a.frequency - b.frequency)
      data.forEach(item => {
        initData.current.push([item.frequency, item.fieldStrength])
        initData.average.push([item.frequency, item.average])
        initData.max.push([item.frequency, item.maximum])
        initData.min.push([item.frequency, item.minimum])
        initData.limit.push([item.frequency, item.fieldStrength])
        initData.occupancy.push([item.frequency, item.occupancy])
      })
    }
    return initData
  })
  const signalList = computed(() => {
    if (!pageData.value || !pageData.value.subtaskResultBody) {
      return []
    }
    return pageData.value.subtaskResultBody.signalFromSrList || []
  })
  const paramSetting = async form => {
    let ws = null
    const data = {
      parameterCode: deviceParams.length,
      parameterValue: deviceParams.map(item => item.code),
      parameterCount: deviceParams.map(item => form[item.key])
    }
    paramsLoading.value = true
    ws = await linkWs({
      host: '*************',
      port: 44444,
      taskFunCode: PARAMS_SETTING,
      sendTime: 10,
      data: encodeURIComponent(JSON.stringify(data))
    })
    ws.onmessage = event => {
      paramsLoading.value = false
      try {
        const rsp = JSON.parse(event.data)
        if (rsp.code === 200) {
          ElMessage.success('设备参数设置成功')
        } else {
          ElMessage.error('设备参数设置失败')
        }
      } catch (err) {
        console.log(err)
      }
      ws.close()
    }
  }
  const generateParams = form => {
    if (taskFunCode.value === SCAN_CODE) {
      return {
        dataType: 1,
        thresholdType: form.thresholdType,
        threshold: form.threshold,
        terminationDirection: 90,
        azimuthStep: 10,
        polarization: form.polarization,
        startFrequency: spectrum.startFre,
        endFrequency: spectrum.endFre,
        frequencyStep: spectrum.stepLength
      }
    } else if (taskFunCode.value === DISPERSED_CODE) {
      return {
        dataType: 1,
        thresholdType: form.thresholdType,
        threshold: form.threshold,
        polarization: form.polarization,
        discrete: spectrum.discrete,
        frequencyCount: spectrum.discrete.length,
        analyseBandwidth: spectrum.bandwidth
      }
    }
  }
  const startWebsocket = form => {
    const params = generateParams(form)
    linkScan(params)
  }
</script>

<style scoped lang="scss">
  @import '@/assets/styles/mixin.scss';

  .online-container {
    @include boxBg('@/assets/images/specscanbg.png');
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 1rem;
    overflow-y: auto;

    .main {
      height: 100%;
    }
  }

  .chart-form {
    padding-left: 10px;
  }
</style>
