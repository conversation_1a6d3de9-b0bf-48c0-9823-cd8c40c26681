<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <!-- <transition name="fade-transform" mode="out-in"> </transition> -->
      <keep-alive :include="tagsViewStore.cachedViews">
        <component :is="Component" v-if="!route.meta.link" :key="route.path" />
      </keep-alive>
    </router-view>
    <iframe-toggle />
  </section>
</template>

<script setup>
  import iframeToggle from './IframeToggle/index'
  import useTagsViewStore from '@/store/modules/tagsView'

  const tagsViewStore = useTagsViewStore()
</script>

<style lang="scss" scoped>
  .app-main {
    /* 50= navbar  50  */
    height: calc(100vh - 50px);
    width: 100%;
    position: relative;
    overflow: hidden;
    flex: 1;
    // border: 1rem solid var(--app-main-bg);
    padding: 1rem;
    overflow: auto;
    background-color: var(--el-bg-color);
  }

  .fixed-header + .app-main {
    padding-top: 50px;
  }

  .hasTagsView {
    .app-main {
      height: calc(100vh - 92px);
    }

    .fixed-header + .app-main {
      padding-top: 92px;
    }
  }
</style>

<style lang="scss">
  // fix css style bug in open el-dialog
  .el-popup-parent--hidden {
    .fixed-header {
      padding-right: 17px;
    }
  }
</style>
