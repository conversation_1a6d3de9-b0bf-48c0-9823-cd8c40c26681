<template>
  <div v-if="model" class="am-time-container">
    <data-chart
      :id="AM_VS_TIME"
      :data="model.data"
      :title="title"
      :config="model.config"
    />
  </div>
</template>

<script setup>
  // AM vs Time 图表
  import { queryAms } from '@/api/charts'
  import DataChart from '@/components/DataChart'
  import useChartsStore from '@/store/modules/charts'
  import useChartOptions from '@/common/hooks/chartOptions'
  import { AM_VS_TIME } from '@/constant/chartConsts'

  const chartsStore = useChartsStore()
  const model = ref(null)
  const isInDm = useRoute().path.includes('digitalmodulation') // 是否在调制解析页面
  const title = computed(() => (isInDm ? '幅度调制 vs 时间' : '幅度 vs 时间'))
  const init = async () => {
    const body = chartsStore.getFileInfo()
    const { data } = await queryAms(body)
    const xMax = chartsStore.static.enabled ? data.dbm.length - 1 : chartsStore.viewNum - 1
    const { chartOptions } = useChartOptions('sequential', xMax)
    generateModel(data.dbm, chartOptions)
  }
  const generateModel = (data, chartOptions) => {
    model.value = {
      id: AM_VS_TIME,
      data: data,
      config: {
        type: computed(() => chartsStore.static.enabled ? 'static' : 'trend'),
        chart: computed(() => chartOptions.value),
        viewNum: computed(() => chartsStore.static.enabled ? 'all' : chartsStore.viewNum)
      }
    }
  }

  onMounted(async () => {
    await init()
  })
</script>
