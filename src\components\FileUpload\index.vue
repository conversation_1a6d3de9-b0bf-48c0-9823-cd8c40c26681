<template>
  <div class="upload-file">
    <el-upload
      v-if="!disabled"
      ref="fileUpload"
      multiple
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :disabled="disabled"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :headers="headers"
      class="upload-file-uploader"
    >
      <!-- 上传按钮 -->
      <el-button type="primary">选取文件</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div v-if="showTip && !disabled" class="el-upload__tip">
      请上传
      <template v-if="fileSize">
        大小不超过 <strong style="color: #f56c6c">{{ fileSize }}MB</strong>
      </template>
      <template v-if="fileType">
        格式为 <strong style="color: #f56c6c">{{ fileType.join('/') }}</strong>
      </template>
      的文件
    </div>
    <!-- 文件列表 -->
    <ul
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
    >
      <li
        v-for="(file, index) in fileList"
        :key="file.uid"
        class="el-upload-list__item ele-upload-list__item-content"
      >
        <el-link :href="`${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ file.remark || file.name }} </span>
        </el-link>
        <div v-if="!disabled" class="ele-upload-list__item-content-action">
          <el-link :underline="false" type="danger" @click="handleDelete(index)">删除</el-link>
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup>
  import { getToken } from '@/utils/auth'
  import { settings } from '@/utils/settings'

  const props = defineProps({
    modelValue: [String, Object, Array],
    // 数量限制
    limit: {
      type: Number,
      default: 5
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => [
        'doc',
        'xls',
        'ppt',
        'txt',
        'pdf',
        'docx',
        'png',
        'mp3',
        'mp4',
        'avi',
        'wav',
        'jpg',
        'jpeg',
        'gif',
        'webp'
      ]
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    }
  })

  const { proxy } = getCurrentInstance()
  const emit = defineEmits()
  const number = ref(0)
  const uploadList = ref([])
  const baseUrl = settings.VITE_GLOB_APP_BASE_API
  const uploadFileUrl = ref(settings.VITE_GLOB_APP_BASE_API + '/common/upload') // 上传文件服务器地址
  const headers = ref({ Authorization: 'Bearer ' + getToken() })
  const fileList = ref([])
  const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize))

  watch(
    () => props.modelValue,
    val => {
      if (val) {
        let temp = 1
        // 首先将值转为数组
        console.log('监听父组件数据', val)
        const list = Array.isArray(val) ? val : props.modelValue.split(',')
        // 然后将数组转为对象数组

        fileList.value = list.map(item => {
          if (typeof item === 'string') {
            item = { name: item, url: item, remark: '79hhh' }
          } else {
            item = {
              ...item,
              remark: item.remark,
              name: item.name || item.fileName,
              url: item.url || item.filePath
            }
          }
          item.uid = item.uid || new Date().getTime() + temp++
          console.log('监听父组件转换后数据', item)
          return item
        })
      } else {
        fileList.value = []
        return []
      }
    },
    { deep: true, immediate: true }
  )

  // 上传前校检格式和大小
  function handleBeforeUpload(file) {
    // 校检文件类型
    if (props.fileType.length) {
      let fileExtension = ''
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
      }
      const isTypeOk = props.fileType.some(type => {
        if (file.type.indexOf(type) > -1) return true
        if (fileExtension && fileExtension.indexOf(type) > -1) return true
        return false
      })
      if (!isTypeOk) {
        proxy.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join('/')}格式文件!`)
        return false
      }
    }
    // 校检文件大小
    if (props.fileSize) {
      const isLt = file.size / 1024 / 1024 < props.fileSize
      if (!isLt) {
        proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`)
        return false
      }
    }
    proxy.$modal.loading('正在上传文件，请稍候...')
    number.value++
    return true
  }

  // 文件个数超出
  function handleExceed() {
    proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`)
  }

  // 上传失败
  function handleUploadError(err) {
    proxy.$modal.msgError('上传文件失败')
    proxy.$modal.closeLoading()
  }

  // 上传成功回调
  function handleUploadSuccess(res, file) {
    if (res.code === 200) {
      console.log('上传成功回调', res)
      uploadList.value.push({
        name: res.newFileName,
        fileName: res.originalFilename,
        remark: res.originalFilename,
        filePath: res.url,
        url: res.url
      })
      uploadedSuccessfully()
    } else {
      number.value--
      proxy.$modal.closeLoading()
      proxy.$modal.msgError(res.msg)
      proxy.$refs.fileUpload.handleRemove(file)
      uploadedSuccessfully()
    }
  }

  // 删除文件
  function handleDelete(index) {
    fileList.value.splice(index, 1)
    emit('update:modelValue', listToString(fileList.value))
  }

  // 上传结束处理
  function uploadedSuccessfully() {
    if (number.value > 0 && uploadList.value.length === number.value) {
      fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value)
      uploadList.value = []
      number.value = 0
      console.log('上传成功后把值处理值传给父组件', fileList.value)
      emit('update:modelValue', listToString(fileList.value))
      proxy.$modal.closeLoading()
    }
  }

  // 获取文件名称
  function getFileName(name = '') {
    if (name.lastIndexOf('/') > -1) {
      return name.slice(name.lastIndexOf('/') + 1)
    } else {
      return ''
    }
  }

  // 对象转成指定字符串分隔
  function listToString(list, separator) {
    console.log('对象转成指定字符串分割前原数据', list)
    let res = list.map(v => {
      let str1 = v.fileName.slice(0, 4)
      let str2 = v.name.slice(0, 4)
      if (str1 !== 'sdic') {
        let a = {
          name: v.fileName,
          remark: v.fileName,
          url: v.url,
          fileName: v.name,
          filePath: v.filePath
        }
        delete a.status
        delete a.uid
        return a
      } else if (str2 == 'sdic') {
        let a = {
          name: v.remark,
          remark: v.remark,
          url: v.url,
          fileName: v.fileName,
          filePath: v.filePath
        }
        delete a.status
        delete a.uid
        return a
      } else {
        let a = {
          name: v.name,
          remark: v.name,
          url: v.url,
          fileName: v.fileName,
          filePath: v.filePath
        }
        delete a.status
        delete a.uid
        return a
      }
    })
    console.log('对象转成指定字符串分割', res)
    return res
    // let strs = ''
    // separator = separator || ','
    // for (let i in list) {
    //   if (list[i].url) {
    //     strs += list[i].url + separator
    //   }
    // }
    // return strs != '' ? strs.substr(0, strs.length - 1) : ''
  }
</script>

<style scoped lang="scss">
  .upload-file {
    flex: 1;
  }
  .upload-file-uploader {
    margin-bottom: 5px;
  }
  .upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
    padding: 5px 10px;
  }
  .upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }
</style>
