<script setup>
  import { ElMessage } from 'element-plus'
  import { linkWs } from '@/api/upperComputer/scan'
  import { DEVICE_SEARCH, DEVICE_STATUS_SEARCH } from '@/constant/funCodes'
  import { getConfigKey } from '@/api/system/config'

  const props = defineProps({
    host: {
      type: String,
      default: ''
    },
    port: {
      type: Number,
      default: 0
    },
    deviceCode: {
      type: String,
      default: ''
    }
  })

  const wsLoading = ref(false)
  const longitude = ref('--')
  const latitude = ref('--')
  const timeType = ref('--')
  const timeMeasurement = ref('--')
  const message = ref('--')
  const deviceStatus = ref('--')
  const open = defineModel('open')
  const sysType = ref('416')
  const htraDeviceInfo = ref('')

  const judgeSysType = async () => {
    await getConfigKey('system.type').then(async res => {
      sysType.value = res.msg
    })
  }

  /**
   * 获取设备状态类型对应的中文描述
   * @param type 设备状态类型，0表示正常，1表示异常，2表示正在执行任务，其他值返回'--'
   * @returns 返回设备状态类型对应的中文描述
   */
  const getDeviceStatusType = type => {
    switch (type) {
      case 0:
        return '正常'
      case 1:
        return '异常'
      case 2:
        return '正在执行任务'
      default:
        return '--'
    }
  }

  /**
   * 根据设备类型获取时间设备名称
   * @param type 设备类型，取值范围为1~4
   * @returns 返回时间设备名称，若传入的设备类型不在取值范围内则返回'--'
   */
  const getTimeDeviceType = type => {
    switch (type) {
      case 1:
        return '北斗/GPS一体机'
      case 2:
        return '北斗'
      case 3:
        return 'GPS'
      case 4:
        return '其他'
      default:
        return '--'
    }
  }

  // 设备自检
  const selfTest = async () => {
    open.value = true
    wsLoading.value = true
    await judgeSysType()
    try {
      const paramResult = await paramSetting()
      if (paramResult && sysType.value === '416') {
        const statusResult = await statusSetting()
        if (statusResult) {
          ElMessage.success('设备自检成功')
        }
      }
    } catch (error) {
      ElMessage.error('设备连接失败，请确保设备开机并连接正常后再试')
    } finally {
      wsLoading.value = false
    }
  }
  /**
   * 处理WebSocket连接
   * @param ws WebSocket对象
   * @param resolve Promise的resolve函数
   * @param reject Promise的reject函数
   * @param processResult 处理返回结果的回调函数
   * @returns 无返回值
   */
  const handleWebSocket = (ws, resolve, reject, processResult) => {
    ws.onmessage = event => {
      try {
        const rsp = JSON.parse(event.data)
        console.log('收到数据:', rsp)
        if (rsp?.data?.msgHeader?.taskCode === 1) {
          console.log('回执，跳过处理')
          return
        }
        if (rsp.code === 200) {
          console.log('处理设备数据:', rsp.data.result)
          processResult(rsp.data.result)
          resolve(true)
          ws.close()
        } else {
          console.log('rsp.code 不为200:', rsp.code)
          reject(new Error('设备连接失败，请确保设备开机并连接正常后再试'))
        }
      } catch (err) {
        console.log('处理数据出错:', err)
        reject(new Error('设备连接失败，请确保设备开机并连接正常后再试'))
      }
    }

    ws.onerror = err => {
      console.log('WebSocket连接错误:', err)
      reject(new Error('WebSocket连接错误'))
    }

    ws.onclose = () => {
      console.log('WebSocket连接已关闭')
    }
  }

  /**
   * 513指令（设备状态查询）
   */
  const paramSetting = () => {
    return new Promise(async (resolve, reject) => {
      let ws = null
      let data = {}
      try {
        ws = await linkWs({
          host: props.host,
          port: props.port,
          taskFunCode: DEVICE_STATUS_SEARCH,
          sendTime: 10,
          deviceCode: props.deviceCode,
          data: encodeURIComponent(JSON.stringify(data))
        })
        handleWebSocket(ws, resolve, reject, result => {
          switch (sysType.value) {
            case '416':
              longitude.value = result.longitude || '--'
              latitude.value = result.latitude || '--'
              deviceStatus.value = result.deviceStatus.deviceStatus || '--'
              message.value = result.deviceStatus.message || '--'
              break
            case 'htra':
              htraDeviceInfo.value = result.deviceInfo || '--'
            default:
              break
          }
        })
      } catch (err) {
        reject(new Error('设备连接失败，请确保设备开机并连接正常后再试'))
      }
    })
  }

  /*
   *515指令（设备位置查询）
   */
  const statusSetting = () => {
    return new Promise(async (resolve, reject) => {
      let ws = null
      let data = {}
      try {
        ws = await linkWs({
          host: props.host,
          port: props.port,
          taskFunCode: DEVICE_SEARCH,
          sendTime: 10,
          deviceCode: props.deviceCode,
          data: encodeURIComponent(JSON.stringify(data))
        })
        handleWebSocket(ws, resolve, reject, result => {
          timeType.value = result.deviceLocation.timeType || '--'
          timeMeasurement.value = result.deviceLocation.timeMeasurement || '--'
        })
      } catch (err) {
        reject(new Error('设备连接失败，请确保设备开机并连接正常后再试'))
      }
    })
  }

  defineExpose({
    selfTest
  })
</script>

<template>
  <el-dialog v-model="open" title="设备自检信息" width="400px" align-center append-to-body center>
    <div class="min-h-[240px]" v-loading="wsLoading">
      <div v-if="sysType === '416'">
        <div class="m-2 text-lg">设备状态：{{ getDeviceStatusType(deviceStatus) }}</div>
        <div v-if="deviceStatus === 1" class="m-2 text-lg"> 状态信息：{{ message }} </div>
        <div class="m-2 text-lg">时统设备：{{ getTimeDeviceType(timeType) }}</div>
        <div class="m-2 text-lg">时统时间：{{ timeMeasurement }}</div>
        <div class="m-2 text-lg">经度：{{ longitude }} </div>
        <div class="m-2 text-lg">纬度：{{ latitude }}</div>
      </div>
      <div v-else>
        <div v-html="htraDeviceInfo"> </div>
        <!-- {{ htraDeviceInfo }} -->
      </div>
    </div>
    <template #footer>
      <div class="flex items-center justify-center">
        <el-button type="primary" @click="open = false">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
