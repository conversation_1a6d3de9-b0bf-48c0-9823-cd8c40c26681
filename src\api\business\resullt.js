import request from '@/utils/request'

// 查询实验记录列表
export function listResullt(query) {
  return request({
    url: '/business/resullt/list',
    method: 'get',
    params: query
  })
}

// 查询实验记录详细
export function getResullt(objId) {
  return request({
    url: '/business/resullt/' + objId,
    method: 'get'
  })
}

// 新增实验记录
export function addResullt(data) {
  return request({
    url: '/business/resullt',
    method: 'post',
    data: data
  })
}

// 修改实验记录
export function updateResullt(data) {
  return request({
    url: '/business/resullt',
    method: 'put',
    data: data
  })
}

// 删除实验记录
export function delResullt(objId) {
  return request({
    url: '/business/resullt/' + objId,
    method: 'delete'
  })
}
