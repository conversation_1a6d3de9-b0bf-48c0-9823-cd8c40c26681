import useScanStore from '@/store/modules/scanMonitor'
import { numToPlot } from '@/utils/utils';

const { spectrumCopy } = useScanStore()

export const chartConfigColumn = {
  chart: {
    backgroundColor: '#020e1f', // 图表背景色
    type: 'column', // 图表类型：柱状图
    height: 400, // 图表高度
    spacingTop: 30, // 顶部间距
    spacingBottom: 0, // 底部间距
    spacingLeft: 0, // 左侧间距
    spacingRight: 10, // 右侧间距
    zoomType: 'x', // 缩放类型：仅x轴
  },
  exporting: { enabled: false }, // 禁用导出功能
  reflow: true,
  credits: { enabled: false }, // 禁用版权信息
  accessibility: { enabled: false }, // 禁用无障碍功能
  xAxis: {
    type: 'linear', // 线性轴
    title: { text: '频率 (Hz)', style: { color: '#aeadad' } },
    labels: { style: { color: '#aeadad' } },
    tickAmount: 11,
    lineColor: '#aeadad',
    lineWidth: 0.5,
  },
  yAxis: {
    title: { text: '幅度 (dBm)', style: { color: '#aeadad' } }, // Y轴标题
    min: spectrumCopy.refLevel - 100, // 设置 Y 轴的最小值
    max: spectrumCopy.refLevel, // 设置 Y 轴的最大值
    gridLineColor: 'rgba(1, 84, 120, 0.8)', // 网格线颜色
    gridLineDashStyle: 'solid', // 网格线样式
    gridLineWidth: 0.5, // 网格线宽度
    lineColor: 'rgba(1, 84, 120, 1)', // Y 轴线颜色
    labels: { style: { color: '#aeadad' } }, // Y 轴标签样式
    tickAmount: 11, // 刻度数量
  },
  title: { enabled: false, text: '' }, // 禁用标题
  legend: {
    enabled: false, // 启用图例
  },
  series: [
    {
      id: 'current',
      name: '实时',
      color: '#ffff00', // 颜色：半透明黄色
      data: [], // 数据
      borderRadius: 2, // 柱状图圆角
      pointPlacement: 'on',
      threshold: spectrumCopy.refLevel - 100 // 设置柱子的起始位置为 Y 轴的最小值
    },
    {
      id: 'average',
      name: '平均',
      color: '#f56c6c', // 颜色：半透明红色
      data: [], // 数据
      borderRadius: 2, // 柱状图圆角
      pointPlacement: 'on',
      threshold: spectrumCopy.refLevel - 100 // 设置柱子的起始位置为 Y 轴的最小值
    },
    {
      id: 'max',
      name: '最大',
      color: '#67c23a', // 绿色
      data: [], // 数据
      borderRadius: 2, // 柱状图圆角
      pointPlacement: 'on',
      threshold: spectrumCopy.refLevel - 100 // 设置柱子的起始位置为 Y 轴的最小值
    },
    {
      id: 'min',
      name: '最小',
      color: '#409eff', // 蓝色
      data: [], // 数据
      borderRadius: 2, // 柱状图圆角
      pointPlacement: 'on',
      threshold: spectrumCopy.refLevel - 100 // 设置柱子的起始位置为 Y 轴的最小值
    },
    {
      id: 'limit',
      name: '门限',
      color: '#ffffff', // 白色
      data: [], // 数据
      borderRadius: 2, // 柱状图圆角
      pointPlacement: 'on',
      threshold: spectrumCopy.refLevel - 100 // 设置柱子的起始位置为 Y 轴的最小值

    }
  ],
  plotOptions: {
    column: {
      pointPadding: 0.1,  // 较紧凑的柱形
      groupPadding: 0.1,   // 较小的组间距
      borderWidth: 0,
      pointWidth: 40, // 自适应宽度
      grouping: false, // 不对不同系列分组，保持原有位置
      pointPlacement: 'on'
    }
  },
  tooltip: {
    formatter: function () {
      return `<b>${numToPlot(this.x)}</b><br/>${this.series.name}: ${this.y} dBm`;
    }
  },
};



