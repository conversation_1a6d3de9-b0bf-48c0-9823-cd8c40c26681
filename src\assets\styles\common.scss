// 通用类名
$num_list: 1, 2, 3, 4, 5;

@each $num in $num_list {
  .ml-#{$num} {
    margin-left: $num * 4px;
  }
  .mr-#{$num} {
    margin-right: $num * 4px;
  }
  .mt-#{$num} {
    margin-top: $num * 4px;
  }
  .mb-#{$num} {
    margin-bottom: $num * 4px;
  }
  .pl-#{$num} {
    padding-left: $num * 4px;
  }
  .pr-#{$num} {
    padding-right: $num * 4px;
  }
  .pt-#{$num} {
    padding-top: $num * 4px;
  }
  .pb-#{$num} {
    padding-bottom: $num * 4px;
  }
}
.overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
