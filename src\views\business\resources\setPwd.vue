<template>
  <el-dialog v-model="dia" title="设置登录名/密码" width="400px" append-to-body>
    <el-form ref="formRef" v-loading="loading" class="mr-4" :model="form" label-width="6em">
      <el-form-item label="登录名" prop="uuid">
        <el-input v-model="form.login" maxlength="20" placeholder="请输入登录名" />
      </el-form-item>
      <el-form-item label="登录密码" maxlength="20" prop="id">
        <el-input v-model="form.password" placeholder="请输入登录密码" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel(false)">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
  import * as resApi from '@/api/business/resources'
  export default {
    name: 'ResourceDetail',
    emits: ['actSuccess'],
    data() {
      return {
        form: {},
        title: '',
        dia: false,
        loading: false,
        cb: null,
        resid: ''
      }
    },
    methods: {
      show(id, cb = null) {
        console.log('sel', id)
        this.cb = cb || null
        this.dia = true
        this.form = {
          resIds: id
        }
      },
      // 取消按钮
      cancel(t = false) {
        this.dia = false
        t && this.$emit('actSuccess')
        this.form = {}
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs['formRef'].validate(valid => {
          if (valid) {
            this.loading = true
            resApi
              .setLoginResources(this.form)
              .then(response => {
                this.$modal.msgSuccess('操作成功')
                this.cancel(true)
              })
              .finally(() => {
                this.loading = false
              })
          }
        })
      }
    }
  }
</script>
