<template>
  <el-collapse v-model="activeItems" class="collapse-panels">
    <el-collapse-item
      v-for="panel in panels"
      :key="panel.value"
      :title="panel.title"
      :name="panel.value"
    >
      <component :is="dom[panel.value]" @operation="emit('filter')" />
    </el-collapse-item>
  </el-collapse>
</template>

<script setup name="LeftForm">
  import SignalFileParams from './SignalFileParams.vue' //信号文件参数
  import RefLine from './RefLine.vue' //参考线
  import DownConversion from './DownConversion.vue' //下变频
  import Filtering from './Filtering.vue' //滤波
  import SignalChoose from './SignalChoose.vue' //信号分选

  const emit = defineEmits(['filter'])
  const panels = [
    {
      title: '信号文件参数',
      value: 'SignalFileParams'
    },
    {
      title: '下变频',
      value: 'DownConversion'
    },
    {
      title: '滤波',
      value: 'Filtering'
    },
    {
      title: '信号分选',
      value: 'SignalChoose'
    }
  ]

  const dom = shallowReactive({
    SignalFileParams,
    RefLine,
    DownConversion,
    Filtering,
    SignalChoose
  })

  const activeItems = ref(panels.map(panel => panel.value))
</script>

<style scoped lang="scss">
  .panels-leave-active,
  .panels-enter-active {
    transition: all 0.8s ease-in-out;
  }

  .panels-enter-from {
    transform: translateY(-200px);
    opacity: 0;
  }
  .panels-leave-to {
    transform: translateY(-200px);
    opacity: 0;
  }
</style>
