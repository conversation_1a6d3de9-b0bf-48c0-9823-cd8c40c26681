<template>
  <div class="scan-section">
    <slot />
    <div class="bottom-icon left">
      <svg width="24px" height="24px">
        <path
          d="M 0,0 L 0,12 L 12,24 L 24,24"
          fill="transparent"
          stroke="#F1F292"
          stroke-width="3"
        />
      </svg>
    </div>
    <div class="bottom-icon right">
      <svg width="24px" height="24px">
        <polyline
          points="24,0 24,12 12,24 0,24"
          fill="transparent"
          stroke="#F1F292"
          stroke-width="3"
        />
      </svg>
    </div>
  </div>
</template>

<script setup>

</script>

<style scoped lang="scss">
.scan-section {
  height: 100%;
  background-color: var(--scan-bg);
  box-sizing: border-box;
  border-width: 2px;
  border-style: solid;
  border-color: var(--main-color);
  border-radius: 10px;
  position: relative;
  box-shadow: var(--box-shadow);
  .bottom-icon {
    position: absolute;
    bottom: -3px;
    width: 24px;
    height: 24px;
    &.left {
      left: -3px;
    }
    &.right {
      right: -3px
    }
  }
}
</style>