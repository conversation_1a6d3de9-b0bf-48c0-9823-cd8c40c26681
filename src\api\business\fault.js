import request from '@/utils/request'

// 查询资源故障报修信息列表
export function listFault(query) {
  return request({
    url: '/business/fault/list',
    method: 'get',
    params: query
  })
}

// 查询资源故障报修信息详细
export function getFault(faultId) {
  return request({
    url: '/business/fault/' + faultId,
    method: 'get'
  })
}

// 新增资源故障报修信息
export function addFault(data) {
  return request({
    url: '/business/fault',
    method: 'post',
    data: data
  })
}

// 修改资源故障报修信息
export function updateFault(data) {
  return request({
    url: '/business/fault',
    method: 'put',
    data: data
  })
}

// 删除资源故障报修信息
export function delFault(faultId) {
  return request({
    url: '/business/fault/' + faultId,
    method: 'delete'
  })
}
