<template>
  <el-table border :data="signalList">
    <el-table-column label="序号" type="index" width="60" align="center" />
    <el-table-column label="信号类型" prop="signalType" width="200" align="center">
      <template #default="{ row }">
        {{ handleSignalType(row.signalType) }}
      </template>
    </el-table-column>
    <el-table-column label="信号频率(MHz)" prop="signalFrequency" align="center" />
    <el-table-column label="信号带宽(KHz)" prop="bandwidth1" align="center" />
    <el-table-column label="调制方式" prop="modulation" align="center">
      <template #default="{ row }">
        {{ handleModulation(row.modulation) }}
      </template>
    </el-table-column>
    <el-table-column label="码速率(MHz)" prop="codeRate" align="center" />
    <el-table-column label="步长(KHz)" prop="step" align="center" />
    <el-table-column label="子载波数" prop="subcarriers" align="center" />
    <el-table-column label="波形" prop="waveForm" align="center" />
    <el-table-column label="操作" width="180" align="center">
      <template #default="{ row }">
        <el-button size="small" @click="analyse(row)">分析</el-button>
        <el-button size="small" @click="findDirection(row)">测向</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
  import { signalTypes, demodulateTypes } from '@/constant/types'
  import useScanStore from '@/store/modules/scanMonitor'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const { signal } = useScanStore()
  const props = defineProps({
    signalList: {
      type: Array,
      default: () => []
    }
  })
  const handleSignalType = st => {
    const target = signalTypes.find(item => {
      if (item.value.length) {
        return item.value[0] <= st && st <= item.value[1]
      }
      return item.value === st
    })
    return target?.label
  }
  const handleModulation = modulation => {
    const target = demodulateTypes.find(item => item.value === modulation)
    return target?.label
  }
  const analyse = row => {
    signal.centerFreq = row.signalFrequency
    signal.bandwidth = row.bandwidth1
    signal.startFre = row.signalFrequency - row.bandwidth1 / 2
    signal.endFre = row.signalFrequency + row.bandwidth1 / 2
    router.push({
      name: 'ScanSignalAnalyse'
    })
  }
  const findDirection = row => {
    signal.commandType = 1
    analyse(row)
  }
</script>
