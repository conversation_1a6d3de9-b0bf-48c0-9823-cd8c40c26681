<template>
  <div>
    <vxe-toolbar class="vxe-toolbar">
      <template #buttons>
        <vxe-input
          v-model="filterOption.fileName"
          type="search"
          placeholder="请输入文件名称"
          clearable
          @keyup="searchEvent"
          @clear="clearName"
        />
        <vxe-input
          v-model="filterOption.startTime"
          type="date"
          placeholder="请选择起始时间"
          class="time-button"
          clearable
        />
        <p class="interval-p"> ~ </p>
        <vxe-input
          v-model="filterOption.endTime"
          clearable
          type="date"
          placeholder="请选择截止时间"
        />
        <vxe-button
          status="primary"
          content="查询"
          class="search-button"
          @click="timeSearch(filterOption)"
        />
        <vxe-button
          status="success"
          content="上传"
          class="upload-button"
          @click="uploadFile(false)"
        />
        <vxe-button
          status="danger"
          content="批量删除"
          class="delete-all-button"
          @click="deleteAll"
        />
        <vxe-button
          status="primary"
          content="频谱数据批量分析"
          class="pl-5"
          @click="freBatchAnalysis"
        />
      </template>
    </vxe-toolbar>
    <vxe-table
      ref="xTable"
      border
      round
      :checkbox-config="{ labelField: 'listId' }"
      :data="list"
      :loading="loading"
      :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
      :keep-source="true"
      class="mytable-scrollbar"
      @edit-closed="saveEdit"
      @checkbox-change="selectChangeEvent"
      @checkbox-all="selectChangeEvent"
    >
      <vxe-column type="checkbox" title="序号" width="120" fixed="left" />
      <vxe-column field="fileName" title="文件" width="240" type="html" fixed="left" />
      <vxe-column
        field="uploadTime"
        title="上传时间"
        width="180"
        :filters="[
          { data: '', label: '起:' },
          { data: '', label: '止:' }
        ]"
        :filter-method="customDateFilterMethod"
      >
        <template #filter="{ $panel, column }">
          <template v-for="(option, i) in column.filters" :key="i">
            <div class="uploadtime-style">
              <span class="time-span">{{ option.label }}</span>
              <vxe-input
                v-model="option.data"
                type="date"
                placeholder="请选择"
                transfer
                clearable
                @input="$panel.changeOption($event, !!option.data, option)"
              />
            </div>
          </template>
        </template>
      </vxe-column>
      <vxe-column
        field="fileType"
        title="文件类型"
        width="150"
        :filters="[{ data: '' }]"
        :edit-render="{ name: '$select' }"
      >
        <template #default="{ row }">
          <span>{{ formatFiletype(row.fileType) }}</span>
        </template>
        <template #edit="{ row }">
          <select v-model="row.fileType" class="select-style" transfer>
            <option
              v-for="(item, index) in filetypeList"
              :key="index"
              :value="item.value"
              :label="item.label"
            />
          </select>
        </template>
      </vxe-column>
      <vxe-column
        field="dataOrganization"
        title="IQ数据组织方式"
        width="190"
        :filters="[{ data: '' }]"
        :edit-render="{ name: '$select' }"
      >
        <template #default="{ row }">
          <span>{{ formatDataOrganization(row.dataOrganization) }}</span>
        </template>
        <template #edit="{ row }">
          <select v-model="row.dataOrganization" class="select-style" transfer>
            <option
              v-for="(item, index) in dataOrganizationList"
              :key="index"
              :value="item.value"
              :label="item.label"
            />
          </select>
        </template>
      </vxe-column>
      <vxe-column
        field="dataType"
        title="IQ数据类型"
        width="170"
        :filters="[{ data: '' }]"
        :edit-render="{ name: '$select' }"
      >
        <template #default="{ row }">
          <span>{{ formatIQDatatype(row.dataType) }}</span>
        </template>
        <template #edit="{ row }">
          <select v-model="row.dataType" class="select-style" transfer>
            <option
              v-for="(item, index) in datatypeList"
              :key="index"
              :value="item.value"
              :label="item.label"
            />
          </select>
        </template>
      </vxe-column>
      <vxe-column
        field="debugMode"
        title="调制方式"
        width="140"
        :filters="[{ data: '' }]"
        :edit-render="{ name: '$select' }"
      >
        <template #default="{ row }">
          <span>{{ formatDebugMode(row.debugMode) }}</span>
        </template>
        <template #edit="{ row }">
          <select v-model="row.debugMode" class="select-style" transfer>
            <option
              v-for="(item, index) in debugModeList"
              :key="index"
              :value="item.value"
              :label="item.label"
            />
          </select>
        </template>
      </vxe-column>
      <vxe-column
        field="startOffset"
        title="IQ数据起始字节"
        width="160"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.startOffset"
            class="input-name"
            type="text"
            maxlength="15"
            @input="row.startOffset = row.startOffset.replace(/[^/0/1/2/3/4/5/6/7/8/9/]/gi, '')"
          />
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="cutoffLength"
        title="文件末尾截断字节数"
        width="160"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.cutoffLength"
            class="input-name"
            type="text"
            maxlength="15"
            @input="row.cutoffLength = row.cutoffLength.replace(/[^/0/1/2/3/4/5/6/7/8/9/]/gi, '')"
          />
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="centerFreqIn"
        title="中心频率"
        width="140"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.centerFreqIn"
            class="input-name"
            type="text"
            maxlength="15"
            @input="
              row.centerFreqIn = row.centerFreqIn.replace(
                /[^/0/1/2/3/4/5/6/7/8/9/./,/，/k/K/H/h/Z/z/M/m/G/g/]/gi,
                ''
              )
            "
            @blur="showData(row.centerFreqIn)"
          />
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="intermediateFrequencyBandwidth"
        title="中频带宽"
        width="140"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
        <template #edit="{ row }">
          <input
            v-model="row.intermediateFrequencyBandwidth"
            type="text"
            class="input-name"
            @blur="showData(row.intermediateFrequencyBandwidth)"
            @input="
              row.intermediateFrequencyBandwidth = row.intermediateFrequencyBandwidth.replace(
                /[^/0/1/2/3/4/5/6/7/8/9/./，/,/k/K/H/h/Z/z/M/m/G/g/]/gi,
                ''
              )
            "
          />
        </template>
      </vxe-column>
      <vxe-column
        field="samplingRate"
        title="采样率"
        width="120"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.samplingRate"
            class="input-name"
            type="text"
            @blur="showData(row.samplingRate)"
            @input="
              row.samplingRate = row.samplingRate.replace(
                /[^/0/1/2/3/4/5/6/7/8/9/./,/，/k/K/H/h/Z/z/M/m/G/g/]/gi,
                ''
              )
            "
          />
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="steplen"
        title="步长"
        width="120"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.steplen"
            type="text"
            class="input-name"
            @blur="showData(row.steplen)"
            @input="
              row.steplen = row.steplen.replace(
                /[^/0/1/2/3/4/5/6/7/8/9/./,/，/k/K/H/h/Z/z/M/m/G/g/]/gi,
                ''
              )
            "
          />
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          />
        </template>
      </vxe-column>
      <!-- <vxe-column field="powerMultiple" title="功放倍率" width="100">
        <template #default="{ row }">
          <div v-if="row.fileType === '1'">
            <input
              v-model="row.powerMultiple"
              class="input-name"
              type="text"
              maxlength="15"
              @input="row.powerMultiple = row.powerMultiple.replace(/[^0-9]/g, '')"
            />
          </div>
          <div v-else>
            {{ row.powerMultiple }}
          </div>
        </template>
      </vxe-column> -->
      <vxe-column
        field="powerMultiple"
        title="功率倍数"
        width="100"
        :edit-render="{
          name: 'input',
          props: {
            type: 'text',
            maxlength: 15,
            class: 'input-name'
          },
          events: {
            input: ({ row }) => {
              // 确保 powerMultiple 是字符串，避免 replace 报错
              row.powerMultiple = String(row.powerMultiple || '').replace(/[^0-9/]/g, '')
            },
            disabled: ({ row }) => String(row.fileType) !== '1'
          }
        }"
        :editable="({ row }) => String(row.fileType) === '1'"
      >
      </vxe-column>

      <vxe-column field="操作" title="操作" width="260" fixed="right">
        <template #default="{ row }">
          <vxe-button
            class="drow-button-style"
            status="primary"
            content="分析"
            @click="goAnalysisView(row)"
          />
          <vxe-button status="primary" content="下载" @click="downFile(row)" />
          <vxe-button status="primary" content="删除" @click="deleteCurrentLine(row)" />
        </template>
      </vxe-column>
    </vxe-table>
    <Popup :message="childValue" :debugmodemessage="debugModeMessage" @childcLick="closeModal" />
    <!-- 分页 -->
    <p>
      <vxe-pager
        v-model:current-page="curPage"
        v-model:page-size="size"
        class="vxe-page"
        perfect
        :total="total"
        :page-sizes="[10, 20, 50, 100, 200, 500]"
        :layouts="[
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'Sizes',
          'FullJump',
          'Total'
        ]"
      />
    </p>
  </div>
</template>
<script setup>
  import {
    displayUnitConversion,
    showData,
    fliterData,
    customDateFilterMethod,
    downFile
  } from '@/api/tool/filemanage'
  import Popup from '@/components/Popup/index.vue'
  import { onMounted, ref, reactive } from 'vue'
  import useChartsStore from '@/store/modules/charts'
  import useDictStore from '@/store/modules/dict'
  import { useRouter } from 'vue-router'
  import useList from '@/api/tool/filemanage/tableFunciton'
  import { allSignalListGet } from '@/api/singalManage'
  const filterOption = ref({})
  const fileId = ref(0)
  const xTable = ref(null)
  const {
    list,
    loading,
    curPage,
    size,
    total,
    loadData,
    timeSearch,
    deleteCurrentLine,
    deleteAll,
    freBatchAnalysis,
    selectChangeEvent,
    saveEdit
  } = useList(allSignalListGet, filterOption, fileId, xTable)
  const router = useRouter()
  const debugModeMessage = ref([])
  const childValue = ref(false)
  const filetypeList = ref([])
  const dataOrganizationList = ref([])
  const datatypeList = ref([])
  const debugModeList = ref([])
  const file = reactive({
    filename: ''
  })
  onMounted(() => {
    getDictionaryData()
  })
  const closeModal = status => {
    childValue.value = status
    loadData()
  }

  //获取字典数据
  const getDictionaryData = () => {
    const store = useDictStore()
    const $table = xTable.value
    //获取文件类型
    const filetype = store.dict.filter(item => item.dictType == 'filetype')
    filetype.forEach(item => {
      filetypeList.value.push({ label: item.dictLabel, value: item.dictValue })
    })
    const fileTypeColumn = $table.getColumnByField('fileType')
    if (fileTypeColumn) {
      // 修改筛选列表，并默认设置为选中状态
      $table.setFilter(fileTypeColumn, filetypeList.value)
      // 修改条件之后，需要手动调用 updateData 处理表格数据
      $table.updateData()
    }
    //获取IQ数据组织方式
    store.dict
      .filter(item => item.dictType == 'data_organization')
      .forEach(item => {
        dataOrganizationList.value.push({ label: item.dictLabel, value: item.dictValue })
      })
    const dataOrganizationColumn = $table.getColumnByField('dataOrganization')
    if (dataOrganizationColumn) {
      $table.setFilter(dataOrganizationColumn, dataOrganizationList.value)
      $table.updateData()
    }
    //获取IQ数据类型
    store.dict
      .filter(item => item.dictType == 'data_type')
      .forEach(item => {
        datatypeList.value.push({ label: item.dictLabel, value: item.dictValue })
      })
    const dataTypeColumn = $table.getColumnByField('dataType')
    if (dataTypeColumn) {
      $table.setFilter(dataTypeColumn, datatypeList.value)
      $table.updateData()
    }
    //获取调制方式
    store.dict
      .filter(item => item.dictType == 'debug_mode')
      .forEach(item => {
        debugModeList.value.push({ label: item.dictLabel, value: item.dictValue })
      })
    debugModeList.value.sort((a, b) => {
      const valueA = parseInt(a.value)
      const valueB = parseInt(b.value)
      if (valueA < valueB) {
        return -1
      } else if (valueA > valueB) {
        return 1
      } else {
        return 0
      }
    })
    debugModeMessage.value = debugModeList.value
    const debugModeColumn = $table.getColumnByField('debugMode')
    if (debugModeColumn) {
      $table.setFilter(debugModeColumn, debugModeList.value)
      $table.updateData()
    }
  }
  const chartsStore = useChartsStore()
  // 跳到分析页面
  const goAnalysisView = row => {
    if (row.fileType === '0') {
      chartsStore.setFileInfo(transToStore(row), '0')
      router.push({
        name: 'SignalAnalyse'
      })
    } else {
      const files = []
      files.push(row)
      chartsStore.setFreqFiles(files)
      chartsStore.writeStorageFreqFiles()
      router.push({
        name: 'FreqAnalyse'
      })
    }
  }
  // 缓存文件数据转化
  const transToStore = row => {
    const result = {}
    const requiredKeys = [
      'samplingRate',
      'bitRate',
      'centerFreqIn',
      'dataType',
      'debugMode',
      'fftSize',
      'fileType',
      'intermediateFrequencyBandwidth',
      'uploadTime',
      'iqReverse',
      'logarithm',
      'startOffset',
      'cutoffLength',
      'steplen',
      'powerMultiple'
    ]
    Object.keys(row).forEach(key => {
      if (!requiredKeys.includes(key)) {
        return
      }
      if (typeof row[key] === 'string' && row[key].includes('Hz')) {
        result[key] = displayUnitConversion(row[key])
      } else {
        result[key] = row[key]
      }
    })
    result.fileName = row.fileName
    return result
  }
  //上传文件
  const uploadFile = () => {
    childValue.value = true
  }
  // 全局搜索方法
  const searchEvent = value => {
    file.filename = value.$input.props.modelValue
  }
  const clearName = () => {
    file.filename = ''
  }
  // IQ数据组织方式
  const formatDataOrganization = value => {
    let txtArr = dataOrganizationList.value.filter(item => {
      return value == item.value || value == item.label
    })
    return txtArr.length > 0 ? txtArr[0].label : ''
  }
  // 文件类型
  const formatFiletype = value => {
    let txtArr = filetypeList.value.filter(item => {
      return value == item.value || value == item.label
    })
    return txtArr.length > 0 ? txtArr[0].label : ''
  }
  // IQ数据类型
  const formatIQDatatype = value => {
    let txtArr = datatypeList.value.filter(item => {
      return value == item.value || value == item.label
    })
    return txtArr.length > 0 ? txtArr[0].label : ''
  }
  // 调制方式
  const formatDebugMode = value => {
    let txtArr = debugModeList.value.filter(item => {
      return value == item.value || value == item.label
    })
    return txtArr.length > 0 ? txtArr[0].label : ''
  }
</script>
<style lang="less" scoped>
  .time-button {
    margin: 0 20px;
  }
  .interval-p {
    margin: 0 16px 0 0;
  }
  .search-button {
    margin: 0 0 0 20px;
  }
  .upload-button {
    margin: 0 0 0 20px;
  }
  .delete-all-button {
    margin: 0 0 0 20px;
  }

  .nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .home {
    background-color: none;
    padding: 0;
  }

  .exp-list {
    margin-bottom: 10px;

    .project-desc {
      height: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: pre-line;
      margin-bottom: 10px;
      font-size: 14px;
    }

    li {
      font-size: 12px;
      display: flex;
      color: #aaa;
      text-align: right;

      span {
        float: left;
        margin-right: 2em;
        color: #888;
        text-align: right;
      }

      & > div {
        flex: 1;
      }
    }
  }

  .uploadtime-style {
    display: flex;
  }
  .time-span {
    line-height: 42px;
    margin-right: 5px;
    margin-left: 10px;
  }
  /* 增加筛选颜色 */
  .keyword-lighten {
    color: #000;
    background-color: #ffff00;
  }
  .input-name {
    width: 100%;
    height: 34px;
    border-radius: 4px;
    outline: 0;
    margin: 0;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 0.6em;
    color: var(--chart-text-color);
    border: 1px solid #dcdfe6;
    background-color: var(--background-color);
    box-shadow: none;
  }
  .select-style {
    background-color: var(--background-color);
    border: 1px solid var(--chart-text-color);
    border-radius: 4px;
    padding: 8px;
    color: var(--chart-text-color);
    width: 100%;
    cursor: pointer;
  }
  :deep(.vxe-table--filter-wrapper .vxe-table--filter-body > li:hover) {
    background-color: var(--scrollbar-color) !important;
    border: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-table--filter-wrapper .vxe-table--filter-header > li:hover) {
    background-color: var(--scrollbar-color) !important;
    border: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-table--filter-wrapper) {
    background-color: var(--background-color) !important;
    border: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-select-option--wrapper) {
    background-color: var(--background-color) !important;
    border: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }

  :deep(.vxe-table--body) {
    width: 100% !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-table--header) {
    width: 100% !important;
  }
  :deep(.vxe-pager--goto) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-pager--num-btn) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-pager--next-btn) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-pager--jump-next) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-pager--jump-prev) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-pager--prev-btn) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-input--inner) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-table--header-wrapper) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  .vxe-toolbar {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  .vxe-page {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  .mytable-scrollbar {
    background-color: var(--background-color);
    color: var(--chart-text-color) !important;
    border: 1px solid var(--background-color) !important;
  }
  .mytable-scrollbar ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  /*滚动条的轨道*/
  .mytable-scrollbar ::-webkit-scrollbar-track-piece {
    background-color: var(--background-color);
  }
  /*滚动条里面的小方块，能向上向下移动*/
  .mytable-scrollbar ::-webkit-scrollbar-thumb {
    background-color: var(--chart-text-color);
    border-radius: 5px;
  }
  .mytable-scrollbar ::-webkit-scrollbar-thumb:hover {
    background-color: var(--chart-text-color);
  }
  .mytable-scrollbar ::-webkit-scrollbar-thumb:active {
    background-color: var(--chart-text-color);
  }
  /*边角，即两个滚动条的交汇处*/
  .mytable-scrollbar ::-webkit-scrollbar-corner {
    background-color: var(--background-color);
  }
</style>
