<template>
  <el-collapse v-model="activeItems" class="collapse-panels">
    <el-collapse-item
      v-for="panel in panels"
      :key="panel.value"
      :title="panel.title"
      :name="panel.value"
    >
      <component :is="dom[panel.value]" />
    </el-collapse-item>
  </el-collapse>
</template>

<script setup name="DigitalModulation">

import DemodSettings from './DemodSettings.vue'
import TriggerSettings from './TriggerSettings.vue'
import SyncSearch from './SyncSearch.vue'
import Equalization from './Equalization.vue'

const panels = [
  {
    title: '解调设置',
    value: 'DemodSettings',
  },
  {
    title: '触发设置',
    value: 'TriggerSettings'
  },
  {
    title: '同步搜索',
    value: 'SyncSearch'
  },
  {
    title: '均衡',
    value: 'Equalization'
  }
]

const dom = shallowReactive({
  DemodSettings, TriggerSettings, SyncSearch, Equalization
})

const activeItems = ref(panels.map(panel => panel.value))

</script>

<style scoped lang="scss">
.panels-leave-active,
.panels-enter-active {
  transition: all 0.8s ease-in-out;
}

.panels-enter-from {
  transform: translateY(-200px);
  opacity: 0;
}
.panels-leave-to {
  transform: translateY(-200px);
  opacity: 0;
}
</style>
