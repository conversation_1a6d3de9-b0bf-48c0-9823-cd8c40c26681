import request from '@/utils/request'

// 查询设备管理列表
export function getEquipList(data) {
  return request({
    url: '/device/list',
    method: 'post',
    data
  })
}

// 新增/更新 设备管理
export function addOrUpdateEquip(data) {
  return request({
    url: '/device/saveOrUpdate',
    method: 'post',
    data
  })
}
// 获取设备详情
export function getEquipDetail(equipId) {
  return request({
    url: '/device/detial?id='+equipId,
    method: 'get',
  })
}

// 删除设备
export function delEquip(data) {
  return request({
    url: '/device/del',
    method: 'post',
    data
  })
}
// 批量删除设备
export function batchDelEquip(data) {
  return request({
    url: '/device/batchDel',
    method: 'post',
    data
  })
}
// 恢复默认设备参数
export function restoreDefaultPara(id) {
  return request({
    url: '/device/restoreDefaultPara?id='+id,
    method: 'get',
  })
}
// 恢复默认扫描频段
export function restoreDefaultScan(id) {
  return request({
    url: '/device/restoreDefaultScan?id='+id,
    method: 'get',
  })
}
// 同步设备参数设置
export function syncParaSettings(id) {
  return request({
    url: '/device/syncParaSettings?id='+id,
    method: 'get',
  })
}
// 同步扫描频段设置
export function syncScanSettings(id) {
  return request({
    url: '/device/syncScanSettings?id='+id,
    method: 'get',
  })
}

// 新增/更新 设备参数
export function saveOrUpdatePara(data) {
  return request({
    url: '/device/saveOrUpdatePara',
    method: 'post',
    data
  })
}
// 新增/更新 扫描频段
export function saveOrUpdateScan(data) {
  return request({
    url: '/device/saveOrUpdateScan',
    method: 'post',
    data
  })
}