<template>
  <div class="app-container">
    <div class="ptitle">黑白名单</div>
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="信号名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入信号名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:export']"
          type="warning"
          plain
          icon="upload"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:edit']"
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      class="fit-table"
      :data="bwList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="信号类型" align="center" prop="type">
        <template #default="scope">
          <dict-tag :options="warn_mng_status" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="信号名称" align="center" prop="name" />
      <el-table-column label="中心频率（MHz）" align="center" prop="centerFreq">
        <template #default="scope">
          {{ scope.row.centerFreq / 1000000 }}
        </template>
      </el-table-column>

      <el-table-column label="信号带宽（KHz）" align="center" prop="signalBandwidth">
        <template #default="scope">
          {{ scope.row.signalBandwidth / 1000 }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:post:edit']"
            link
            icon="Edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['system:post:remove']"
            link
            icon="Delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改黑白名单对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="bwRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="信号类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio v-for="dict in warn_mng_status" :key="dict.value" :label="dict.value">
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="信号名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入信号名称" />
        </el-form-item>
        <el-form-item label="中心频率" prop="centerFreq">
          <el-input
            v-model="form.centerFreq"
            placeholder="请输入中心频率"
            maxlength="20"
            @input="
              form.centerFreq = form.centerFreq.replace(
                /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                ''
              )
            "
          />
        </el-form-item>
        <el-form-item label="信号带宽" prop="signalBandwidth">
          <el-input v-model="form.signalBandwidth" placeholder="请输入信号带宽" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Post">
  import { getBWList, addOrUpdateBW, getBWDetail, delBW, batchDelBW } from '@/api/warning/bwList'
  import { interfaceUnitConversion } from '@/utils/utils'
  const { proxy } = getCurrentInstance()
  const { warn_mng_status } = proxy.useDict('warn_mng_status')

  /**黑白名单列表 */
  const bwList = ref([])
  const open = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      name: ''
    },
    rules: {
      type: [{ required: true, message: '信号类型不能为空', trigger: 'blur' }],
      name: [{ required: true, message: '信号名称不能为空', trigger: 'blur' }],
      centerFreq: [{ required: true, message: '中心频率不能为空', trigger: 'blur' }],
      signalBandwidth: [{ required: true, message: '信号带宽不能为空', trigger: 'blur' }]
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  /** 查询黑白名单列表 */
  function getList() {
    loading.value = true
    getBWList(queryParams.value).then(response => {
      bwList.value = response.data.list
      total.value = response.data.total || 0
      loading.value = false
    })
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false
    reset()
  }
  /** 表单重置 */
  function reset() {
    form.value = {
      postId: undefined,
      type: '0',
      name: undefined,
      centerFreq: undefined,
      signalBandwidth: undefined
    }
    proxy.resetForm('bwRef')
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    handleQuery()
  }
  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = []
    selection.forEach(item => {
      ids.value.push(item.id)
    })
    single.value = selection.length != 1
    multiple.value = !selection.length
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset()
    open.value = true
    title.value = '添加黑白名单'
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset()
    const bwId = row.id || ids.value
    getBWDetail(bwId).then(response => {
      form.value = response.data
      form.value.type = form.value.type.toString()
      open.value = true
      title.value = '修改黑白名单'
    })
    // form.value = JSON.parse(JSON.stringify(row))
    // open.value = true
    // title.value = '修改黑白名单'
    // getBWDetail(row.id)
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['bwRef'].validate(valid => {
      if (valid) {
        const submitData = {
          name: form.value.name,
          type: form.value.type,
          centerFreqStr: interfaceUnitConversion(form.value.centerFreq),
          signalBandwidthStr: interfaceUnitConversion(form.value.signalBandwidth)
        }
        open.value = false
        if (form.value.id != undefined) {
          submitData.id = form.value.id
          addOrUpdateBW(submitData).then(response => {
            proxy.$modal.msgSuccess('修改成功')
            open.value = false
            getList()
          })
        } else {
          addOrUpdateBW(submitData).then(response => {
            proxy.$modal.msgSuccess('新增成功')
            open.value = false
            getList()
          })
        }
      }
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除')
      .then(function () {
        if (ids.value.length > 0) {
          return batchDelBW(ids.value)
        } else {
          return delBW({ id: row.id })
        }
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess('删除成功')
      })
      .catch(() => {})
  }
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      '/alert/blackwhite/export',
      {
        ...queryParams.value
      },
      `post_${new Date().getTime()}.xlsx`
    )
  }

  getList()
</script>
