<template>
  <div class="app-container" style="position: relative">
    <div id="many_line" ref="echart" class="many_line" />
  </div>
</template>
<script lang="ts" setup>
  import { onMounted ,watch,ref} from 'vue'
  import { getServer } from '@/api/monitor/server'
  import * as echarts from 'echarts/core'
  import { GridComponent, TooltipComponent } from 'echarts/components'
  import { LineChart } from 'echarts/charts'
  import { UniversalTransition } from 'echarts/features'
  import { CanvasRenderer } from 'echarts/renderers'
  const props = defineProps(['modelValue'])


  echarts.use([GridComponent, LineChart, CanvasRenderer, UniversalTransition, TooltipComponent])
  const data = ref([])
  let timer = null
  const eqlineChart = () => {
    // getServer().then(result => {
      let chartDom = document.getElementById('many_line')
      let myChart = echarts.init(chartDom)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
     
      // let data = props.modelValue.cpu.cpuNum ?  props.modelValue.cpu: form.value.cpu
      // console.log('data',data)
      // let data_x = ['核心数', '用户使用率', '系统使用率', '当前空闲率']
      // let data_total = [data.cpuNum , props.modelValue.cpu.used || 0,props.modelValue.cpu.sys || 0, props.modelValue.cpu.free || 0]
    
      var option
      option = {
        grid: {
          x: 30,
          y: 50,
          x2: 30,
          y2: 35
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#c4c4c4'
            }
          },
          axisLabel: {
            color: '#67727a',
            fontSize: '12px'
          },
          data: ['核心数', '用户使用率', '系统使用率', '当前空闲率']
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#67727a',
            formatter: '{value}',
            fontSize: '12px'
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#bfcbd9'
            }
          }
        },
        series: [
          {
            name: 'cpu',
            type: 'line',
            data: props.modelValue,
            smooth: true, //变为曲线 默认false折线
            symbolSize: 0, //去掉页面上显示的圆点
            lineStyle: {
              color: '#ffac19',
              opacity: 1
            },
            areaStyle: {
              color: '#ffac19',
              opacity: 0.3
            }
          }
        ]
      }
      myChart && myChart.setOption(option,true)
    // })
  }

  
  watch(
    () => props.modelValue,
    val => {
      if (val) {
        // timer = setInterval(eqlineChart, 5000);
        eqlineChart()
     
        // eqlineChart(props.modelValue)
    
      } 
    }
  )
  // const getList = (num) => {
  //   eqlineChart()
  //   timer && clearInterval(timer)
  //   timer = setInterval(() => {
  //     eqlineChart()
  //   }, num * 1000)
  // }
  onMounted(() => {
    eqlineChart()
    // getServer().then(res => {
    //   let arr = [res.data.cpu.cpuNum,res.data.cpu.used,res.data.cpu.sys,res.data.cpu.free]
    //  eqlineChart(arr)
    // })
  })

</script>
<style lang="less" scoped>
  .many_line {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }
  #searchType {
    position: absolute;
    right: 0;
    top: 0;
  }
</style>
