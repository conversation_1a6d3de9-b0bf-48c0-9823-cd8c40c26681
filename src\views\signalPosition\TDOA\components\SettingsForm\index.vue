<template>
  <div class="sidebar-settings h-full relative">
    <!--  定位信号 -->
    <div class="titleSetting">
      <span class="title-span">定位信号</span>
      <img style="width: 100%; height: 35px" src="@/assets/icons/svg/u246.svg" />
    </div>
    <div class="form-content">
      <slot name="signal" />
    </div>
    <!--  谱线设置 -->
    <div class="titleSetting">
      <span class="title-span">谱线设置</span>
      <img style="width: 100%; height: 35px" src="@/assets/icons/svg/u246.svg" />
    </div>
    <!-- 参考电平 -->
    <div class="form-content">
      <spectralLineSetting v-model="model.refLevel" />
    </div>
    <!--  定位设备 -->
    <div class="titleSetting">
      <span class="title-span">定位设备</span>
      <img style="width: 100%; height: 35px" src="@/assets/icons/svg/u246.svg" />
    </div>
    <div class="form-content">
      <slot name="equipment" />
    </div>

    <div class="flex justify-center absolute bottom-[15px] left-1/2 -translate-x-1/2">
      <el-button class="ctl-btn" :class="{ close: status }" @click="emit('start')">
        <x-icon
          class="mr-3"
          icon="start"
          source="cus"
          :color="!status ? '#80ffff' : ''"
          size="16"
        />
        启动
      </el-button>
      <el-button class="ctl-btn" :class="{ close: !status }" @click="emit('stop')">
        <x-icon
          class="mr-3"
          icon="scanstop"
          source="cus"
          :color="status ? '#80ffff' : ''"
          size="16"
        />
        停止
      </el-button>
    </div>
  </div>
</template>

<script setup name="ScanRightSettings">
  import spectralLineSetting from './SpectralLineSetting.vue'
  const props = defineProps({
    status: {
      type: Boolean,
      default: false
    },
    model: {
      type: Object,
      default: () => {}
    }
  })
  const emit = defineEmits(['start', 'stop'])
</script>
<style scoped>
  .titleSetting {
    width: 95%;
    height: 16px;
    margin: 0% 2% 0% 2%;
  }
  .title-span {
    color: var(--scan-text-color);
    position: relative;
    top: 22px;
    left: 2%;
  }

  .form-content {
    width: 100%;
    margin: 50px 0 0 10px;
  }
</style>
