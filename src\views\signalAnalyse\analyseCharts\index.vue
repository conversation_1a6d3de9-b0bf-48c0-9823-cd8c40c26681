<template>
  <div class="scan-charts">
    <div class="scan-toolbar justify-end">
      <div class="pd6 br1">
        <el-button @click="showResetZoom">全频段</el-button>
      </div>
      <!-- <div class="pd6 br1">
        <el-button v-if="viewType === 'scan'" @click="switchType">
          {{ model.type ? '离散' : '全景' }}
        </el-button>
        <el-button v-else-if="viewType === 'monitor'" @click="switchType">
          {{ model.type ? '雷达监测' : '连续波' }}
        </el-button>
        <el-button v-else>{{ model.type ? '雷达信号' : '连续波' }}</el-button>
      </div>
      <el-checkbox-group v-model="checkList" class="pd6 br1">
        <el-checkbox
          v-for="item in lineTypes"
          :key="item.value"
          v-model="item.value"
          :label="item.label"
          border
        />
      </el-checkbox-group>
      <div class="pd6 br1">
        <el-button @click="showResetZoom">全频段</el-button>
      </div>
      <div class="pd6 bl1" style="display: inline-flex">
        <el-select
          v-model="selectedFreStds"
          :teleported="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          effect="dark"
          value-key="label"
        >
          <el-option
            v-for="item in frequencyStandards"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
        <el-checkbox v-model="isActive" label="激活" border />
        <el-checkbox v-model="isTopFollow" label="峰值跟踪" border />
      </div>
      <div v-if="viewType !== 'signal'" class="pd6 bl1">
        <el-button @click="switchAssit">{{ assistChartName }}</el-button>
      </div> -->

      <el-select
        v-model="deviceCode"
        :teleported="false"
        effect="dark"
        value-key="label"
        class="pd6 bl1 w-[120px] equip"
      >
        <el-option
          v-for="item in deviceEnum"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </el-select>
    </div>
    <div class="main" style="display: flex">
      <!-- 频谱扫描echarts -->
      <div class="scanCharts" style="flex: 1">
        <div v-if="viewType === 'radar'" class="radar-chart">
          <slot
            name="radar"
            :show-lines="checkList"
            :selected-fremarkers="selectedFreStds"
            :is-active="isActive"
            :is-top-follow="isTopFollow"
          />
        </div>
        <div v-else class="spectrum">
          <!-- 信号检测echarts -->
          <Spectrum
            ref="spectrumRef"
            :key="usekey"
            :usekey="usekey"
            :show-lines="checkList"
            :selected-fremarkers="selectedFreStds"
            :is-active="isActive"
            :is-top-follow="isTopFollow"
            :data-list="dataList"
            :model="model"
          />
        </div>
        <!-- 瀑布图 -->
        <div v-show="assistChartType === 0" class="water-fall">
          <WaterFallPlot
            :is-online="true"
            :data="dataList.current"
            :height="50"
            :legend-width="60"
            :container-height="isRadarAnalyse ? 200 : 500"
          />
        </div>
        <!-- 占用度 -->
        <div v-show="assistChartType === 1" class="occupancy">
          <Occupancy :data="dataList.occupancy" :model="model" />
        </div>
      </div>
      <slot ref="analyseRef" />
    </div>
  </div>
</template>

<script setup name="AnalyseChart">
  import useScanStore from '@/store/modules/scanMonitor'
  import Spectrum from '@/components/SpectrumSignal/Spectrum.vue'
  import WaterFallPlot from '@/views/analyse/modules/WaterFallPlot.vue'
  import Occupancy from './Occupancy.vue'
  import { lineTypes } from '@/constant'
  import { ElMessage } from 'element-plus'

  const props = defineProps({
    viewType: {
      type: String,
      default: 'scan' // scan 扫描  monitor 监测
    },
    dataList: {
      type: Object,
      default: () => ({})
    },
    model: {
      type: Object,
      default: () => ({})
    },
    usekey: {
      type: String,
      default: 'default'
    },
    deviceEnum: {
      type: Array,
      default: () => []
    }
  })
  const frequencyStandards = [
    { label: '频标1', value: 1 },
    { label: '频标2', value: 2 },
    { label: '频标3', value: 3 },
    { label: '频标4', value: 4 },
    { label: '频标5', value: 5 }
  ]

  const scanStore = useScanStore()
  const spectrum = scanStore.signal

  const deviceCode = defineModel('deviceCode')
  let isRestoring = false // 标志位
  watch(deviceCode, (newValue, oldValue) => {
    // 如果正在恢复旧值，直接返回，避免死循环
    if (isRestoring) {
      isRestoring = false
      return
    }
    if (spectrum.status === 1) {
      ElMessage.warning('任务执行过程中，请先停止当前任务')
      // 设置标志位，并恢复旧值
      isRestoring = true
      deviceCode.value = oldValue // 恢复旧值
    } else {
      // 在此处处理允许更改的逻辑
      console.log('Device code changed to:', newValue)
    }
  })

  // const checkList = ref(['实时', '平均', '最大', '最小', '门限'])
  const checkList = ref(['实时'])
  const spectrumRef = ref(null)
  const selectedFreStds = ref([])
  const isActive = ref(true)
  const isTopFollow = ref(false)
  const assistChartType = ref(0)
  const assistChartName = computed(() => (assistChartType.value === 0 ? '瀑布图' : '占用度'))
  const isRadarAnalyse = computed(() => {
    return props.model.hideTab && props.model.type // 这个代表是雷达分析
  })
  const switchType = () => {
    props.model.type = Number(!props.model.type)
  }
  const switchAssit = () => {
    assistChartType.value = Number(!assistChartType.value)
  }
  const showResetZoom = () => {
    spectrumRef.value.instance.zoomOut()
  }
</script>

<style scoped>
  .scan-charts {
    padding: 12px 8px;
  }

  .scan-toolbar {
    display: flex;
    border-top: 1px solid var(--toolbar-color);
    border-bottom: 1px solid var(--toolbar-color);
  }

  .pd6 {
    padding: 6px;
  }

  .br1 {
    border-right: 1px solid var(--toolbar-color);
  }

  .bl1 {
    border-left: 1px solid var(--toolbar-color);
  }
  :deep(.el-input__inner) {
    color: #ffffff;
  }
</style>
