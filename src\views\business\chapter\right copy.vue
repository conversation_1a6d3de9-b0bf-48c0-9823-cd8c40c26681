<template>
  <el-col :span="20" class="app-container pl-4">
    <div class="ptitle">章节信息</div>
    <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
      <el-form-item label="章节编码" prop="chapterCode">
        <el-input
          v-model="queryParams.chapterCode"
          placeholder="请输入章节编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="章节名称" prop="chapterName">
        <el-input
          v-model="queryParams.chapterName"
          placeholder="请输入章节名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:chapter:add']"
          type="primary"
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="toggleExpandAll">展开/折叠</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>
    <div class="fit-table">
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        border
        stripe
        :data="chapterList"
        row-key="chapterId"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="章节名称" prop="chapterName" />
        <el-table-column label="章节编码" prop="chapterCode" />
        <el-table-column label="序号" width="80" prop="orderNum" />
        <el-table-column label="操作" fiex="right" width="200">
          <template #default="scope">
            <el-link
              v-hasPermi="['business:chapter:edit']"
              icon="Edit"
              @click="handleUpdate(scope.row)"
            >
              修改
            </el-link>
            <el-link
              v-hasPermi="['business:chapter:add']"
              icon="Plus"
              @click="handleAdd(scope.row)"
            >
              新增
            </el-link>
            <el-link
              v-hasPermi="['business:chapter:remove']"
              icon="Delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <ChapterDetail ref="detailRef" @act-success="getList()" />
  </el-col>
</template>
<script>
  import { listChapter, delChapter } from '@/api/business/chapter'
  import ChapterDetail from './detail.vue'
  export default {
    name: 'ChapterList',
    components: { ChapterDetail },
    props: ['courseId'],
    data() {
      return {
        chapterList: [],
        queryParams: {},
        loading: false,
        refreshTable: true,
        isExpandAll: true,
        showSearch: true
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      getList() {
        this.loading = true
        listChapter({ courseId: this.courseId, ...this.queryParams })
          .then(response => {
            this.chapterList = this.handleTree(response.data, 'chapterId', 'chapterPid')
          })
          .finally(() => {
            this.loading = false
          })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryRef')
        this.handleQuery()
      },

      /** 新增按钮操作 */
      handleAdd(row) {
        this.$refs.detailRef.show({
          chapterPid: row?.chapterId || '0',
          chapterId: '',
          courseId: this.courseId
        })
      },

      /** 展开/折叠操作 */
      toggleExpandAll() {
        this.refreshTable = false
        this.isExpandAll = !this.isExpandAll
        this.$nextTick(() => {
          this.refreshTable = true
        })
      },

      /** 修改按钮操作 */
      handleUpdate({ chapterId }) {
        this.$refs.detailRef.show({
          chapterId,
          courseId: this.courseId
        })
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        this.$modal
          .confirm('是否确认删除所选课程章节？')
          .then(function () {
            return delChapter(row.chapterId)
          })
          .then(() => {
            this.getList()
            this.$modal.msgSuccess('删除成功')
          })
          .catch(() => {})
      }
    }
  }
</script>
