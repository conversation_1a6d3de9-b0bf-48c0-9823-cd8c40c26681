import request from '@/utils/request'

// 查询实验信息列表
export function listExperiment (query) {
  return request({
    url: '/business/experiment/list',
    method: 'get',
    params: query
  })
}

// 查询实验信息详细
export function getExperiment (expId) {
  return request({
    url: '/business/experiment/' + expId,
    method: 'get'
  })
}

// 新增实验信息
export function addExperiment (data) {
  return request({
    url: '/business/experiment',
    method: 'post',
    data: data
  })
}

// 修改实验信息
export function updateExperiment (data) {
  return request({
    url: '/business/experiment',
    method: 'put',
    data: data
  })
}
// 实验项目审批
export function approvalExperiment (data) {
  return request({
    url: '/business/experiment/approval',
    method: 'put',
    data: data
  })
}

// 删除实验信息
export function delExperiment (expId) {
  return request({
    url: '/business/experiment/' + expId,
    method: 'delete'
  })
}

// 实验成果列表
export function fileExperiment (expId) {
  return request({
    url: '/business/experiment/fiels/' + expId
  })
}

// 实验学生查询
export function expstudentList (params) {
  return request({
    url: '/business/expstudent/list',
    params
  })
}

//添加实验学生
export function expstudentAdd (data) {
  return request({
    url: '/business/expstudent',
    method: 'post',
    data
  })
}

//修改实验学生
export function expstudentEdit (data) {
  return request({
    url: '/business/expstudent',
    method: 'put',
    data
  })
}
//实验学生
export function expstudentUpdate (data) {
  return request({
    url: '/business/expstudent',
    method: 'put',
    data
  })
}
//删除实验学生
export function expstudentRemove (stuId) {
  return request({
    url: '/business/expstudent/' + stuId,
    method: 'delete'
  })
}


//信号文件列表查询
export function allSignalListGet(data){
  return request({
    url:"/file/selectfile",
    method:"post",
    data:data
  })
}
//单个文件列表查询
export function signalListGet(data){
  return request({
    url:"/file",
    method:"get",
    params: data
  })
}
//信号文件新增
export function signalListAdd(data){
  return request({
    url:"/file/add",
    method:"post",
    data: data
  })
}
//信号文件修改
export function signalListEdit(data){
  return request({
    url:"/file/edit",
    method:"put",
    data
  })
}
//信号文件导入
export function signalFileExport(){
  return request({
    url:"/file/importData",
    method:"post",
  })
}
//信号文件下载
export function signalFileLoad(data){
  return request({
    url:"/file/export",
    method:"get",
    params: data
  })
}
//获取字典数据
export function signalDataGet(query){
  return request({
    url:"/file/dataType",
    method:"get",
    params:query
  })
}