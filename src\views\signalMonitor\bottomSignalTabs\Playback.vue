<template>
  <div class="container ml-5 px-2 py-1">
    <Slider
      v-model="process"
      class="my-5"
      :min="0"
      :max="allNum"
      :view-num="viewNum"
      :is-drag="true"
    />
    <el-row>
      <el-col :span="12">
        <div class="play-tool">
          <el-button style="margin-left: 0" @click="play">
            <x-icon icon="CaretRight" source="el" size="24" />
          </el-button>
          <el-button @click="stop">
            <x-icon icon="stop" size="24" source="cus" />
          </el-button>
          <el-button @click="pause">
            <x-icon icon="pause" size="24" source="cus" />
          </el-button>
          <el-button @click="fastback">
            <x-icon icon="fastback" size="24" source="cus" />
          </el-button>
          <el-button @click="back">
            <x-icon icon="back" size="24" source="cus" />
          </el-button>
          <el-button @click="forward">
            <x-icon icon="forward" size="24" source="cus" />
          </el-button>
          <el-button @click="refresh">
            <x-icon icon="Refresh" size="24" source="el" />
          </el-button>
          <!-- <el-checkbox v-model="isLoop" class="ml-4" label="循环播放" /> -->
          <el-tooltip :content="file.name" placement="top">
            <span class="current-paly"> {{ statusText }} {{ file.name }} </span>
          </el-tooltip>
        </div>
        <div class="upload-file-bar">
          <el-button style="width: 200px" @click="addPlayFile">打开文件</el-button>
        </div>
      </el-col>
      <el-col :span="12">
        <el-form class="play-control">
          <el-form-item label="步进大小(采样点)">
            <el-input v-model="settings.step" />
          </el-form-item>
          <el-form-item label="回放速度(ms)">
            <el-input v-model="settings.playbackRate" />
          </el-form-item>
          <el-form-item label="自动循环">
            <el-checkbox v-model="settings.autoLoop" />
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-dialog v-model="fileListVisible" title="文件列表" class="el-tabs">
      <el-table v-loading="loading" :data="fileList">
        <el-table-column property="fileName" label="文件名" />
        <el-table-column property="uploadTime" label="采集时间">
          <template #default="{ row }">
            {{ dayjs(row.uploadTime, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column property="op" label="操作" width="200">
          <template #default="{ row }">
            <el-button @click="playfile(row)">播放</el-button>
            <el-button @click="downFile(row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup name="Playbar">
  import Slider from '@/components/Slider'
  import useScanStore from '@/store/modules/scanMonitor'
  import dayjs from 'dayjs'
  import { ElMessage } from 'element-plus'
  import { allSignalListGet } from '@/api/singalManage'
  import { downFile } from '@/api/tool/filemanage'
  import { queryFileByte, queryFileFft } from '@/api/charts'

  let currentFileFfts = []
  const scanStore = useScanStore()
  const viewNum = ref(0)
  const allNum = ref(0)
  const process = ref(0)
  const interval = ref(null)
  const loading = ref(false)
  const fileListVisible = ref(false)
  const fileList = ref([])
  const file = reactive({
    name: '',
    dateTime: ''
  })
  const settings = reactive({
    step: 2000,
    playbackRate: 1000,
    autoLoop: true
  })
  const statusText = computed(() => {
    if (interval.value) {
      return '播放中'
    } else if (process.value === allNum.value && allNum.value !== 0) {
      return '播放完毕'
    }
    return '等待播放'
  })
  const addPlayFile = () => {
    if (scanStore.monitor.status === 1) {
      ElMessage.warning('请先停止信号监测再播放')
      return
    }
    getFileList()
    fileListVisible.value = true
  }
  const playfile = async row => {
    loading.value = true
    const rsp = await queryFileByte(row)
    loading.value = false
    if (!rsp || !rsp.data) {
      ElMessage.error('播放失败')
      return
    }
    readRowInfo(rsp.data, row) // 生成面板信息
    fileListVisible.value = false
    play()
  }
  const play = () => {
    if (!file.name) {
      ElMessage.warning('请先添加播放文件')
      return
    }
    if (interval.value) {
      return
    }
    interval.value = setInterval(() => {
      generatePlayFfts(process.value) // 生成播放fft
      process.value += viewNum.value
      if (process.value >= allNum.value) {
        if (settings.autoLoop) {
          process.value = 0
        } else {
          process.value = allNum.value
          pause()
        }
      }
    }, settings.playbackRate)
  }
  const readRowInfo = (data, row) => {
    allNum.value = data.pIQ
    process.value = 0
    const recordNum = getRecordNum(row.fileName)
    viewNum.value = Math.floor(allNum.value / recordNum)
    file.dateTime = dayjs(row.uploadTime, 'YYYYMMDDHHmmss').format('YYYY/MM/DD HH:mm:ss')
    file.name = row.fileName
    currentFileFfts = data.pIQ_In
  }
  const getRecordNum = fileName => {
    const arr = fileName.split('_')
    const num = arr[arr.length - 2] * 1
    if (isNaN(num)) {
      return 50
    }
    return num
  }
  const generatePlayFfts = start => {
    if (start + viewNum.value > currentFileFfts.length) {
      start = currentFileFfts.length - viewNum.value
    }
    scanStore.playFfts = currentFileFfts.slice(start, start + viewNum.value)
  }
  const pause = () => {
    clearInterval(interval.value)
    interval.value = null
  }
  const stop = () => {
    pause()
    process.value = 0
  }
  const fastback = () => {
    process.value = 0
  }
  const back = () => {
    process.value -= viewNum.value
  }
  const forward = () => {
    process.value += viewNum.value
  }
  const getFileList = () => {
    allSignalListGet({ pageNum: 1, pageSize: 9999 }).then(rsp => {
      console.log(rsp)
      fileList.value = rsp.data.rows
    })
  }
</script>

<style scoped lang="scss">
  .container {
    background-color: #222;
    color: var(--scan-play-color);
    font-weight: 100;
    height: 260px;
    padding: 20px 10px;

    .container-header {
      display: flex;
      justify-content: center;
      cursor: pointer;
    }

    .play-tool {
      margin: 2px;

      .el-button {
        padding: 4px 0;
        margin: 0;
        border-radius: 0;
        border: var(--play-bar-slider-border);
        background-color: var(--play-bar-slider);
        margin: 2px;
        width: auto;
        height: auto;

        :hover {
          background-color: #7c4918;
        }

        .x-icon {
          color: var(--scan-text-color);
        }
      }
    }

    .upload-file-bar {
      margin-top: 30px;

      .el-button {
        background-color: var(--btn-bg);
        border-color: var(--play-bar-active);
        color: var(--table-scrollbar-color);
      }
    }

    .play-control {
      .el-input {
        --el-input-focus-border-color: var(--play-bar-active);
        --el-input-border-color: var(--play-bar-active);
        --el-select-input-focus-border-color: var(--play-bar-active);
        --el-input-hover-border-color: var(--play-bar-active);
        --el-text-color-regular: var(--scan-text-color);

        :deep(.el-input__wrapper) {
          background-color: var(--input-bg);
        }
      }

      .el-checkbox {
        --main-color: var(--input-bg);
      }
    }
  }
</style>
