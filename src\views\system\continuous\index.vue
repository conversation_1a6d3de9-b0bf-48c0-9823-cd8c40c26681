<template>
  <!-- 表格数据 -->
  <div class="fit-table">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="addContinuousFn"
        >
          新增
        </el-button>
      </el-col>
  
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          @click="delContinuousFn"
        >
          删除
        </el-button>
      </el-col>
    </el-row>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="continuousList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" />
      <el-table-column label="通用参数" align="center">
        <el-table-column label="信号编号" prop="signalNumber" width="100" />
        <el-table-column label="信号频率(Hz)" prop="signalFrequency" width="120" />
        <el-table-column label="信号带宽(Hz)" prop="signalBandwidth" width="120" />
        <el-table-column label="信号场强(dBμV/m)" prop="signalStrength" width="150" />
        <el-table-column label="载噪比(dBm)" prop="carrierToNoiseRatio" width="120" />
      </el-table-column>
      <el-table-column label="连续波信号分析结果" align="center">
        <el-table-column label="分析时间" prop="analysisTime" width="120" />
        <el-table-column label="中心频率(Hz)" prop="centerFrequency" width="120" />
        <el-table-column label="信号场强开关" prop="signalFieldStrengthSwitch" width="120" />
        <el-table-column label="信号场强(dBμV/m)" prop="continueSignalStrength" width="150" />
        <el-table-column label="信号电平(dBμV)" prop="signalLevel" width="140" />
        <el-table-column label="调制方式" prop="modulationMethod" width="120" />
        <el-table-column label="xdB带宽(dBm)" prop="xdbBandwidth" width="130" />
        <el-table-column label="β带宽" prop="continueBandwidth" width="120" />
        <el-table-column label="码速率(bps)" prop="continueCodeRate" width="120" />
        <el-table-column label="调制度(%)" prop="adjustmentSystem" width="120" />
        <el-table-column label="频偏(Hz)" prop="frequencyOffset" width="120" />
      </el-table-column>
      <el-table-column label="跳频信号分析结果" align="center">
        <el-table-column label="网台序号" prop="networkStationSerialNumber" width="120" />
        <el-table-column label="跳速(次/s)" prop="jumpSpeed" width="120" />
        <el-table-column label="截获频率个数" prop="numberOfInterceptionFrequencies" width="120" />
        <el-table-column label="频率列表" prop="frequencyList" width="120" />
      </el-table-column>
      <el-table-column label="扩频信号分析结果" align="center">
        <el-table-column label="频率(Hz)" prop="frequency" />
        <el-table-column label="场强(dBμV/m)" prop="fieldStrength" width="120" />
        <el-table-column label="信号带宽(Hz)" prop="speciallySignalBandwidth" width="120" />
        <el-table-column label="扩频码长" prop="spreadSpectrumCodeLength" width="120" />
      </el-table-column>
      <el-table-column label="特定信号类型识别结果" align="center">
        <el-table-column label="信号类型" prop="signalType" width="120" />
        <el-table-column label="制式" prop="standard" />
        <el-table-column label="信号频率(Hz)" prop="specialSignalFrequency" width="120" />
        <el-table-column label="带宽(Hz)" prop="bandwidth" />
        <el-table-column label="调制方式" prop="modulationMethodSpread" width="120" />
        <el-table-column label="码速率" prop="codeRate" />
        <el-table-column label="步长" prop="stepSize" />
        <el-table-column label="子载波数" prop="numberOfSubcarriers" width="120" />
        <el-table-column label="波形" prop="waveForm" />
        <el-table-column label="脉冲长度(μs)" prop="pulseLength" width="120" />
      </el-table-column>
      <el-table-column label="测向结果" align="center">
        <el-table-column label="示向度" prop="directionality" />
        <el-table-column label="俯仰角" prop="pitchAngle" />
        <el-table-column label="测向质量" prop="directionFindingQuality" width="120" />
        <el-table-column label="信号场强" prop="directionSignalStrength" width="120" />
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="160"
        fixed="right"
      >
        <template #default="scope">
          <el-button text link @click="editContinuousFn(scope.row)">
            修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <pagination
    v-show="total > 0"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :total="total"
    @pagination="getList"
  />

  <el-dialog
    v-model="addContinuous"
    width="900px"
    append-to-body
    :rules="rules"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px"
    >
      <span class="titleStyle">通用参数</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="信号编号" prop="signalNumber">
            <el-input v-model="form.signalNumber" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号频率(Hz)" prop="signalFrequency">
            <el-input v-model="form.signalFrequency" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号带宽(Hz)" prop="signalBandwidth">
            <el-input v-model="form.signalBandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强(dBμV/m)" prop="signalStrength">
            <el-input v-model="form.signalStrength" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="载噪比(dBm)" prop="carrierToNoiseRatio">
            <el-input v-model="form.carrierToNoiseRatio" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">连续波信号分析结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="分析时间" prop="analysisTime">
            <el-input v-model="form.analysisTime" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="中心频率(Hz)" prop="centerFrequency">
            <el-input v-model="form.centerFrequency" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强开关">
            <el-select v-model="form.signalFieldStrengthSwitch" style="width:100%">
              <el-option label="信号场强" :value="0" />
              <el-option label="信号电平" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强(dBμV/m)" prop="continueSignalStrength">
            <el-input v-model="form.continueSignalStrength" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号电平(dBμV)" prop="signalLevel">
            <el-input v-model="form.signalLevel" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调制方式">
            <el-select v-model="form.modulationMethod" style="width:100%">
              <el-option
                v-for="item in modulationMethodList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="xdB带宽(dBm)" prop="xdbBandwidth">
            <el-input v-model="form.xdbBandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="β带宽(Hz)" prop="continueBandwidth">
            <el-input v-model="form.continueBandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="码速率(bps)" prop="continueCodeRate">
            <el-input v-model="form.continueCodeRate" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调制度(%)" prop="adjustmentSystem">
            <el-input v-model="form.adjustmentSystem" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="频偏(Hz)" prop="frequencyOffset">
            <el-input v-model="form.frequencyOffset" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">跳频信号分析结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="网台序号" prop="networkStationSerialNumber">
            <el-input v-model="form.networkStationSerialNumber" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="跳速(次/s)" prop="jumpSpeed">
            <el-input v-model="form.jumpSpeed" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="截获频率个数" prop="numberOfInterceptionFrequencies">
            <el-input v-model="form.numberOfInterceptionFrequencies" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="频率列表" prop="frequencyList">
            <el-input v-model="form.frequencyList" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">扩频信号分析结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="频率(Hz)" prop="frequency">
            <el-input v-model="form.frequency" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="场强(dBμV/m)" prop="fieldStrength">
            <el-input v-model="form.fieldStrength" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号带宽(Hz)" prop="speciallySignalBandwidth">
            <el-input v-model="form.speciallySignalBandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="扩频码长" prop="spreadSpectrumCodeLength">
            <el-input v-model="form.spreadSpectrumCodeLength" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">特定信号类型识别结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="信号类型">
            <el-select v-model="form.signalType" style="width:100%">
              <el-option
                v-for="item in signalTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="制式">
            <el-select v-model="form.standard" style="width:100%">
              <el-option
                v-for="item in standardList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号频率(Hz)" prop="specialSignalFrequency">
            <el-input v-model="form.specialSignalFrequency" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="带宽(Hz)" prop="bandwidth">
            <el-input v-model="form.bandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调制方式">
            <el-select v-model="form.modulationMethodSpread" style="width:100%">
              <el-option
                v-for="item in modulationMethodList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="码速率" prop="codeRate">
            <el-input v-model="form.codeRate" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="步长" prop="stepSize">
            <el-input v-model="form.stepSize" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="子载波数" prop="numberOfSubcarriers">
            <el-input v-model="form.numberOfSubcarriers" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="波形">
            <el-select v-model="form.waveForm" style="width:100%">
              <el-option label="BW0" :value="0" />
              <el-option label="BW1" :value="1" />
              <el-option label="BW2" :value="2" />
              <el-option label="BW3" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉冲长度(μs)" prop="pulseLength">
            <el-input v-model="form.pulseLength" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">测向结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="示向度" prop="directionality">
            <el-input v-model="form.directionality" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="俯仰角" prop="pitchAngle">
            <el-input v-model="form.pitchAngle" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="测向质量" prop="directionFindingQuality">
            <el-input v-model="form.directionFindingQuality" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强" prop="directionSignalStrength">
            <el-input v-model="form.directionSignalStrength" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(formRef)">确 定</el-button>
        <el-button @click="closeModel">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog
    v-model="editContinuous"
    width="800px"
    append-to-body
    :rules="rules"
  >
    <el-form
      ref="formRef"
      :model="editform"
      :rules="rules"
      label-width="140px"
    >
      <span class="titleStyle">通用参数</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="信号编号" prop="signalNumber">
            <el-input v-model="editform.signalNumber" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号频率(Hz)" prop="signalFrequency">
            <el-input v-model="editform.signalFrequency" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号带宽(Hz)" prop="signalBandwidth">
            <el-input v-model="editform.signalBandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强(dBμV/m)" prop="signalStrength">
            <el-input v-model="editform.signalStrength" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="载噪比(dBm)" prop="carrierToNoiseRatio">
            <el-input v-model="editform.carrierToNoiseRatio" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">连续波信号分析结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="分析时间" prop="analysisTime">
            <el-input v-model="editform.analysisTime" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="中心频率(Hz)" prop="centerFrequency">
            <el-input v-model="editform.centerFrequency" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强开关">
            <el-select v-model="editform.signalFieldStrengthSwitch" placeholder="请选择" style="width:100%">
              <el-option label="信号场强" :value="0" />
              <el-option label="信号电平" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强(dBμV/m)" prop="continueSignalStrength">
            <el-input v-model="editform.continueSignalStrength" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号电平(dBμV)" prop="signalLevel">
            <el-input v-model="editform.signalLevel" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调制方式" prop="modulationMethod">
            <el-select v-model="editform.modulationMethod" style="width:100%">
              <el-option
                v-for="item in modulationMethodList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="xdB带宽(dBm)" prop="xdbBandwidth">
            <el-input v-model="editform.xdbBandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="β带宽(Hz)" prop="continueBandwidth">
            <el-input v-model="editform.continueBandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="码速率(bps)" prop="continueCodeRate">
            <el-input v-model="editform.continueCodeRate" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调制度(%)" prop="adjustmentSystem">
            <el-input v-model="editform.adjustmentSystem" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="频偏(Hz)" prop="frequencyOffset">
            <el-input v-model="editform.frequencyOffset" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">跳频信号分析结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="网台序号" prop="networkStationSerialNumber">
            <el-input v-model="editform.networkStationSerialNumber" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="跳速(次/s)" prop="jumpSpeed">
            <el-input v-model="editform.jumpSpeed" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="截获频率个数" prop="numberOfInterceptionFrequencies">
            <el-input v-model="editform.numberOfInterceptionFrequencies" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="频率列表" prop="frequencyList">
            <el-input v-model="editform.frequencyList" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">扩频信号分析结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="频率(Hz)" prop="frequency">
            <el-input v-model="editform.frequency" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="场强(dBμV/m)" prop="fieldStrength">
            <el-input v-model="editform.fieldStrength" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号带宽(Hz)" prop="speciallySignalBandwidth">
            <el-input v-model="editform.speciallySignalBandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="扩频码长" prop="spreadSpectrumCodeLength">
            <el-input v-model="editform.spreadSpectrumCodeLength" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">特定信号类型识别结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="信号类型">
            <el-select v-model="editform.signalType" style="width:100%">
              <el-option
                v-for="item in signalTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="制式">
            <el-select v-model="editform.standard" style="width:100%">
              <el-option
                v-for="item in standardList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号频率(Hz)" prop="specialSignalFrequency">
            <el-input v-model="editform.specialSignalFrequency" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="带宽(Hz)" prop="bandwidth">
            <el-input v-model="editform.bandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调制方式">
            <el-select v-model="editform.modulationMethodSpread" style="width:100%">
              <el-option
                v-for="item in modulationMethodList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="码速率" prop="codeRate">
            <el-input v-model="editform.codeRate" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="步长" prop="stepSize">
            <el-input v-model="editform.stepSize" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="子载波数" prop="numberOfSubcarriers">
            <el-input v-model="editform.numberOfSubcarriers" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="波形">
            <el-select v-model="editform.waveForm" style="width:100%">
              <el-option label="BW0" :value="0" />
              <el-option label="BW1" :value="1" />
              <el-option label="BW2" :value="2" />
              <el-option label="BW3" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉冲长度(μs)" prop="pulseLength">
            <el-input v-model="editform.pulseLength" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">测向结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="示向度" prop="directionality">
            <el-input v-model="editform.directionality" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="俯仰角" prop="pitchAngle">
            <el-input v-model="editform.pitchAngle" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="测向质量" prop="directionFindingQuality">
            <el-input v-model="editform.directionFindingQuality" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强" prop="directionSignalStrength">
            <el-input v-model="editform.directionSignalStrength" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="closeEditModel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { listcontinueWave,addcontinueWave,delcontinueWave,updatecontinueWave} from '@/api/system/continuewave'
import useDictStore from '@/store/modules/dict'
const loading = ref(false)
const total = ref(0)
const continuousList = ref([])
const addContinuous = ref(false)
const editContinuous = ref(false)
const deleteId = ref([])
const modulationMethodList = ref([])
const formRef = ref(null);
const standardList = ref([
  {label:'MYGB  ',value:0},
  {label:'调幅广播',value:1},
  {label:'调频广播',value:2},
  {label:'单边带话音',value:3},
])
const signalTypeList = ref([
  {label:'未知  ',value:0},
  {label:'2GALE',value:1},
  {label:'3GALE',value:2},
  {label:'Link11',value:3},
  {label:'Link4A',value:4},
  {label:'Link16',value:5},
  {label:'AIS',value:6},
  {label:'ADS_B',value:7},
  {label:'敌我识别',value:8},
  {label:'塔康',value:9},
  {label:'导航',value:10},
  {label:'移动通信',value:11},
])
const { resetFields,validate } = toRefs(formRef);
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
})
const commonNumberRules = { pattern: /^[0-9]+$/, message: "请输入数字整数", trigger: "blur" }
const pointNumberRules = { pattern: /^\d{1,6}(\.\d{2})?$/, message: "请输入", trigger: "blur" }
const pointDigitNumberRules = { pattern: /^\d{1,6}(\.\d{1})?$/, message: "请输入", trigger: "blur" }
const dateRules = { 
  pattern: /^(19\d{2}|20\d{2})-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$/, 
  message: "请输入正确的日期格式", 
  trigger: "blur" 
}
const timeRules = {
  pattern:/^(19\d{2}|20\d{2})-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]) (0\d|1\d|2[0-3]):([0-5]\d):([0-5]\d)$/,
  message: "请输入正确的时间格式", 
  trigger: "blur" 
}
const rules = reactive({
  signalNumber: [{ pattern: /^10\d{6}$/, message: "请输入10开头的8位整数", trigger: "blur" }],
  signalFrequency: [commonNumberRules],
  signalBandwidth: [commonNumberRules],
  signalStrength: [pointNumberRules],
  carrierToNoiseRatio: [pointNumberRules],
  analysisTime: [timeRules],
  centerFrequency: [commonNumberRules],
  continueSignalStrength: [pointNumberRules],
  signalLevel: [pointNumberRules],
  xdbBandwidth: [commonNumberRules],
  continueBandwidth: [commonNumberRules],
  continueCodeRate: [commonNumberRules],
  adjustmentSystem: [pointNumberRules],
  frequencyOffset: [commonNumberRules],
  networkStationSerialNumber: [commonNumberRules],
  jumpSpeed: [commonNumberRules],
  numberOfInterceptionFrequencies: [commonNumberRules],
  frequencyList:[{ pattern: /^[0-9,]+$/, message: "请重新输入", trigger: "blur" }],
  frequency: [commonNumberRules],
  fieldStrength: [pointNumberRules],
  speciallySignalBandwidth: [commonNumberRules],
  spreadSpectrumCodeLength: [commonNumberRules],
  specialSignalFrequency: [commonNumberRules],
  bandwidth: [commonNumberRules],
  codeRate: [commonNumberRules],
  stepSize: [commonNumberRules],
  numberOfSubcarriers: [commonNumberRules],
  pulseLength: [pointNumberRules],
  directionality: [pointDigitNumberRules],
  pitchAngle: [pointDigitNumberRules],
  directionFindingQuality:[{ pattern: /^(100|([1-9][0-9]?|0))$/, message: "请输入0到100的数字", trigger: "blur" }],
  directionSignalStrength: [pointNumberRules],
});
const form = ref({})
const editform = ref({})
const addContinuousFn = () => {
  addContinuous.value = true
}
const gettype = () => {
  const store = useDictStore()
  const debugmode = store.dict.filter(item => item.dictType == "debug_mode" )
  debugmode.forEach(item=>{
    modulationMethodList.value.push({label:item.dictLabel,value:item.dictValue})
  })
}
const delContinuousFn = () => {
  if(deleteId.value.length !== 0){
    delcontinueWave(deleteId.value).then(res=>{
      console.log(res)
      getList()
    })
  }else{
    alert('请选择删除的内容')
  }
}
const editContinuousFn = (row) => {
  editform.value  = { ...row }
  editContinuous.value = true
}
const submitEditForm = () => {
  updatecontinueWave(editform.value).then(res=>{
    getList()
    editContinuous.value = false
  })
}
const closeEditModel = () => {
  editContinuous.value = false
}
const closeModel = () => {
  addContinuous.value = false
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      addcontinueWave(form.value).then(res=>{
        addContinuous.value = false
        getList()
        formRef.value.resetFields();
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  selection.map(item=>{
    deleteId.value.push(item.id)
    deleteId.value = [...new Set(deleteId.value)];
  })
}
const getList = () => {
  listcontinueWave().then(res=>{
    continuousList.value = res.rows
  })
}
const handleDelete = (row) => {
  console.log(row)
}
onMounted(() => {
  getList()
  gettype()
})

watchEffect(() => {})

computed(() => {})
</script>
<style>
.online-main{
  height: 100%;
  display: flex;
  flex-direction: column;
}
.titleStyle{
  margin-bottom: 11px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}
:deep(.el-select){
  width:100%
}
</style>