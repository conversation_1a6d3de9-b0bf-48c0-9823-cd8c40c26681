<template>
  <div class="actions-list">
    <template v-for="(v, i) in actions">
      <el-button
        v-if="v.type != 'slot' && (v.filter ? v.filter() : true)"
        v-hasPermi="v.permission"
        :type="v.type || ''"
        :plain="v.isPlain || true"
        :disabled="v.disabled && v.disabled()"
        @click="v.click"
      >
        <x-icon v-if="v.icon" :source-icon="v.icon" />
        {{ v.label }}
      </el-button>
      <slot v-else :name="v.slotName" />
    </template>
  </div>
</template>
<script>
  export default {
    name: 'Actions',
    props: {
      actions: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {}
    },
    methods: {
      demo() {}
    }
  }
</script>
<style lang="less"></style>
