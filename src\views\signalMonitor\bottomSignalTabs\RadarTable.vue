<template>
  <el-table border :data="currentList" :height="260" @cell-click="onCellClick">
    <el-table-column label="序号" type="index" width="60" align="center" />
    <el-table-column label="批号" prop="batchNumber" width="100" align="center" />
    <el-table-column label="真方位（C°）" prop="direction" align="center" :width="120" />
    <el-table-column label="机方位（C°）" prop="directionCar" align="center" width="120" />
    <el-table-column label="更新次数" prop="numberOfIntercepts" align="center" :width="100" />
    <el-table-column label="幅度" prop="amplitudeCenterValue" align="center" />
    <el-table-column label="载频（MHz）" prop="operatingFrequencyValue" align="center" width="120">
      <template #default="{ row }">
        <div class="overflow">
          {{ transOperFreqValue(row.operatingFrequencyValue) }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="载频类型" prop="workingFrequencyType" align="center" :width="180">
      <template #default="{ row }">
        {{ queryWftTypeLabel(row.workingFrequencyType) }}
      </template>
    </el-table-column>
    <el-table-column label="重周" prop="recurrenceValue" align="center" :width="180">
      <template #default="{ row }">
        <div class="overflow">
          {{ row.operatingFrequencyValue.join() }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="重周类型" prop="recurrenceType" align="center" :width="180">
      <template #default="{ row }">
        {{ queryRecurrenceTypeLabel(row.recurrenceType) }}
      </template>
    </el-table-column>
    <el-table-column label="脉宽" prop="pulseWidthValue" align="center" :width="180">
      <template #default="{ row }">
        <div class="overflow">
          {{ row.pulseWidthValue.join() }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="脉宽类型" prop="pulseWidthType" align="center" :width="180">
      <template #default="{ row }">
        {{ queryPulseWidthTypeLabel(row.recurrenceType) }}
      </template>
    </el-table-column>
    <el-table-column label="可信度" prop="reliability" align="center" />
    <el-table-column label="出现时间" prop="initialInterceptionTime" align="center" :width="180">
      <template #default="{ row }">
        {{ transTime(row.initialInterceptionTime) }}
      </template>
    </el-table-column>
    <el-table-column label="更新时间" prop="latestInterceptionTime" align="center" :width="180">
      <template #default="{ row }">
        {{ transTime(row.latestInterceptionTime) }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="120" align="center" fixed="right">
      <template #default="{ row }">
        <el-button class="mt-2" size="small" @click="analyse(row)">分析</el-button>
        <el-button class="mt-2" size="small" @click="findDirection(row)">测向</el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="scantabs">
    <div
      v-for="item in spectrumList"
      :key="item.key"
      :class="{ active: activeKey === item.key }"
      class="st-item"
      @click="activeKey = item.key"
    >
      {{ item.value }}
    </div>
  </div>
  <el-tooltip
    v-model:visible="visible"
    :content="tooltipCtx"
    placement="top"
    effect="light"
    trigger="click"
    virtual-triggering
    :virtual-ref="triggerRef"
  />
</template>

<script setup>
  import useScanStore from '@/store/modules/scanMonitor'
  import { workingFrequencyTypes, recurrenceTypes, pulseWidthTypes } from '@/constant/types'
  import { round } from 'lodash'

  const props = defineProps({
    signalList: {
      type: Array,
      default: () => []
    }
  })
  const router = useRouter()
  const { radar } = useScanStore()
  const spectrumList = [
    { value: '已知雷达', key: 0 },
    { value: '未知雷达', key: 1 }
  ]
  const tooltipCtx = ref('')
  const activeKey = ref(0)
  const visible = ref(false)
  const triggerRef = ref({
    getBoundingClientRect() {
      return position.value
    }
  })

  const position = ref({
    top: 0,
    left: 0,
    bottom: 0,
    right: 0
  })
  const currentList = computed(() => {
    return props.signalList.filter(item => {
      if (activeKey.value === 0) {
        return item.batchNumber <= 1000
      } else {
        return item.batchNumber > 1000
      }
    })
  })
  const transOperFreqValue = freqs => {
    return freqs.map(freq => round(freq / 1e6, 3)).join(',')
  }
  const transTime = time => {
    const [mm, dd, hh] = time.split(',')
    const year = new Date().getYear() + 1900
    return `${year}/${mm}/${dd} ${hh}:00`
  }
  const queryWftTypeLabel = value => {
    const item = workingFrequencyTypes.find(item => item.value === value)
    return item?.label || value
  }
  const queryRecurrenceTypeLabel = value => {
    const item = recurrenceTypes.find(item => item.value === value)
    return item?.label || value
  }
  const queryPulseWidthTypeLabel = value => {
    const item = pulseWidthTypes.find(item => item.value === value)
    return item?.label || value
  }
  const addRowToStore = row => {
    radar.centerFreq = row.frequencyCenterValue
    radar.bandwidth = row.bandwidth
    radar.startFre = row.frequencyCenterValue - row.bandwidth / 2
    radar.endFre = row.frequencyCenterValue + row.bandwidth / 2
  }
  const addPolarData = () => {
    const x1 = [],
      x2 = [],
      y = []
    props.signalList.forEach(item => {
      x1.push(item.direction)
      x2.push(item.directionCar)
      y.push(item.amplitudeCenterValue)
    })
    radar.directionAmplitudes.north = x1
    radar.directionAmplitudes.car = x2
    radar.directionAmplitudes.am = y
  }
  const analyse = row => {
    addRowToStore(row)
    addPolarData()
    router.push({
      name: 'ScanRadarAnalyse'
    })
  }
  const findDirection = row => {
    analyse(row)
    radar.commandType = 1
  }
  const onCellClick = (row, column, cell, e) => {
    const actionProps = ['operatingFrequencyValue', 'recurrenceValue', 'pulseWidthValue']
    if (actionProps.includes(column.property)) {
      visible.value = true
      tooltipCtx.value = cell.innerText
      position.value = DOMRect.fromRect({
        width: 0,
        height: 0,
        x: e.clientX,
        y: e.clientY
      })
    }
  }
</script>
