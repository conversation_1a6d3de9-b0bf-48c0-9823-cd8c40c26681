import request from '@/utils/request'
import useChartsStore from '@/store/modules/charts'

export const queryFileByte = data => {
  return request({
    url: '/signal/getFFTNew',
    method: 'post',
    data
  })
}

export const getSignalSort = data => {
  return request({
    url: '/signal/getSignalSort',
    method: 'post',
    data
  })
}

// 获取时序图数据
export const querySequence = data => {
  return request({
    url: '/file/fft',
    // params: query,
    method: 'post',
    data
  })
}

// 获取ccdf图数据
export const queryCCDF = query => {
  return request({
    url: '/file/ccdf',
    params: query,
    method: 'post'
  })
}

// 获取AM vs Time图数据
export const queryAms = data => {
  return request({
    url: '/signal/getAMS',
    method: 'post',
    data
  })
}

// 获取AM vs Time图数据
export const queryFms = data => {
  return request({
    url: '/signal/getMag',
    method: 'post',
    data
  })
}

// 获取IQ vs Time图数据
export const queryIqs = data => {
  return request({
    url: '/signal/getTime',
    method: 'post',
    data
  })
}

// 获取IQ Plot(FM vs Time)图数据
export const queryPlots = data => {
  return request({
    url: '/signal/getPlot',
    method: 'post',
    data
  })
}

// 获取星座图
export const queryConstellationPlots = data => {
  return request({
    url: '/signal/getModulation',
    method: 'post',
    data
  })
}

// 获取眼图数据
export const queryEye = data => {
  return request({
    url: '/signal/getEye',
    method: 'post',
    data
  })
}

// 获取EVM数据
export const queryEvm = data => {
  if (!data) {
    data = useChartsStore().getFileInfo()
  }
  return request({
    url: '/signal/getEVM',
    method: 'post',
    data
  })
}

// 获取信号文件分析总览图
export const queryFileFft = data => {
  return request({
    url: '/signal/getFFT',
    method: 'post',
    data
  })
}


export const batchFreqAnalysis = data => {
  return request({
    url: '/signal/getSpectrumAnalysis',
    method: 'post',
    data
  })
}


// 获取信号文件分析总览图
export const queryRadarFft = data => {
  return request({
    url: '/signal/getRadar',
    method: 'post',
    data
  })
}

//下变频
export const getDDC = data => {
  return request({
    url: '/signal/getDDC',
    method: 'post',
    data
  })
}
//滤波
export const getFilter = data => {
  return request({
    url: '/signal/getFirFilter',
    method: 'post',
    data
  })
}
//ITU参数测量
export const getITU = data => {
  return request({
    url: '/signal/getSignalITU',
    method: 'post',
    data
  })
}
//M次方谱
export const postMspectrum = data => {
  return request({
    url: '/signal/getM_spectrum',
    method: 'post',
    data
  })
}
//fsk波特率估计
export const postFSKEstimate = data => {
  return request({
    url: '/signal/getFSK',
    method: 'post',
    data
  })
}
//psk波特率估计
export const postPSKEstimate = data => {
  return request({
    url: '/signal/getPSK',
    method: 'post',
    data
  })
}
