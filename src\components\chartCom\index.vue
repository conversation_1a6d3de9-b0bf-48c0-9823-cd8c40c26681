<!-- 首页Chart组件 -->
<template>
  <div class="chart-title w-full">
    <div class="equipType" :style="{ '--dot-color': statusColor }">
      {{ deviceData.type === 0 ? '主机' : '子机' }}
    </div>
    <div>
      <span class="mx-2">IP :</span>
      <el-input v-model="IPs[0]" class="inputClass" />
      <span class="mx-2">-</span>
      <el-input v-model="IPs[1]" class="inputClass" />
      <span class="mx-2">-</span>
      <el-input v-model="IPs[2]" class="inputClass" />
      <span class="mx-2">-</span>
      <el-input v-model="IPs[3]" class="inputClass" />
    </div>
    <div>
      端口:
      <el-input v-model="deviceData.port" class="inputClass protClass" />
    </div>
    <div>
      <el-button class="saveBtn" @click="savePort"> 保存 </el-button>
    </div>
  </div>
  <ScanSection>
    <div class="scan-charts">
      <div
        class="flex h-[40px] items-center border-t-[1px] border-b-[1px] border-solid border-[#225459] gap-x-1"
      >
        <div class="pd6">
          <el-button @click="switchDeviceType">
            {{ deviceData.deviceScan?.type === 1 ? '离散' : '全景' }}
          </el-button>
        </div>
        <el-checkbox-group v-model="checkList" class="pd6">
          <el-checkbox
            v-for="item in lineTypes"
            :key="item.value"
            v-model="item.value"
            :label="item.label"
            border
          />
        </el-checkbox-group>
        <div class="pd6">
          <el-button @click="showResetZoom">全频段</el-button>
        </div>
        <div class="flex items-center justify-around w-[200px]">
          <div class="w-20 text-[#fff]">参考电平</div>
          <el-input v-model="model.refLevel" class="h-full" type="number">
            <template #append>
              <span class="pl-1 pr-1">dBm</span>
            </template>
          </el-input>
        </div>
      </div>
      <div
        class="flex h-[40px] items-center justify-between border-t-[1px] border-b-[1px] border-solid border-[#225459]"
      >
        <div class="flex gap-x-2">
          <el-select
            v-model="selectedFreStds"
            :teleported="false"
            multiple
            collapse-tags
            collapse-tags-tooltip
            effect="dark"
            value-key="label"
          >
            <el-option
              v-for="item in frequencyStandards"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </el-select>
          <el-checkbox v-model="isActive" label="激活" border />
          <el-checkbox v-model="isTopFollow" label="峰值跟踪" border />
        </div>
        <div class="bg-[#0d8686] h-[30px] px-2.5 ml-2.5 text-[#fff] leading-[30px]">
          经度：{{ deviceData.longitude }}；纬度：{{ deviceData.latitude }}。{{
            status ? '正在执行频段扫描' : '断连'
          }}
        </div>
      </div>
      <div class="main">
        <!-- 频谱扫描echarts -->
        <el-row :gutter="10">
          <el-col class="scanCharts" :span="20">
            <!-- 信号检测echarts -->
            <Spectrum
              ref="spectrumRef"
              :key="usekey"
              :usekey="usekey"
              :show-lines="checkList"
              :selected-fremarkers="selectedFreStds"
              :is-active="isActive"
              :is-top-follow="isTopFollow"
              :data-list="dataList"
              :model="model"
            />
          </el-col>
          <el-col class="rightBtn" :span="4">
            <el-button @click="handleScan(1)">扫描频段</el-button>
            <el-button @click="handleScan(2)">设备参数</el-button>
            <el-button class="ctl-btn" :class="{ close: status }" @click="start"> 启动 </el-button>
            <el-button class="ctl-btn" :class="{ close: !status }" @click="stop"> 停止 </el-button>
            <el-button class="ctl-btn" :class="{ close: status }" @click="fullScreen">
              全屏
            </el-button>
            <el-button class="ctl-btn" :class="{ close: status }" @click="signalDetection">
              信号监测
            </el-button>
          </el-col>
        </el-row>
      </div>
    </div>
  </ScanSection>

  <!-- 弹出层  -->
  <el-dialog
    v-model="dialogTableVisible"
    :title="dialogTitle"
    width="600"
    center
    class="dialog-class"
  >
    <div v-if="dialogType === 1">
      <el-row :gutter="20">
        <el-col :span="5">
          <el-button style="height: 40px; width: 100px" @click="switchDeviceType">
            {{ deviceData.deviceScan.type === 1 ? '离散' : '连续' }}
          </el-button>
        </el-col>
        <el-col :span="19">
          <!-- 连续 -->
          <div v-if="deviceData.deviceScan.type === 0">
            <el-form label-position="right" label-width="90px">
              <el-form-item label="中心频率">
                <el-input
                  v-model="deviceScan.centerFreq"
                  type="number"
                  @change="dataChangeFun('centerFreq')"
                >
                  <template #append>
                    <el-select
                      v-model="selectForm.centerFreq"
                      @change="dataChangeFun('centerFreq')"
                    >
                      <el-option v-for="unit in units" :key="unit" :value="unit" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="扫宽">
                <el-input
                  v-model="deviceScan.bandwidth"
                  type="number"
                  @change="dataChangeFun('bandwidth')"
                >
                  <template #append>
                    <el-select v-model="selectForm.bandwidth" @change="dataChangeFun('bandwidth')">
                      <el-option v-for="unit in units" :key="unit" :value="unit" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="起始频率">
                <el-input
                  v-model="deviceScan.startFreq"
                  type="number"
                  @change="dataChangeFun('startFreq')"
                >
                  <template #append>
                    <el-select v-model="selectForm.startFreq" @change="dataChangeFun('startFreq')">
                      <el-option v-for="unit in units" :key="unit" :value="unit" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="终止频率">
                <el-input
                  v-model="deviceScan.endFreq"
                  type="number"
                  @change="dataChangeFun('endFreq')"
                >
                  <template #append>
                    <el-select v-model="selectForm.endFreq" @change="dataChangeFun('endFreq')">
                      <el-option v-for="unit in units" :key="unit" :value="unit" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="步长">
                <el-select v-model="deviceScan.step" type="number">
                  <el-option
                    v-for="item in enumList.measure_mode._object.frequency_step"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="门限类型">
                <el-select v-model="deviceScan.thresholdType" class="w-full">
                  <el-option
                    v-for="item in enumList.threshold_type._object.threshold_type"
                    :key="Number(item.value)"
                    :label="item.label"
                    :value="Number(item.value)"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="门限">
                <el-input v-model="deviceScan.threshold" type="number">
                  <template #append>
                    <span class="pl-1 pr-1">dBm</span>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="信号组数">
                <el-input
                  v-model="deviceScan.freqSectionNum"
                  type="number"
                  @change="dataChangeFun('freqSectionNum')"
                >
                  <template #append>
                    <span class="pl-1 pr-1">个</span>
                  </template>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
          <!-- 离散 -->
          <div v-else>
            <el-form label-position="right" label-width="90px">
              <el-form-item label="频率">
                <el-input
                  v-model="deviceScan.centerFreq"
                  type="number"
                  @change="dataChangeFun('centerFreq')"
                >
                  <template #append>
                    <el-select
                      v-model="selectForm.centerFreq"
                      @change="dataChangeFun('centerFreq')"
                    >
                      <el-option v-for="unit in units" :key="unit" :value="unit" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="带宽">
                <el-input
                  v-model="deviceScan.bandwidth"
                  type="number"
                  @change="dataChangeFun('bandwidth')"
                >
                  <template #append>
                    <el-select v-model="selectForm.bandwidth" @change="dataChangeFun('bandwidth')">
                      <el-option v-for="unit in units" :key="unit" :value="unit" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-form>
            <el-row>
              <el-col :span="5">
                <el-button class="content-button" @click="addItem">添加</el-button>
                <span class="content-font">频率|带宽</span>
                <el-button class="content-button" @click="clear">清空</el-button>
                <el-button class="content-button" @click="save">保存</el-button>
              </el-col>
              <el-col :span="19">
                <textarea v-model="freList" class="content-textarea" readonly rows="7" />
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>
      <div style="margin-bottom: 30px">
        <div class="btnGroup">
          <el-button
            style="background-color: #fff; color: #000"
            @click="dialogTableVisible = false"
          >
            取消
          </el-button>
          <el-button style="background-color: #2389fd" @click="saveHandle"> 确定 </el-button>
        </div>
        <div class="btnGroup">
          <el-button style="background-color: #808000" @click="syncScanSettingsHandle">
            同步设置
          </el-button>
          <el-button style="background-color: #808000" @click="restoreDefaultScanHandle">
            恢复默认
          </el-button>
        </div>
      </div>
    </div>
    <div v-else>
      <el-form>
        <el-form-item label="测量模式">
          <el-select v-model="devicePara.measureMode" type="number">
            <el-option
              v-for="item in enumList.measure_mode._object.measure_mode"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="极化方式">
          <el-select v-model="devicePara.polarizationType">
            <el-option
              v-for="item in enumList.polarization_type._object.polarization_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="检波方式">
          <el-select v-model="devicePara.detectionMode" type="number">
            <el-option
              v-for="item in enumList.detection_mode._object.detection_mode"
              :key="Number(item.value)"
              :label="item.label"
              :value="Number(item.value)"
            />
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="检波速度">
          <el-input v-model="devicePara.detectionSpeed" type="number" />
        </el-form-item> -->
        <el-form-item label="衰减模式">
          <el-select
            v-model="devicePara.attenuationMode"
            type="number"
            @change="attenuationModeChange"
          >
            <el-option
              v-for="item in enumList.attenuation_mode._object.attenuation_mode"
              :key="Number(item.value)"
              :value="Number(item.value)"
              :label="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="衰减值">
          <el-input v-model="devicePara.attenuation" type="number">
            <template #append>
              <span class="pl-1 pr-1">dB</span>
            </template>
          </el-input>
        </el-form-item>
        <!-- <el-form-item label="增益模式">
          <el-select v-model="devicePara.gainMode" type="number">
            <el-option
              v-for="item in enumList.gain_mode._object.gain_mode"
              :key="Number(item.value)"
              :value="Number(item.value)"
              :label="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="增益值">
          <el-input v-model="devicePara.gain" type="number">
            <template #append>
              <span class="pl-1 pr-1">dbm</span>
            </template>
          </el-input>
        </el-form-item> -->
        <!-- <el-form-item label="驻留时间">
          <el-input v-model="devicePara.lingerTime" type="number">
            <template #append>
              <span class="pl-1 pr-1">ms</span>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="门限类型">
          <el-select v-model="devicePara.thresholdType">
            <el-option
              v-for="item in enumList.threshold_type._object.threshold_type"
              :key="Number(item.value)"
              :value="Number(item.value)"
              :label="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="门限">
          <el-input v-model="devicePara.threshold" type="number">
            <template #append>
              <span class="pl-1 pr-1">dBμV/m</span>
            </template>
          </el-input>
        </el-form-item> -->
      </el-form>
      <div style="margin-bottom: 30px">
        <div class="btnGroup">
          <el-button
            style="background-color: #fff; color: #000"
            @click="dialogTableVisible = false"
          >
            取消
          </el-button>
          <el-button style="background-color: #2389fd" @click="saveHandle"> 确定 </el-button>
        </div>
        <div class="btnGroup">
          <el-button style="background-color: #808000" @click="syncParaSettingsHandle">
            同步设置
          </el-button>
          <el-button style="background-color: #808000" @click="restoreDefaultParaHandle">
            恢复默认
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
  import { lineTypes } from '@/constant'
  import useScanStore from '@/store/modules/scanMonitor'
  import ScanSection from '@/components/SpectrumSignal/ScanSection.vue'
  import Spectrum from '@/components/SpectrumSignal/Spectrum.vue'
  import { numToPlot, plotToNum } from '@/utils/utils'
  import _ from 'lodash'
  import {
    saveOrUpdatePara,
    addOrUpdateEquip,
    saveOrUpdateScan,
    restoreDefaultPara,
    restoreDefaultScan,
    syncParaSettings,
    syncScanSettings,
    getEquipDetail
  } from '@/api/system/equipment'

  const emit = defineEmits(['start', 'stop', 'initEquip'])
  const router = useRouter()

  const props = defineProps({
    /** 图表数据 */
    dataList: {
      type: Object,
      default: () => ({})
    },
    /** 设备ID */
    deviceId: {
      type: Number,
      default: null
    },
    /**
     * 图表模式
     * 频谱扫描、信号监测、雷达分析、频谱分析
     */
    model: {
      type: Object,
      default: () => ({})
    },
    /** 使用的key */
    usekey: {
      type: String,
      default: 'default'
    },
    /** 枚举值列表 */
    enumList: {
      type: Object,
      default: () => ({})
    },
    /** 是否激活 */
    status: {
      type: Number,
      default: 0
    },
    /** 设备数据 */
    deviceData: {
      type: Object,
      default: () => ({})
    }
  })

  const { proxy } = getCurrentInstance()
  // 分割后的IP地址
  const IPs = ref({
    0: null,
    1: null,
    2: null,
    3: null
  })
  // 设备数据
  const deviceData = ref({})
  // 设备参数
  const devicePara = ref({})
  // 设备扫描频段
  const deviceScan = ref({})

  // 频标多选
  const frequencyStandards = [
    { label: '频标1', value: 1 },
    { label: '频标2', value: 2 },
    { label: '频标3', value: 3 },
    { label: '频标4', value: 4 },
    { label: '频标5', value: 5 }
  ]
  // 频率单位
  const units = ['GHz', 'MHz', 'KHz', 'Hz']
  // 图表类型复选框
  const checkList = ref(['实时', '平均', '最大', '最小', '门限'])
  //  单位下拉框对应的值
  const selectForm = ref({
    // 中心频率
    centerFreq: 'Hz',
    // 扫宽
    bandwidth: 'Hz',
    // 起始频率
    startFreq: 'Hz',
    // 终止频率
    endFreq: 'Hz'
    // 步长
    // step: 'Hz'
  })
  const state = ref(1)

  const statusColor = computed(() => {
    switch (props.status) {
      case 1:
        return '#8ac928'
      case 0:
        return '#f00'
      default:
        return '#ddd'
    }
  })
  //离散频率/带宽数组
  const freList = ref('')
  // 弹出层是否显示
  const dialogTableVisible = ref(false)
  /**
   * 弹出层类型
   * 1: 扫描频段
   * 2: 设备参数
   */
  const dialogType = ref(1)
  // 弹出层标题
  const dialogTitle = ref('扫描频段')
  // 图标实例
  const spectrumRef = ref()
  // 选中的频标数组
  const selectedFreStds = ref([])
  // 是否激活
  const isActive = ref(true)
  // 是否峰值跟踪
  const isTopFollow = ref(false)

  /**
   * 切换扫描频段类型
   * 0: 连续
   * 1: 离散
   */
  const switchDeviceType = () => {
    deviceData.value.deviceScan.type === 1
      ? (((deviceData.value.deviceScan.type = 0), (props.model.type = 0)), dataChangeFun())
      : ((deviceData.value.deviceScan.type = 1), (props.model.type = 1))
  }
  // 扫描频段 弹出层打开
  const handleScan = type => {
    // 恢复原有数据
    deviceScan.value = JSON.parse(JSON.stringify(deviceData.value.deviceScan))
    devicePara.value = JSON.parse(JSON.stringify(deviceData.value.devicePara))
    deviceScan.value.step = deviceScan.value.step.toString()
    if (deviceData.value.deviceScan.type === 1) {
      freList.value =
        (deviceScan.value?.freqBandStr ?? '')
          .split(',')
          .filter(item => item) // 过滤掉空字符串
          .map(item => {
            const [centerFreq, bandwidth] = item.split('|')
            const formattedCenterFreq = centerFreq ? numToPlot(centerFreq) : ''
            const formattedBandwidth = bandwidth ? numToPlot(bandwidth) : ''
            return `${formattedCenterFreq}|${formattedBandwidth}`
          })
          .join('\n') + '\n'
    } else {
      freList.value = ''
    }
    // 三目运算符判断弹出层标题
    type === 1
      ? ((dialogTitle.value = '扫描频段'), dataChangeFun())
      : (dialogTitle.value = '设备参数')
    dialogType.value = type
    dialogTableVisible.value = true
  }
  // 全频段展示
  const showResetZoom = () => {
    spectrumRef.value.instance.zoomOut()
  }
  // 扫描频段数据变更
  /**
   * 数据变化函数
   *
   * @param key 数据项key
   * @returns 无返回值
   */
  const dataChangeFun = key => {
    if (key) {
      console.log(deviceScan.value[key])
      deviceScan.value[key] = plotToNum(deviceScan.value[key] + selectForm.value[key])
      formatValue(key, deviceScan.value[key])
      linkage(key)
      return
    } else {
      // 遍历扫描频段支持动态单位的数据，进行单位转换
      const tarObject = {
        centerFreq: deviceData.value.deviceScan.centerFreq,
        bandwidth: deviceData.value.deviceScan.bandwidth,
        startFreq: deviceData.value.deviceScan.startFreq,
        endFreq: deviceData.value.deviceScan.endFreq
      }
      Object.keys(tarObject).forEach(key => {
        formatValue(key, tarObject[key])
      })
    }
  }
  /**
   * 计算并设置设备扫描的频率参数
   * @param key 频率参数类型，可选值为'centerFreq'、'bandwidth'、'startFreq'、'endFreq'
   * @returns 无返回值，直接修改deviceScan.value中的相关属性
   */
  const linkage = key => {
    const startFreq = plotToNum(deviceScan.value.startFreq + selectForm.value.startFreq)
    const endFreq = plotToNum(deviceScan.value.endFreq + selectForm.value.endFreq)
    const bandwidth = plotToNum(deviceScan.value.bandwidth + selectForm.value.bandwidth)
    const centerFreq = plotToNum(deviceScan.value.centerFreq + selectForm.value.centerFreq)
    if (key === 'centerFreq' || key === 'bandwidth') {
      deviceScan.value.startFreq = centerFreq * 1 - (bandwidth / 2) * 1
      if (deviceScan.value.startFreq < 20000000) {
        deviceScan.value.startFreq = 20000000
        deviceScan.value.bandwidth =
          2 * (deviceScan.value.centerFreq * 1 - deviceScan.value.startFreq)
      }
      deviceScan.value.endFreq =
        centerFreq * 1 + (bandwidth / 2) * 1 < 0 ? 0 : centerFreq * 1 + (bandwidth / 2) * 1
      formatValue('startFreq', deviceScan.value.startFreq)
      formatValue('endFreq', deviceScan.value.endFreq)
    }
    if (key === 'startFreq' || key === 'endFreq') {
      deviceScan.value.bandwidth = endFreq * 1 - startFreq * 1 < 0 ? 0 : endFreq * 1 - startFreq * 1
      deviceScan.value.centerFreq =
        (startFreq * 1 + endFreq * 1) / 2 < 0 ? 0 : (startFreq * 1 + endFreq * 1) / 2
      formatValue('centerFreq', deviceScan.value.centerFreq)
      formatValue('bandwidth', deviceScan.value.bandwidth)
    }
  }
  // 单位变更
  const formatValue = (key, value) => {
    const newFormVal = numToPlot(value)
    if (newFormVal.search('KHz') != -1) {
      deviceScan.value[key] = newFormVal.replace('KHz', '')
      selectForm.value[key] = 'KHz'
    } else if (newFormVal.search('MHz') != -1) {
      deviceScan.value[key] = newFormVal.replace('MHz', '')
      selectForm.value[key] = 'MHz'
    } else if (newFormVal.search('GHz') != -1) {
      deviceScan.value[key] = newFormVal.replace('GHz', '')
      selectForm.value[key] = 'GHz'
    }
  }

  // 新增离散频率/带宽
  const addItem = () => {
    const centerFreq = deviceScan.value.centerFreq + selectForm.value.centerFreq
    const bandwidth = deviceScan.value.bandwidth + selectForm.value.bandwidth
    const itemStr = `${centerFreq}|${bandwidth}\n`
    if (freList.value.includes(itemStr)) {
      return
    }
    freList.value += itemStr
  }
  // 清空离散频率/带宽
  const clear = () => {
    freList.value = ''
  }
  // 保存离散频率/带宽
  const save = () => {
    const items = []
    freList.value.split('\n').forEach(item => {
      if (!item) {
        return
      }
      const [centerFreq, bandwidth] = item.split('|')
      items.push({ centerFreq: plotToNum(centerFreq), bandwidth: plotToNum(bandwidth) })
    })
    props.model.discrete = items
  }
  /**
   * 修改端口、IP
   */
  const savePort = () => {
    const submitData = {
      id: deviceData.value.id,
      ip: `${IPs.value[0]}.${IPs.value[1]}.${IPs.value[2]}.${IPs.value[3]}`,
      port: deviceData.value.port
    }
    addOrUpdateEquip(submitData).then(response => {
      proxy.$modal.msgSuccess('修改成功')
      initData()
    })
  }
  /**
   * 保存扫描频段/设备参数
   * type: 1 扫描频段 2 设备参数
   * 1: 扫描频段 saveScan
   * 2: 设备参数 savePara
   */
  const saveHandle = () => {
    if (dialogType.value === 1) {
      console.log(saveScan())
      saveOrUpdateScan(saveScan()).then(response => {
        proxy.$modal.msgSuccess('修改成功')
        initData()
      })
    } else {
      saveOrUpdatePara(savePara()).then(response => {
        proxy.$modal.msgSuccess('修改成功')
        initData()
      })
    }
    dialogTableVisible.value = false
  }
  /**
   * 保存扫描频段
   * deviceScan.type
   * 0: 连续
   * 1: 离散
   */
  const saveScan = () => {
    if (deviceScan.value.type === 0) {
      const tarObject = {
        centerFreq: deviceScan.value.centerFreq,
        bandwidth: deviceScan.value.bandwidth,
        startFreq: deviceScan.value.startFreq,
        endFreq: deviceScan.value.endFreq
        // step: deviceScan.value.step
      }
      Object.keys(tarObject).forEach(key => {
        tarObject[key] = plotToNum(tarObject[key] + selectForm.value[key])
      })
      const submitData = {
        deviceId: deviceData.value.id,
        id: deviceScan.value.id,
        type: deviceScan.value.type,
        freqSectionNum: deviceScan.value.freqSectionNum,
        thresholdType: deviceScan.value.thresholdType,
        threshold: deviceScan.value.threshold,
        step: deviceScan.value.step,
        ...tarObject
      }
      return submitData
    } else {
      const freStrArray = _.compact(freList.value.split('\n')).map(item => {
        const [centerFreq, bandwidth] = item.split('|')
        return `${plotToNum(centerFreq)}|${plotToNum(bandwidth)}`
      })
      const freStr = freStrArray.join(',')
      const submitData = {
        deviceId: deviceData.value.id,
        id: deviceScan.value.id,
        type: deviceScan.value.type,
        freqBandStr: freStr
      }
      return submitData
    }
  }
  // 保存设备参数
  const savePara = () => {
    const submitData = {
      deviceId: deviceData.value.id,
      id: devicePara.value.id,
      measureMode: devicePara.value.measureMode,
      polarizationType: devicePara.value.polarizationType,
      detectionMode: devicePara.value.detectionMode,
      detectionSpeed: devicePara.value.detectionSpeed,
      attenuationMode: devicePara.value.attenuationMode,
      attenuation: devicePara.value.attenuation,
      gainMode: devicePara.value.gainMode,
      gain: devicePara.value.gain,
      lingerTime: devicePara.value.lingerTime,
      thresholdType: devicePara.value.thresholdType,
      threshold: devicePara.value.threshold
    }
    return submitData
  }
  // 同步扫描频段设置
  const syncScanSettingsHandle = () => {
    syncScanSettings(deviceData.value.id).then(response => {
      proxy.$modal.msgSuccess('同步成功')
      dialogTableVisible.value = false
      emit('initEquip')
    })
  }
  // 恢复默认扫描频段
  const restoreDefaultScanHandle = () => {
    restoreDefaultScan(deviceData.value.id).then(response => {
      proxy.$modal.msgSuccess('恢复成功')
      dialogTableVisible.value = false
      emit('initEquip')
    })
  }
  // 同步设备参数设置
  const syncParaSettingsHandle = () => {
    syncParaSettings(deviceData.value.id).then(response => {
      proxy.$modal.msgSuccess('同步成功')
      dialogTableVisible.value = false
      emit('initEquip')
    })
  }
  // 恢复默认设备参数
  const restoreDefaultParaHandle = () => {
    restoreDefaultPara(deviceData.value.id).then(response => {
      proxy.$modal.msgSuccess('恢复成功')
      dialogTableVisible.value = false
      emit('initEquip')
    })
  }

  const start = () => {
    emit('start', { deviceData: deviceData.value, spectrum: props.model, key: props.usekey })
  }
  const stop = () => {
    emit('stop', props.model)
  }
  // 携带参数跳转扫描页面
  const fullScreen = () => {
    router.push({
      path: './scan',
      query: {
        data: JSON.stringify(deviceData.value)
      }
    })
    console.log(deviceData.value)
    stop()
  }
  // 携带参数跳转信号监测页面
  const signalDetection = () => {
    router.push({
      path: './intermediate/routineAnalysis',
      query: {
        data: JSON.stringify(deviceData.value)
      }
    })
    stop()
  }
  // 初始化数据
  const initData = () => {
    getEquipDetail(props.deviceId).then(response => {
      deviceData.value = response.data
      IPs.value = deviceData.value.ip.split('.').reduce((acc, cur, index) => {
        acc[index] = parseInt(cur)
        return acc
      }, {})
    })
  }

  const attenuationModeChange = info => {
    if (info === 0) {
      devicePara.value.attenuation = 0
    }
  }

  onMounted(() => {
    // if (props.deviceId) {
    //   initData()
    // }
    deviceData.value = props.deviceData
    IPs.value = props.deviceData.ip.split('.').reduce((acc, cur, index) => {
      acc[index] = parseInt(cur)
      return acc
    }, {})
  })
</script>

<style scoped lang="scss">
  .chart-title {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: space-around;
    color: #fff;
    height: 40px;
    line-height: 40px;
    .equipType {
      width: 120px;
      text-align: center;
      font-size: 20px;
      font-weight: 700;
      position: relative;
      &::after {
        content: '';
        display: inline-block;
        position: absolute;
        right: 20%; /* 调整这个值来改变圆点的水平位置 */
        top: 50%; /* 调整这个值来改变圆点的垂直位置 */
        transform: translate(50%, -50%); /* 使圆点居中 */
        width: 25px;
        height: 25px;
        margin-left: 5px;
        border-radius: 50%;
        background-color: var(--dot-color); /* 使用 CSS 变量来设置颜色 */
      }
    }
    .inputClass {
      width: 50px;
      height: 25px;
      --el-input-focus-border-color: var(--main-border-color);
      --el-input-border-color: var(--main-border-color);
      --el-select-input-focus-border-color: var(--main-border-color);
      --el-input-hover-border-color: var(--main-border-color);
    }
    .protClass {
      width: 80px;
    }
    .saveBtn {
      width: 60px;
      height: 25px;
      background: linear-gradient(142.835deg, rgba(2, 167, 240, 1) 45%, rgba(1, 84, 120, 1) 69%);
      border: none;
      border-radius: 5px;
      box-shadow: 3px 3px 5px rgba(0, 255, 255, 0.349);
      font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑';
      font-weight: 700;
      font-style: normal;
      font-size: 14px;
    }
  }
  .scan-charts {
    padding: 12px 8px;
    .main {
      height: 100%;
      .rightBtn {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        .el-button {
          width: 100%;
          height: 50px;
          margin-bottom: 10px;
          &:nth-child(2n) {
            margin-bottom: 35px;
          }
        }
      }
    }

    .el-button + .el-button {
      margin-left: 0 !important;
    }
    .pd6 {
      padding: 6px;
    }

    .br1 {
      border-right: 1px solid var(--toolbar-color);
    }

    .bl1 {
      border-left: 1px solid var(--toolbar-color);
    }
  }

  .content-button {
    width: 85%;
    height: 30px;
    margin: 11px 2px;
  }

  .content-font {
    color: var(--scan-text-color);
    text-align: center;
  }

  .content-textarea {
    width: 79%;
    margin: 13px 15px;
    background: #01171e;
    border: 1px solid #00bfbf;
    color: var(--scan-text-color);
    padding: 0.5rem 1rem;
    resize: none;
  }

  .btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    .el-button {
      width: 100px;
      height: 30px;
      margin: 0 10px;
      border-radius: 8px;
      border: none;
    }
  }

  :deep(.el-input-group__append) {
    padding: 0;
  }
</style>

<style lang="scss">
  .dialog-class {
    background: #212121;
    .el-dialog__header {
      background-color: #01bfbf;
      margin-right: 0;
      padding: 10px;
      .el-dialog__title {
        color: #fff;
        font-weight: 600;
      }
      .el-dialog__headerbtn {
        top: 0px;
      }
      .el-dialog__headerbtn .el-dialog__close {
        font-size: 24px;
        color: #fff;
      }
    }
    .el-dialog__body {
      margin-top: 10px;
    }
    .el-select {
      width: 100%;
    }
  }
</style>
