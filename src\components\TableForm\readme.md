# 自定义搜索表格

> 由自定义表单+ 扩展表格+分页组成

## props

|参数|含义|类型|必填|默认值|可选值|
|-|-|-|-|-|-|
|title|页头|string|false|''|''|
|searhConfig|搜索表单配置项，详情参照表单组件说明|Object|false|-|-|
|hidePage|是否显示分页|Boolean|true|false|true/false|
|actions|表格上方按钮组|Array|false|-|-|
|-|-|-|-|-|-|
|-|-|-|-|-|-|
|-|-|-|-|-|-|

## tableConfig

|参数|含义|类型|必填|默认值|可选值|
|-|-|-|-|-|-|
|isChecked|是否可选择|||
|cols|表格item|Array|false|null|-|
|-|-|-|-|-|-|
|-|-|-|-|-|-|

### tableConfig.cols

|参数|含义|类型|必填|默认值|可选值|
|-|-|-|-|-|-|
|label|表头label|||||||
|minWidth|单元格最小宽度|string|false|-|-|
|width|单元格宽度|string|false|150||
|type|渲染内容类型|string|false|||
|renderFun|自定义渲染内容|fun|false||
|prop|属性名|||
|actions|按钮组||

### type

> 单元格内容渲染类型

text:渲染el-link可点击连接，需要传入对应click方法
warning:渲染预警表格tag，可传入对应warningMsg显示对应预警内容
dict:渲染字段组件，传入字典组件相关参数，（字典名，渲染类型等）

### renderFun

> 自定义渲染表格内容方法

```js
//h,h渲染函数，scope单元格内容
renderFun:(h,scope)=>{
  return h('div',scope.row.name)
}
```

```
