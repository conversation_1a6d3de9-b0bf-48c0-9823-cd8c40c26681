<template>
  <el-tabs class="mt-6">
    <el-tab-pane>
      <template #label>
        <el-button class="tabs-button"> 信号 </el-button>
      </template>
      <DirectionFinding v-if="spectrum.commandType" :table-data="tableData" />
      <AnalyseTable v-else :table-data="tableData" @pick="pick" @position="position" />
    </el-tab-pane>
    <el-tab-pane>
      <template #label>
        <el-button class="tabs-button"> IQ采集 </el-button>
      </template>
      <Gather :device-info="deviceInfo" />
    </el-tab-pane>
    <el-tab-pane lazy>
      <template #label>
        <el-button class="tabs-button"> 回放 </el-button>
      </template>
      <Playback />
    </el-tab-pane>
    <el-tab-pane>
      <template #label>
        <el-button class="tabs-button"> 告警 </el-button>
      </template>
      <warning :table-data="warmList" />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup name="BottomSignalTabs">
  import Gather from './Gather.vue'
  import Playback from './Playback.vue'
  import DirectionFinding from '@/components/DirectionFinding'
  import AnalyseTable from './AnalyseTable.vue'
  import warning from './warning.vue'
  import useScanStore from '@/store/modules/scanMonitor'
  import { numToPlot } from '@/utils/utils'
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const emit = defineEmits(['stop'])

  const props = defineProps({
    signalList: {
      type: Array,
      default: () => []
    },
    deviceInfo: {
      type: Object,
      default: () => {}
    },
    warmList: {
      type: Object,
      default: () => []
    }
  })
  const activeKey = ref(0)
  const scanStore = useScanStore()
  const spectrum = scanStore.spectrumCopy
  const routineAnalysis = scanStore.routineAnalysis
  const spectrumList = computed(() => {
    const signalList = props.signalList
    const len = signalList.length
    signalList.sort((a, b) => a.centerFreIn - b.centerFreIn)
    const itemNum = Math.ceil(signalList.length / spectrum.freqSectionNum)
    const list = []
    let temp = []
    for (let i = 0; i < len; i++) {
      temp.push(signalList[i])
      if (temp.length === itemNum || i === len - 1) {
        const start = temp[0]
        const end = temp[temp.length - 1]
        const title = generateTabTitle(start, end)
        list.push({ title, body: temp })
        temp = []
      }
    }
    return list
  })
  const tableData = computed(() => {
    return spectrumList.value[activeKey.value]?.body || []
  })
  const generateTabTitle = (start, end) => {
    const diff = end.signalCenterFrequency - start.signalCenterFrequency
    return `${numToPlot(start.signalCenterFrequency, '', 0)} - ${numToPlot(
      end.signalCenterFrequency,
      '',
      0
    )}(${numToPlot(diff, '', 1)})`
  }
  const tabClick = index => {
    activeKey.value = index
  }
  //Add by xf at 2023-12-05
  const pick = data => {
    // routineAnalysis.centerFreq = row.signalCenterFrequency
    // routineAnalysis.bandwidth = row.signalBandwidth
    // routineAnalysis.startFreq = row.signalCenterFrequency - row.signalCenterFrequency / 2
    // routineAnalysis.endFreq = row.signalCenterFrequency + row.signalBandwidth / 2
    // console.log(spectrum, '跳转')
    // const queryData = { ...spectrum }
    // queryData.centerFreq = row.signalCenterFrequency
    // queryData.bandwidth = row.signalBandwidth
    // queryData.startFreq = row.signalCenterFrequency - row.signalCenterFrequency / 2
    // queryData.endFreq = row.signalCenterFrequency + row.signalBandwidth / 2
    // queryData.code = props.deviceInfo.value
    // console.log(queryData, '跳转')
    // emit('stop')
    // router.push({
    //   path: './intermediate/routineAnalysis',
    //   query: {
    //     data: JSON.stringify(queryData)
    //   }
    // })
    const queryData = { ...spectrum }
    queryData.centerFreq = data.signalCenterFrequency
    queryData.code = props.deviceInfo.value
    router.push({
      name: 'ScanSignalAnalyse',
      query: {
        data: JSON.stringify(queryData)
      }
    })
  }

  /**
   * 根据传入的行数据，跳转到信号定位TDOA页面
   * @param row 行数据
   */
  const position = data => {
    emit('stop')
    router.push({
      path: '/signalPosition/TDOA',
      query: {
        centerFreq: data.signalCenterFrequency,
        bandwidth: 40e6,
        singleSamplingTime: 1000,
        signalList: JSON.stringify(data)
      }
    })
  }
</script>

<style lang="scss" scoped>
  .scrolling-text {
    overflow: hidden;
    position: relative;
    width: 580px;
    height: 30px; // 定义一个固定高度
    line-height: 30px; // 保持文本垂直居中
    background: #fff;
    color: #c72a29;
    border: 1px solid #56bcbe;
    border-radius: 4px;
    text-align: center;
  }

  .scrolling-text div {
    position: absolute;
    width: 100%;
    height: 100%;
    text-align: center; // 文本居中显示
    animation: scrollUpDown 6s linear infinite;
  }

  @keyframes scrollUpDown {
    0%,
    100% {
      transform: translateY(100%); // 初始和结束位置在容器下方，使得内容不可见
    }
    25%,
    75% {
      transform: translateY(0); // 中间状态，内容完全可见
    }
  }
</style>
