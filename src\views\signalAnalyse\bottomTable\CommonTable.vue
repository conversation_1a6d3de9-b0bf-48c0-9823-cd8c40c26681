<script setup>
  import { round } from 'lodash'

  const props = defineProps({
    modulationList: {
      type: Array,
      default: () => []
    }
  })
</script>

<template>
  <el-table id="commonTable" border :data="modulationList">
    <el-table-column prop="modulation" label="调制方式" align="center">
      <template #default="{ row }">
        <span>{{ row.modulationNm }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="rate" label="出现概率" align="center">
      <template #default="{ row }">
        <span>{{ round(row.rate * 100, 2) + '%' }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="num" label="统计次数" align="center" />
  </el-table>
</template>

<style scss scoped>
  #commonTable {
    height: 100%;
  }
  :deep(.el-scrollbar__view) {
    height: 100%;
  }
</style>
