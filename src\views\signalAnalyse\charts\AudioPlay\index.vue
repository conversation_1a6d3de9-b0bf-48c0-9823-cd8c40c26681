<template>
  <div class="w-full">
    <button @click="initializeAudio">播放音频</button>
    <audio class="w-full" ref="audioPlayer" controls></audio>
  </div>
</template>

<script setup>
  import { ref, watch, onMounted } from 'vue'

  const props = defineProps({
    audioData: {
      type: Array,
      default: () => []
    }
  })

  const audioPlayer = ref(null)
  let audioContext = null

  // 初始化音频上下文和播放音频
  const initializeAudio = () => {
    if (!audioContext) {
      audioContext = new (window.AudioContext || window.webkitAudioContext)()
    }
    if (props.audioData.length > 0) {
      playAudio(props.audioData)
    }
  }

  // 播放音频
  const playAudio = audioData => {
    // 将Int16Array转换为采样率为32kHz的WAV文件
    const wavBlob = createWAVBlob(audioData, 32000)
    const url = URL.createObjectURL(wavBlob)
    audioPlayer.value.src = url
    audioPlayer.value.play()
  }

  // 创建WAV文件
  function createWAVBlob(samples, sampleRate) {
    const buffer = new ArrayBuffer(44 + samples.length * 2)
    const view = new DataView(buffer)

    // 写入WAV文件头
    writeString(view, 0, 'RIFF') // ChunkID
    view.setUint32(4, 36 + samples.length * 2, true) // ChunkSize
    writeString(view, 8, 'WAVE') // Format
    writeString(view, 12, 'fmt ') // Subchunk1ID
    view.setUint32(16, 16, true) // Subchunk1Size
    view.setUint16(20, 1, true) // AudioFormat (1表示PCM)
    view.setUint16(22, 1, true) // NumChannels (单声道)
    view.setUint32(24, sampleRate, true) // SampleRate (采样率)
    view.setUint32(28, sampleRate * 2, true) // ByteRate (采样率 * 通道数 * 每个样本的字节数)
    view.setUint16(32, 2, true) // BlockAlign (通道数 * 每个样本的字节数)
    view.setUint16(34, 16, true) // BitsPerSample (每个样本16位)
    writeString(view, 36, 'data') // Subchunk2ID
    view.setUint32(40, samples.length * 2, true) // Subchunk2Size

    // 写入样本数据
    for (let i = 0; i < samples.length; i++) {
      view.setInt16(44 + i * 2, samples[i], true)
    }

    return new Blob([view], { type: 'audio/wav' })
  }

  // 写入字符串到DataView
  function writeString(view, offset, string) {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i))
    }
  }

  // 监听audioData属性的变化
  watch(
    () => props.audioData,
    newVal => {
      if (newVal.length > 0 && audioContext) {
        playAudio(newVal)
      }
    },
    { immediate: true }
  )
</script>

<style scoped>
  /* 添加你的样式 */
</style>
