<template>
  <el-dialog
    :key="dia"
    v-model="dia"
    size="800px"
    append-to-body
    :title="id ? '修改实验进度' : '新增实验进度'"
  >
    <c-form
      v-bind="formConfig"
      :key="form.objId + '' + keys"
      ref="refsForm"
      v-model="form"
      class="mr-4"
    >
      <template #expInfo="{ formdata }">
        {{ formdata.expName }}
      </template>
    </c-form>
    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button type="primary" @click="sub">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
  let queryExpUrl = 'business/experiment/list?pageNum=1&pageSize=100'
  let queryExpParams = '&expAduitStatus=1&expStatus=0,1'
  import { getProcess, updateProcess, addProcess } from '@/api/business/process'
  export default {
    name: 'ProcessDetail',
    props: ['maxPercent'],
    emits: ['actSuccess'],
    data() {
      return {
        dia: false,
        loading: false,
        id: '',
        keys: Date.now(),
        form: {},
        expProcess: 0,
        formConfig: {
          splice: 2,
          labelWidth: '7em',
          layout: 'block',
          autoEmit: false,
          hideBtn: true,
          formInfo: [
            {
              title: '实验项目',
              name: 'expId',
              type: 'cuselect',
              queryInfo: queryExpUrl + queryExpParams,
              replaceFields: { label: 'expName', value: 'expId' },
              rules: { required: true, message: '请选择所属实验项目' },
              callback: ({ obj }) => {
                this.expProcess = obj.expProcess * 1 || 0
                // this.formConfig.formInfo[1].min = expProcess
                this.$refs.refsForm.setFormParams({ expProcess: this.expProcess })
                // this.$refs.refsForm.setFormItem({ name: expProcess, data: { min: expProcess } })
              }
            },
            {
              title: '实验进度',
              name: 'expProcess',
              type: 'slider',
              max: 100,
              min: 0,
              step: 5,
              callback: res => {
                if (res < this.expProcess) {
                  this.$modal.msgWarning('项目已完成' + this.expProcess + '%，无法选择更低进度')
                  this.$refs.refsForm.setFormParams({ expProcess: this.expProcess })
                }
              },
              rules: { required: true, message: '请输入进度名称' }
            },
            {
              title: '阶段成果',
              isFull: true,
              name: 'expResult',
              type: 'textarea',
            },
            {
              title: '成果文件',
              isFull: true,
              name: 'srFileList',
              type: 'file-upload',
              limit: 100,
              fileType: [],
              fileSize: 200
            }
          ]
        }
      }
    },
    methods: {
      show(id, expId = '', expProcess = 0) {
        this.id = id
        this.dia = true
        this.form = {
          srFileList: [],
          expId,
          expProcess,
          expResult: ''
        }
        this.formConfig.formInfo[1].readonly = false
        this.formConfig.formInfo[0].queryInfo = queryExpUrl + queryExpParams
        this.formConfig.formInfo[0].readonly = false
        setTimeout(() => {
          // this.$refs.refsForm.setFormParams({ expId })
        }, 2000)
        this.$nextTick(() => {})
        id && this.getInfo(id)
      },
      getInfo(id) {
        this.loading = true
        getProcess(id)
          .then(res => {
            res.data.expProcess *= 1
            this.formConfig.formInfo[0].queryInfo = queryExpUrl
            this.formConfig.formInfo[0].readonly = false
            this.formConfig.formInfo[1].readonly = false
            this.form = res.data
          })
          .finally(() => {
            this.loading = false
          })
      },
      sub() {
        this.$refs.refsForm.subForm(form => {
          let act = this.form.objId ? updateProcess : addProcess
          act(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
        })
      }
    }
  }
</script>
<style></style>
