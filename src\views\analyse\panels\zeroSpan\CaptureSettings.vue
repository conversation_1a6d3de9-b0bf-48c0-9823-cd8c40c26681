<template>
  <data-form :fields="fields" :model="model" />
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData'
  import { rateUnits, levelUnits, timeUnits } from '@/constant/units'

  const fields = ref([
    {
      target: 'refLevel',
      name: '参考电平',
      options: levelUnits,
      appendSelect: true
    },
    {
      target: 'centerFreqIn',
      name: '中心频率',
      options: rateUnits,
      appendSelect: true
    },
    { target: 'step', name: '步进', options: rateUnits, appendSelect: true },
    {
      target: 'samplingRate',
      name: '采样率',
      appendText: 'MS/s'
    },
    {
      target: 'intermediateFrequencyBandwidth',
      name: '中频带宽',
      options: rateUnits,
      appendSelect: true
    },
    { target: 'autoIfBw', name: '自动中频带宽', isCheckbox: true },
    {
      target: 'swpTime',
      name: '扫描时间',
      options: timeUnits,
      appendSelect: true
    }
  ])

  const model = reactive(new ModelData(fields.value, 'zp'))
</script>
