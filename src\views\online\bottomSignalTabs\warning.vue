<template>
  <el-table class="ml-2 max-h-[270px] h-[270px]" border :height="270" :data="tableData">
    <el-table-column label="序号" type="index" width="60" align="center" />
    <el-table-column label="信号频率" prop="freqHz" width="200" align="center">
      <template #default="{ row }">
        {{ numToPlot(row.freqHz) }}
      </template>
    </el-table-column>
    <el-table-column label="带宽（KHz）" prop="bwHz" align="center">
      <template #default="{ row }">
        {{ row.bwHz / 1000 }}
      </template>
    </el-table-column>
    <el-table-column label="信号幅度(dBm)" prop="level" align="center" />
    <el-table-column label="告警类型" prop="type" align="center">
      <template #default="{ row }">
        {{ getAlarmType(row.type) }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
  import { numToPlot } from '@/utils/utils'

  const props = defineProps({
    tableData: {
      type: Array,
      default: () => []
    }
  })

  const getAlarmType = type => {
    switch (type) {
      case 0:
        return '黑名单'
      case 2:
        return '功率告警'
      case 3:
        return '模板告警'
      default:
        return '未知类型'
    }
  }
</script>
