<template>
  <chart-header title="解调比特流" />
  <div class="modulation-bits">
    <div class="chart-bar">
      <div class="bar-left">符号表</div>
      <el-select v-model="binary" size="small" @change="transferBinary">
        <el-option
          v-for="(v, i) in options"
          :key="i"
          :value="v.value"
          :label="v.label"
        />
      </el-select>
    </div>
    <div class="symbol-table">
      <el-row v-for="(item, index) in tableData" :key="item.num" :gutter="20">
        <el-col :span="2">{{ index * 16 }}</el-col>
        <el-col :span="5">{{ item.slice(0, 8) }}</el-col>
        <el-col :span="5">{{ item.slice(8) }}</el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import Interval from '@/common/classes/interval'
  import useChartsStore from '@/store/modules/charts'
  import ChartHeader from '@/views/analyse/header/ChartHeader'
  import useDmFormStore from '@/store/modules/form/dmForm'
  import { BINARY2_16 } from '@/constant'

  defineComponent([ChartHeader])
  const chartsStore = useChartsStore()
  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    },
    params: {
      type: Object,
      default: () => ({})
    }
  })
  const interval = ref(null)
  const tableData = ref([])
  const options = [
    { label: '二进制', value: 2 },
    { label: '十六进制', value: 16 }
  ]
  const binary = ref(2)
  const transferBinary = (val) => {
    if (val === 16) {
      generateBinary16Data()
    } else {
      generateBinary2Data()
    }
  }
  const generateBinary2Data = () => {
    let temp = ''
    const origin = tableData.value
    tableData.value = []
    origin.forEach(item => {
      temp += num16To2(item)
    })
    while(temp.length > 0) {
      const item = temp.slice(0, 16)
      tableData.value.push(item)
      temp = temp.slice(16)
    }
  }
  const generateBinary16Data = () => {
    let temp = ''
    const origin = tableData.value
    tableData.value = []
    origin.forEach(item => {
      temp += num2To16(item)
      if (temp.length >= 16) {
        tableData.value.push(temp)
        temp = ''
      }
    })
    if (temp.length > 0) {
      tableData.value.push(temp)
    }
  }
  // 16进制转2进制
  const num16To2 = (str) => {
    let temp = ''
    for (let i = 0; i < str.length; i++) {
      temp += BINARY2_16[str[i]]
    }
    return temp
  }
  // 2进制转16进制
  const num2To16 = (str) => {
    let temp = '', i = 0
    for (i; i < str.length; i += 4) {
      const param = str[i] + str[i + 1] + str[i + 2] + str[i + 3]
      Object.keys(BINARY2_16).forEach(key => {
        if (param === BINARY2_16[key]) {
          temp += key
        }
      })
    }
    if (i < str.length) {
      let s = str.slice(i, str.length)
      while(s.length < 4) {
        s = '0' + s
      }
      Object.keys(BINARY2_16).forEach(key => {
        if (s === BINARY2_16[key]) {
          temp += key
        }
      })
    }
    return temp
  }
  const updateSymbolTable = (start = 0) => {
    tableData.value = []
    const bitData = props.data.slice(start, start + viewNum.value)
    for (let i = 0; i < bitData.length; i += 16) {
      tableData.value.push(bitData.slice(i, i + 16))
    }
    if (binary.value === 16) {
      generateBinary16Data(16)
    }
  }

  const viewNum = computed(() => {
    let num = 1
    let { modulation, resultLen } = useDmFormStore()
    if (chartsStore.static.enabled) {
      resultLen = chartsStore.static.dmLimit
    }
    while (modulation > 2) {
      num++
      modulation = modulation / 2
    }
    return num * resultLen
  })

  onMounted(() => {
    updateSymbolTable()
    interval.value = chartsStore.static.enabled ? null : Interval.create(
      updateSymbolTable,
      viewNum.value,
      viewNum.value,
      props.data.length
    )
  })

  onUnmounted(() => {
    Interval.remove(interval.value)
  })
</script>

<style scoped lang="scss">
  .modulation-bits {
    background-color: var(--background-color);
    padding: 0 20px 20px 20px;
    color: var(--el-text-color);
    height: 500px;
    .chart-bar {
      padding: 8px 0;
    }
  }
  .symbol-table {
    padding: 20px;
    border: 1px solid var(--card--border-color);
    height: 440px;
    overflow-y: auto;
  }
</style>
