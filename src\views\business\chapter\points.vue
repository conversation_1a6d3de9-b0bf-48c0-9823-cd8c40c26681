<template>
  <el-col :span="24" class="app-container pl-4">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      @selectionChange="rows => (selection = rows)"
    />

    <DetailInfo ref="refsMt" :chapter-id="chapterId" @actSuccess="getList()" />
    <FileListPreview ref="refsFile" />
    <mtDetailInfo ref="refsMtwarning" @actSuccess="getList()" />
    <QaAdd ref="refsQa" />
  </el-col>
</template>
<script>
  import * as mtApi from '@/api/business/points'
  import DetailInfo from './detail copy.vue'
  import mtDetailInfo from './tmpInfo.vue'
  import router from '@/router'
  import QaAdd from './qa.vue'
  export default {
    name: 'PointsList',
    components: { DetailInfo, QaAdd, mtDetailInfo },
    props: ['chapterId'],
    data() {
      return {
        selection: [],
        tableFormConfig: {
          title: '知识点',
          searchConfig: {
            formConfig: [{ title: '知识点', name: 'pointName' }],
            loadData: p => {
              p.chapterId = this.chapterId
              return this.chapterId ? mtApi.listPoints(p) : Promise.resolve({ rows: [] })
            }
          },
          actions: [
            {
              label: '新增',
              type: 'primary',
              icon: 'el_Plus',
              permission: ['business:points:add'],
              disabled: () => !this.chapterId,
              click: () => {
                this.$refs.refsMt.show()
              }
            },
            // {
            //   label: '导出',
            //   icon: 'el_Download',
            //   isPlain: true,
            //   disabled: () => !this.chapterId,
            //   permission: ['business:points:export'],
            //   click: row =>
            //     this.download(
            //       'business/points/export',
            //       { chapterId: this.chapterId },
            //       `points_${new Date().getTime()}.xlsx`
            //     )
            // },
            {
              type: 'danger',
              label: '删除',
              permission: ['business:points:remove'],
              disabled: () => !this.selection.length,
              isPlain: true,
              icon: 'el_Delete',
              click: row => this.deleteAll()
            }
          ],
          tableConfig: {
            isChecked: true,
            cols: [
              { label: '知识点', prop: 'pointName', minWidth: 200 },
              {
                label: '操作',
                type: 'action',
                width: '240',
                actions: [
                  {
                    title: '编辑',
                    label: '编辑',
                    // icon: 'el_Edit',
                    permission: ['business:points:edit'],
                    click: row => this.$refs.refsMt.show(row.pointId)
                  },
                  {
                    title: '提问',
                    label: '提问',
                    // icon: 'el_ChatLineRound',
                    permission: ['business:qa:add'],
                    click: row => this.$refs.refsQa.show('', row.pointId)
                  },
                  {
                    title: '教学资料',
                    label: '教学资料',
                    // icon: 'el_Memo',
                    click: row =>
                      mtApi.getPoints(row.pointId).then(res => {
                        this.$refs.refsFile.show(res.data.srFileList)
                      })
                  },
                  {
                    title: '删除',
                    permission: ['business:points:remove'],
                    label: '删除',
                    // icon: 'el_Delete',
                    click: row => this.deleteAll(row.pointId)
                  },
                  {
                    title: '答疑',
                    permission: ['business:points:remove'],
                    label: '答疑',
                    // icon: 'el_Warning',
                    click: row => {
                      router.push({
                        path: '/information/qa',
                        query: { pointId: row.pointId }
                      })

                      // if (row.qaId) {
                      // this.$refs.refsMtwarning.show('75bc08bbb3cc49c0a025c93d6d9959cb')
                      // } else {
                      //   this.$modal.msgWarning('11111')
                      // }
                    }
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList(data = null) {
        this.$refs.refsTableForm.loadData(data)
      },
      //批量删除模板
      deleteAll(id = null) {
        if (!this.selection.length && !id) return this.$modal.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选知识点吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.objId)
          mtApi.delPoints(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.getList()
          })
        })
      }
    }
  }
</script>
