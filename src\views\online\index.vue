<template>
  <div key="online-container" class="online-container">
    <el-row key="online-main" class="main">
      <el-col :span="18">
        <ScanSection>
          <!--频谱图  -->
          <AnalyseCharts
            key="online-chart"
            usekey="online"
            :data-list="dataList"
            :model="spectrum"
            :device-enum="deviceList"
            :device-list="deviceDataList"
            v-model:checkList="checkList"
            v-model:deviceCode="deviceCode"
          />
          <!-- 信号、采集、回放功能按钮模块 -->
          <BottomSignalTabs
            v-if="!spectrum.type"
            :signal-list="signalList"
            :warm-list="warmSigList"
            :deviceInfo="codeFindInfo"
            @stop="closeScan"
          />
          <div class="flex justify-end items-center py-2">
            <div
              v-if="warmSigList.length <= 0"
              class="text-[#fff] mr-2 px-1.5 py-1 border border-[#56bcbe] rounded-sm"
              >当前设备 ：{{ codeFindInfo.label }} ；工作状态 ：{{
                spectrum.status ? '频段扫描' : '断连'
              }}
              ；IP地址 ：{{ codeFindInfo.ip }}</div
            >
            <!-- warning -->
            <div v-else class="scrolling-text">
              <div
                >{{ getAlarmType(scrollingMessage.type) }}：信号频率 ：{{
                  numToPlot(scrollingMessage.freqHz)
                }}
                信号带宽 ：{{ numToPlot(scrollingMessage.bwHz) }} 信号幅度 ：{{
                  scrollingMessage.level
                }}dBm</div
              >
            </div>
          </div>
        </ScanSection>
      </el-col>
      <el-col :span="6" class="chart-form">
        <ScanSection>
          <div class="form">
            <!-- 右侧参数设置模块 -->
            <RightSettings :model="spectrum" @start="startWebsocket" @stop="closeScan">
              <template #default>
                <!-- 离散-->
                <ScatterSetting ref="scatterRef" v-show="spectrum.type" />
                <!-- 连续  -->
                <FreBandSetting ref="freBandRef" v-show="!spectrum.type" />
              </template>
              <template #formItem="{ form }">
                <el-form-item label="设备参数">
                  <el-button :loading="paramsLoading" @click="paramSetting(form)">
                    设备参数设置
                  </el-button>
                </el-form-item>
              </template>
            </RightSettings>
          </div>
        </ScanSection>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Scan">
  import ScanSection from '@/components/SpectrumSignal/ScanSection.vue'
  import AnalyseCharts from '@/views/analyseCharts/index.vue'
  import RightSettings from '@/components/SettingsFormPro/index.vue'
  import FreBandSetting from './rightSettings/FreBandSetting.vue'
  import ScatterSetting from './rightSettings/ScatterSetting.vue'
  import BottomSignalTabs from './bottomSignalTabs/index.vue'
  import useScanStore from '@/store/modules/scanMonitor'
  import useLinkWsHook from '@/common/hooks/linkWs'
  import { ElMessage } from 'element-plus'
  import { linkWs } from '@/api/upperComputer/scan'
  import { SCAN_CODE, DISPERSED_CODE, PARAMS_SETTING } from '@/constant/funCodes'
  import { deviceParams } from '@/constant/types'
  import { plotToNum } from '@/utils/utils'
  import { getEquipList } from '@/api/system/equipment'

  const route = useRoute()
  const scanStore = useScanStore()
  const spectrum = scanStore.spectrumCopy
  // const checkList = ref(['实时', '平均', '最大', '最小', '门限'])
  const checkList = ref(['实时'])
  // 查询参数
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10
  })
  const deviceCode = ref(null) //当前设备编码
  const host = ref(null) //当前设备ip
  const port = ref(null) //当前设备端口
  const deviceList = ref([]) //设备列表数据
  const resData = ref({}) //接收路由传递参数
  const freBandRef = ref(null) // 连续表单实例
  const scatterRef = ref(null) //离散表单实例
  const deviceData = ref({}) //设备参数
  const deviceDataList = ref([]) //数据列表

  // 用于存储最后一次有效的 signalList
  const lastSignalList = ref([])

  // 获取设备列表,渲染主机/子机
  const getList = async () => {
    deviceDataList.value = []
    deviceData.value = []
    await getEquipList(queryParams.value).then(response => {
      deviceDataList.value = response.data.list
      deviceData.value = response.data.list[0]
      deviceList.value = []
      response.data.list.forEach(item => {
        deviceList.value.push({
          label: item.name,
          value: item.code,
          ip: item.ip,
          port: item.port
        })
      })
    })
  }

  const init = async () => {
    await getList()
    dealRouteInfo()
  }

  watch(deviceCode, async newValue => {
    const idx = deviceDataList.value.findIndex(item => item.code === deviceCode.value)
    const resData = deviceDataList.value[idx]
    scanStore.setSpectrumData('spectrumCopy', {
      centerFreq: resData.deviceScan.centerFreq,
      bandwidth: resData.deviceScan.bandwidth,
      startFreq: resData.deviceScan.startFreq,
      endFreq: resData.deviceScan.endFreq,
      step: resData.deviceScan.step.toString(),
      freqSectionNum: resData.deviceScan.freqSectionNum,
      freqBandStr: resData.deviceScan.freqBandStr,
      type: resData.deviceScan.type
    })
    scanStore.updateTaskParams('spectrumCopy', resData.devicePara)
    // 等待下一次 DOM 更新周期再调用子组件的方法
    await nextTick()
    freBandRef.value.dataChangeFun()
    scatterRef.value.dataChangeFun()
  })

  // 计算属性：查找设备信息
  const codeFindInfo = computed(() => {
    if (!deviceCode.value) {
      return {}
    }
    const resIdx = deviceList.value.findIndex(item => item.value === deviceCode.value)
    const resInfo = deviceList.value[resIdx]
    if (resInfo) {
      return resInfo
    } else if (resData.value) {
      return {
        label: resData.value.name,
        value: resData.value.code,
        ip: resData.value.ip,
        port: resData.value.port
      }
    }
    return {}
  })
  // 单位下拉参数
  const selectForm = ref({})
  // 使用 watchEffect 确保 selectForm 在数据变化时也能提供
  watchEffect(() => {
    if (freBandRef.value) {
      selectForm.value = freBandRef.value.selectForm
    } else {
      selectForm.value = {}
    }
  })

  provide('selectForm', selectForm)
  provide('freBandRef', freBandRef)
  provide('codeFindInfo', codeFindInfo)

  const paramsLoading = ref(false)
  const taskFunCode = computed(() => {
    const { type, commandType } = spectrum
    if (commandType && type) {
      return null
    } else if (!commandType && !type) {
      return SCAN_CODE
    } else if (!commandType && type) {
      return DISPERSED_CODE
    } else {
      return null
    }
  })

  const { linkScan, closeScan, pageData } = useLinkWsHook(spectrum, taskFunCode)
  const dataList = computed(() => {
    let initData = {
      current: [],
      average: [],
      max: [],
      min: [],
      limit: [],
      occupancy: []
    }
    if (scanStore.playFfts.length > 0 && spectrum.status === 0) {
      const current = scanStore.playFfts
      return { current: current }
    }
    if (!pageData.value) {
      return initData
    }
    if (taskFunCode.value === SCAN_CODE) {
      const data = pageData.value.subtaskResultBody
      if (!data) {
        return initData
      }
      const length = data.frequencyListFromSrList.length
      const current = checkList.value.includes('实时') ? new Array(length) : null
      const average = checkList.value.includes('平均') ? new Array(length) : null
      const max = checkList.value.includes('最大') ? new Array(length) : null
      const min = checkList.value.includes('最小') ? new Array(length) : null
      const limit = checkList.value.includes('门限') ? new Array(length) : null
      const occupancy = new Array(length)

      for (let i = 0; i < length; i++) {
        const item = data.frequencyListFromSrList[i]
        current[i] = item.level
        if (average) average[i] = item.average
        if (max) max[i] = item.maximum
        if (min) min[i] = item.minimum
        if (limit) limit[i] = item.threshold
        occupancy[i] = item.occupancy
      }

      if (current) initData.current.push(...current)
      if (average) initData.average.push(...average)
      if (max) initData.max.push(...max)
      if (min) initData.min.push(...min)
      if (limit) initData.limit.push(...limit)
      if (occupancy) initData.occupancy.push(...occupancy)
    } else if (taskFunCode.value === DISPERSED_CODE) {
      const data = pageData.value.discreteChannelResult?.frequencyListFromDcList
      if (!data) {
        return initData
      }
      data.sort((a, b) => a.frequency - b.frequency)
      data.forEach(item => {
        initData.current.push([item.frequency, item.level])
        initData.average.push([item.frequency, item.average])
        initData.max.push([item.frequency, item.maximum])
        initData.min.push([item.frequency, item.minimum])
        initData.limit.push([item.frequency, item.threshold])
        initData.occupancy.push([item.frequency, item.occupancy])
      })
    }
    return initData
  })

  // 计算属性：信号列表
  const signalList = computed(() => {
    if (spectrum.status === 0 && scanStore.playSignalList?.length > 0) {
      return scanStore.playSignalList
    }
    if (!pageData.value || !pageData.value.subtaskResultBody) {
      // 如果 WebSocket 停止，返回最后一次的信号列表
      return lastSignalList.value
    }
    // 存储最新的 signalList 到 lastSignalList
    lastSignalList.value = pageData.value.subtaskResultBody.signalFromSrList || []
    return lastSignalList.value
  })

  //最新的警告列表
  const lastWarmSigList = ref([])

  // 计算属性：警告信号列表
  const warmSigList = computed(() => {
    const currentList = pageData.value?.warmSigList
    if (currentList && currentList.length > 0) {
      lastWarmSigList.value = currentList // 更新到最后一次已知的有效列表
    }
    return currentList && currentList.length > 0 ? currentList : lastWarmSigList.value
  })

  const scrollingIndex = ref(0)
  const scrollingMessage = ref(warmSigList.value[0] || '')
  let intervalId = null

  // 当 warmList 更新时，重新设置滚动信息，避免数组越界
  watch(
    () => warmSigList.value,
    newList => {
      if (scrollingIndex.value >= newList.length) {
        scrollingIndex.value = 0 // 重置索引以避免超出新列表长度
      }
      scrollingMessage.value = newList[scrollingIndex.value] || ''
    },
    { deep: true }
  )

  const getAlarmType = type => {
    switch (type) {
      case 0:
        return '黑名单'
      case 2:
        return '功率告警'
      case 3:
        return '模板告警'
      default:
        return '未知类型'
    }
  }

  // 更新消息并确保在动画开始时更新
  const updateMessage = () => {
    if (warmSigList.value.length > 0) {
      // 动画周期结束后，更新信息
      setTimeout(() => {
        scrollingIndex.value = (scrollingIndex.value + 1) % warmSigList.value.length
        scrollingMessage.value = warmSigList.value[scrollingIndex.value]
      }, 3000) // 假设动画周期是6秒，我们在3秒后更新信息
    }
  }

  /**
   * @description 设备参数设置
   * @param form 表单实例
   */
  const paramSetting = async form => {
    let ws = null
    const data = {
      parameterCount: deviceParams.length,
      parameterCode: deviceParams.map(item => item.code),
      parameterValue: deviceParams.map(item => form[item.key])
    }
    paramsLoading.value = true
    host.value = codeFindInfo.value.ip
    port.value = codeFindInfo.value.port
    ws = await linkWs({
      host: host.value,
      port: port.value,
      taskFunCode: PARAMS_SETTING,
      sendTime: 10,
      deviceCode: deviceCode.value,
      data: encodeURIComponent(JSON.stringify(data))
    })
    ws.onmessage = event => {
      paramsLoading.value = false
      try {
        const rsp = JSON.parse(event.data)
        if (rsp.code === 200) {
          ElMessage.success('设备参数设置成功')
        } else {
          ElMessage.error('设备参数设置失败')
        }
      } catch (err) {
        console.log(err)
      }
      ws.close()
    }
  }
  /**
   * 生成参数
   * @param form 表单对象
   * @returns 返回一个对象，包含各种参数
   */
  const generateParams = form => {
    if (taskFunCode.value === SCAN_CODE) {
      return {
        dataType: 1,
        // thresholdType: form.thresholdType,
        // threshold: form.threshold,
        thresholdType: spectrum.thresholdType,
        threshold: spectrum.threshold,
        terminationDirection: 90,
        azimuthStep: 10,
        polarization: parseInt(form.polarizationType, 16),
        startFrequency: plotToNum(spectrum.startFreq + freBandRef.value.selectForm.startFreq),
        endFrequency: plotToNum(spectrum.endFreq + freBandRef.value.selectForm.endFreq),
        frequencyStep: spectrum.step,
        isSaveFreq: spectrum.isSaveFreq
      }
    } else if (taskFunCode.value === DISPERSED_CODE) {
      return {
        dataType: 1,
        // thresholdType: form.thresholdType,
        // threshold: form.threshold,
        thresholdType: spectrum.thresholdType,
        threshold: spectrum.threshold,
        polarization: parseInt(form.polarizationType, 16),
        discrete: spectrum.discrete,
        frequencyCount: spectrum.discrete.length,
        analyseBandwidth: plotToNum(spectrum.bandwidth + scatterRef.value.selectForm.bandwidth),
        isSaveFreq: spectrum.isSaveFreq
      }
    }
  }

  const startWebsocket = form => {
    const params = generateParams(form)
    console.log(params, 'websocket传递参数')
    console.log(deviceCode.value, 'websocket传递设备编码')
    host.value = codeFindInfo.value.ip
    port.value = codeFindInfo.value.port
    linkScan(params, host.value, port.value, deviceCode.value)
  }

  const dealRouteInfo = () => {
    // 路由信息处理
    if (route.query?.data) {
      resData.value = JSON.parse(route.query.data)
      deviceCode.value = resData.value.code
      scanStore.setSpectrumData('spectrumCopy', {
        centerFreq: resData.value.deviceScan.centerFreq,
        bandwidth: resData.value.deviceScan.bandwidth,
        startFreq: resData.value.deviceScan.startFreq,
        endFreq: resData.value.deviceScan.endFreq,
        step: resData.value.deviceScan.step.toString(),
        freqSectionNum: resData.value.deviceScan.freqSectionNum,
        freqBandStr: resData.value.deviceScan.freqBandStr,
        type: resData.value.deviceScan.type,
        thresholdType: resData.value.deviceScan.thresholdType,
        threshold: resData.value.deviceScan.threshold
      })
      scanStore.updateTaskParams('spectrumCopy', resData.value.devicePara)
      freBandRef.value.dataChangeFun()
      scatterRef.value.dataChangeFun()
    }
    //处理接口数据
    else {
      scanStore.setSpectrumData('spectrumCopy', {
        centerFreq: deviceData.value.deviceScan.centerFreq,
        bandwidth: deviceData.value.deviceScan.bandwidth,
        startFreq: deviceData.value.deviceScan.startFreq,
        endFreq: deviceData.value.deviceScan.endFreq,
        step: deviceData.value.deviceScan.step.toString(),
        freqSectionNum: deviceData.value.deviceScan.freqSectionNum,
        freqBandStr: deviceData.value.deviceScan.freqBandStr,
        type: deviceData.value.deviceScan.type,
        thresholdType: deviceData.value.deviceScan.thresholdType,
        threshold: deviceData.value.deviceScan.threshold
      })
      scanStore.updateTaskParams('spectrumCopy', deviceData.value.devicePara)
      freBandRef.value.dataChangeFun()
      scatterRef.value.dataChangeFun()
      deviceCode.value = deviceList.value.length > 0 ? deviceList.value[0].value : null
      host.value = deviceList.value.length > 0 ? deviceList.value[0].ip : null
      port.value = deviceList.value.length > 0 ? deviceList.value[0].port : null
    }
  }

  /**页面初始化 */
  onMounted(async () => {
    await init()
    intervalId = setInterval(updateMessage, 6000) // 动画周期调整为6秒
  })

  onBeforeUnmount(() => {
    clearInterval(intervalId)
  })

  /**路由激活 */
  // onActivated(async () => {
  //   await init()
  // })
</script>

<style scoped lang="scss">
  @import '@/assets/styles/mixin.scss';

  .online-container {
    @include boxBg('@/assets/images/boxBg.png');
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 1rem;
    overflow-y: auto;
  }

  .chart-form {
    padding-left: 10px;
  }
</style>
