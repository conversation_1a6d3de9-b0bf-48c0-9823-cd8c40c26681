import request from '@/utils/request'

// 查询虚拟资源分配信息列表
export function listShare(query) {
  return request({
    url: '/business/share/list',
    method: 'get',
    params: query
  })
}

// 查询虚拟资源分配信息详细
export function getShare(shId) {
  return request({
    url: '/business/share/' + shId,
    method: 'get'
  })
}

// 新增虚拟资源分配信息
export function addShare(data) {
  return request({
    url: '/business/share',
    method: 'post',
    data: data
  })
}

// 修改虚拟资源分配信息
export function updateShare(data) {
  return request({
    url: '/business/share',
    method: 'put',
    data: data
  })
}

// 删除虚拟资源分配信息
export function delShare(shId) {
  return request({
    url: '/business/share/' + shId,
    method: 'delete'
  })
}
