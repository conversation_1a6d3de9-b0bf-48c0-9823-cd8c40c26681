<template>
  <!-- <el-button style="width: 40%;margin-left:10%;" @click="ITUmeasure">ITU参数测量</el-button> -->
  <el-button style="width: 80%; margin: 0 10%" @click="emit('operation')">信号分选</el-button>
</template>

<script setup>
  import useChartsStore from '@/store/modules/charts'
  import useScanFormStore from '@/store/modules/form/scanForm'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { getITU } from '@/api/charts'
  import { cloneDeep } from 'lodash'
  const emit = defineEmits(['operation'])
  const chartsStore = useChartsStore()
  const scanFormStore = useScanFormStore()
  const body = useScanFormStore().getFileParams()
  const ITUmeasure = () => {
    const { fileCenterFreIn, fileSamplingRate, fileBitRate, fileIfbw } = scanFormStore
    body.centerFreqIn = parseFloat(fileCenterFreIn)
    body.intermediateFrequencyBandwidth = parseFloat(fileIfbw)
    body.samplingRate = parseFloat(fileSamplingRate)
    body.bitRate = parseFloat(fileBitRate)
    getITU(body).then(res => {
      if (res.code === 200) {
        ElMessage.success('ITU参数测量成功')
        chartsStore.setParamsEstimate(res.data)
      }
    })
  }
</script>
