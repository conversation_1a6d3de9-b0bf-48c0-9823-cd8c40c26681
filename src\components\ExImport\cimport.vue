<template>
  <el-dialog v-model="importDia" :title="config.title || '上传文件'" width="580px">
    <div class="upload-wrap">
      <el-upload
        ref="refsUpload"
        :key="importDia + '' + freshKey"
        drag
        action=""
        :http-request="cusUpload"
        :auto-upload="false"
        accept=".xlsx, .xls"
        :limit="1"
      >
        <x-icon icon="UploadFilled" class="el-icon--upload" size="60" />
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            仅允许导入xls、xlsx格式文件。<el-link
              v-if="config.tmpUrl"
              type="primary"
              @click="downLoadTmp"
            >
              <x-icon source-icon="cus_export" size="14" />
              {{ '下载' + config.tmpName + '模板' || ' 下载导入模板' }}
            </el-link>
          </div>
        </template>
      </el-upload>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button v-loading="loading" type="primary" @click="$refs.refsUpload.submit()">
        导入
      </el-button>
    </template>
  </el-dialog>
</template>
<script>
  // import http from '@/utils/http/axios'
  import request from '@/utils/request'
  import { ElMessage, ElMessageBox } from 'element-plus'
  export default {
    data() {
      return {
        importDia: false,
        loading: false,
        config: {},
        freshKey: Date.now()
      }
    },
    methods: {
      show(config) {
        this.config = config
        this.importDia = true
      },
      close() {
        this.config = {}
        this.loading = false
        this.importDia = false
      },
      downLoadTmp() {
        this.download(
          this.config.tmpUrl,
          null,
          `${this.config.tmpName + '模板'|| '下载模板'}_${new Date().getTime()}.xlsx`,
          this.config.tmpDownloadMethod
        )
      },
      async cusUpload({ file }) {
        if (!file) return this.msgWarning('请选择要上传的文件')
        this.loading = true
        //上传文件校验
        if (this.config.checkUrl) {
          let check = {}
          await 
            request(
              {
                url: this.config.checkUrl,
                data: { file:file},
                method: 'post'
              },
              { form: true }
            )
            .then(res => {
              check = res
            })
            .catch(_ => {
              this.loading = false
              check = null
              this.freshKey = Date.now()
              this.config.fail && this.config.fail(_)
            })
          if (!check || check.code != 200) return
          if (check.msg && check.msg != '操作成功') {
            ElMessageBox.confirm(check.msg + '确定要覆盖导入吗？', '提示')
              .then(() => {
                this.checkSubmit(file)
              })
              .catch(() => {
                this.loading = false
              })
          } else {
            this.checkSubmit(file)
          }
        } else {
          this.checkSubmit(file)
        }
      },
      checkSubmit(file) {
        
        console.log('file',file)
        //上传文件
        request(
            {
              url: this.config.uploadUrl,
              data: {file:file},
              method: 'POST',
              headers:{
                
                'Content-Type':'multipart/form-data'
              }
            }
          )
          .then(res => {
            ElMessage.success(res.msg || '导入成功')
            this.config.ok && this.config.ok(res)
            this.close()
          })
          .catch(err => {
            console.log('err', err, 1111)
            this.config.fail && this.config.fail(err)
          })
          .finally(() => {
            this.freshKey = Date.now()
            this.loading = false
          })
      }
    }
  }
</script>
<style lang="less" scoped>
  .upload-wrap {
    margin: 0 auto;
    width: 500px;
    :deep(.el-upload-dragger) {
      width: 500px !important;
    }
  }
</style>
