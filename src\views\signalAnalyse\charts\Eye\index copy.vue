<template>
  <!-- <div class="eye-container"> -->
  <!-- <chart-header title="眼图" name="iEye" /> -->
  <!-- <el-row :gutter="10"> -->
  <!-- <el-col :span="12"> -->
  <div class="w-full" id="iEye" />
  <!-- </el-col> -->
  <!-- <el-col :span="12">
        <div id="qEye" />
      </el-col> -->
  <!-- </el-row> -->
  <!-- </div> -->
</template>

<script setup>
  // 眼图
  import useChartsStore from '@/store/modules/charts'
  import ChartHeader from '@/views/analyse/header/ChartHeader'
  import useDmFormStore from '@/store/modules/form/dmForm'
  import useChartOptions from '@/common/hooks/chartOptions'
  import Highcharts from '@/plugins/highcharts'
  import Interval from '@/common/classes/interval'
  // import ci from '@/common/chartInstances'
  import { setInstance } from '@/utils/instance'
  import { cloneDeep, round } from 'lodash'

  defineComponent([ChartHeader])

  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })

  watch(
    () => props.data,
    newData => {
      if (newData) {
        // updateSeries(newData)
        const eyestep = Math.ceil(newData.eyestep)
        iData.value = generateEyeData(newData.pdIBase, eyestep)

        // // if (iContainer.value) {
        // //   addPointsToSeries(newData)
        // //   console.log(iContainer.value)
        // // } else {
        initCharts()
        // // }
      }
    }
  )

  let iContainer = ref(null) // 左眼图
  // let qContainer = null
  let interval = null
  const chartsStore = useChartsStore()
  const iData = ref([])
  // const qData = ref([])
  // const { settings } = chartsStore
  // 一组几个点
  const groupNum = ref(0)
  const form = useDmFormStore()
  const { themeStyle, chartOptions } = useChartOptions()
  const isStatic = chartsStore.static.enabled
  const generateEyeData = (data, eyestep = 25) => {
    const result = []
    for (let i = 0; i < data.length; i += eyestep) {
      let n = 0,
        temp = []
      while (n < eyestep) {
        temp.push([n, data[i + n]])
        n++
      }
      result.push(temp)
    }
    return result
  }
  const genearteChartsOptions = data => {
    const currentOptions = cloneDeep(chartOptions.value)
    // 设置标题颜色
    currentOptions.title.text = '眼图'
    currentOptions.title.style.color = '#f3f3f3'
    currentOptions.yAxis.min = -1.6
    currentOptions.yAxis.max = 1.6
    currentOptions.xAxis.min = 0
    const max = Math.ceil(props.data.eyestep) - 1
    currentOptions.xAxis.max = max
    currentOptions.xAxis.tickInterval = max / 10
    currentOptions.xAxis.labels.formatter = function () {
      if (this.value === 0) {
        return -1
      }
      if (this.value === max / 2) {
        return 0
      }
      if (this.value === max) {
        return 1
      }
    }
    currentOptions.xAxis.labels.renderFormatter = function () {
      return round(-1 + (this.value / max) * 2, 2)
    }
    currentOptions.xAxis.labels.style.color = '#f3f3f3'
    currentOptions.xAxis.gridLineColor = '#f3f3f3'
    currentOptions.xAxis.lineColor = '#f3f3f3'
    currentOptions.yAxis.labels.style.color = '#f3f3f3'
    currentOptions.yAxis.gridLineColor = '#f3f3f3'
    currentOptions.yAxis.lineColor = '#f3f3f3'
    currentOptions.yAxis.tickAmount = 11
    currentOptions.yAxis.tickInterval = 0.32
    currentOptions.yAxis.labels.formatter = function () {
      return round(this.value, 1)
    }
    const t = isStatic ? data.slice(0, chartsStore.static.dmLimit) : data.slice(0, form.resultLen)
    currentOptions.series = t.map(item => ({
      type: 'spline',
      marker: {
        enabled: false
      },
      enableMouseTracking: false,
      animation: false,
      lineWidth: 0.5,
      color: '#f3f55c',
      data: item
    }))

    currentOptions.chart.backgroundColor = '#020e1f'
    currentOptions.chart.events = {}
    console.log(currentOptions)
    return currentOptions
  }
  const initCharts = () => {
    iContainer.value = new Highcharts.Chart('iEye', genearteChartsOptions(iData.value))
    setInstance(markRaw(iContainer.value))
    console.log(iContainer.value, '2379482798')
    // qContainer = new Highcharts.Chart('qEye', genearteChartsOptions(qData.value))
    // ci.set(iContainer.value, 'iEye')
    // ci.set(qContainer, 'qEye')
  }
  const generateViewData = (data, start) => {
    const showNum = form.resultLen
    const len = data.length
    let temp = []
    if (start + showNum >= len) {
      temp = data.slice(-showNum)
    } else {
      temp = data.slice(start, start + showNum)
    }
    return temp
  }

  // 更新图表
  const updateChart = (start, data, instance) => {
    if (!instance) {
      return
    }
    const curViewData = generateViewData(data, start)
    instance.series?.forEach((s, i) => {
      s.setData(curViewData[i], true, false)
    })
  }
  const upadteDataChart = (start = 0) => {
    updateChart(start, iData.value, iContainer.value)
    // updateChart(start, qData.value, qContainer)
  }

  // 更新 series 数据
  const updateSeries = data => {
    const newPoints = generateEyeData(data.pdIBase, Math.ceil(data.eyestep))
    console.log(iContainer.value.series, 'series')
    iContainer.value.series.forEach((series, seriesIndex) => {
      if (!series) return
      series.setData(newPoints[seriesIndex], false)
    })
    iContainer.value.redraw()
  }

  const init = async () => {
    const eyestep = Math.ceil(props.data.eyestep)
    iData.value = generateEyeData(props.data.pdIBase, eyestep)
    // qData.value = generateEyeData(props.data.pdQBase, eyestep)
    initCharts()
    if (!isStatic) {
      interval = Interval.create(
        upadteDataChart,
        form.resultLen,
        form.resultLen,
        iData.value.length
      )
    }
  }

  watchEffect(() => {
    const { modulation } = form
    groupNum.value = Math.pow(2, modulation)
  })

  watch(
    () => themeStyle.value,
    () => {
      iContainer.value.update(genearteChartsOptions(iData.value), true)
      // qContainer.update(genearteChartsOptions(qData.value), true)
    }
  )

  onMounted(() => {
    init()
  })

  onUnmounted(() => {
    interval?.destroy()
  })
</script>

<style scoped>
  .eye-container {
    background-color: '#020e1f';
  }
</style>
