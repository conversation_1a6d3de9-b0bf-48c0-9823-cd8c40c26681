<template>
  <el-form>
    <el-form-item label="分析频率">
      <el-input v-model="form.centerFreq" type="number" @change="dataChangeFun('centerFreq')">
        <template #append>
          <el-select v-model="selectForm.centerFreq" @change="dataChangeFun('centerFreq')">
            <el-option v-for="unit in units" :key="unit" :label="unit" :value="unit" />
          </el-select>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label="分析带宽">
      <el-select v-model="form.bandwidth" class="w-full" @change="linkage('bandwidth')">
        <el-option
          v-for="item in analyzingBw"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="xdB带宽">
      <el-select v-model="form.xdbBandwidth" class="w-11/12">
        <el-option
          v-for="item in xdbBandwidthTypes"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <span class="text-[#fff]">db</span>
      <!-- <el-input v-model="form.xdbBandwidth" type="number" /> -->
    </el-form-item>
    <el-form-item label="β带宽">
      <el-input v-model="form.bBandwidth" type="number" />
    </el-form-item>
    <el-form-item label="方位角">
      <el-input v-model="signal.azimuth[0]" type="number" style="width: 50%">
        <template #append>
          <span class="pl-1 pr-1">° 到</span>
        </template>
      </el-input>
      <el-input v-model="signal.azimuth[1]" type="number" style="width: 50%">
        <template #append>
          <span class="pl-1 pr-1">°</span>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label="俯仰角">
      <el-input v-model="signal.pitch[0]" type="number" style="width: 50%">
        <template #append>
          <span class="pl-1 pr-1">° 到</span>
        </template>
      </el-input>
      <el-input v-model="signal.pitch[1]" type="number" style="width: 50%">
        <template #append>
          <span class="pl-1 pr-1">°</span>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label="音频解调">
      <el-select v-model="signal.demodulationType" style="width: 100%">
        <el-option
          v-for="item in audioDemodulationTypes"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="解调带宽">
      <el-select v-model="form.demodulationBandwidth" class="w-full">
        <el-option
          v-for="item in demodulateBw"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </el-form>
</template>
<script setup>
  import {
    demodulateBw,
    audioDemodulationTypes,
    analyzingBw,
    xdbBandwidthTypes
  } from '@/constant/types'
  import useScanStore from '@/store/modules/scanMonitor'
  import { numToPlot, plotToNum } from '@/utils/utils'

  const scanStore = useScanStore()
  const { signal } = useScanStore()
  const form = ref(signal)
  const selectForm = ref({
    centerFreq: 'Hz',
    bandwidth: 'Hz',
    startFreq: 'Hz',
    endFreq: 'Hz'
  })
  const units = ['GHz', 'MHz', 'KHz', 'Hz']
  const dataChangeFun = async key => {
    let formValue = {
      centerFreq: form.value.centerFreq
    }
    if (key) {
      signal[key] = plotToNum(form.value[key] + selectForm.value[key])
      await nextTick()
      formatValue(key, form.value[key])
      linkage(key)
      return
    }
    Object.keys(selectForm.value).forEach(async key => {
      if (formValue[key]) {
        formatValue(key, formValue[key])
      }
      linkage(key)
      await nextTick()
    })
  }
  const linkage = key => {
    const freStore = signal
    const bandwidth = plotToNum(freStore.bandwidth + selectForm.value.bandwidth) // 确保带宽是数值
    const centerFreq = plotToNum(freStore.centerFreq + selectForm.value.centerFreq)
    // console.log(centerFreq, '中心频率')
    // console.log(bandwidth, '带宽')

    if (key === 'centerFreq' || key === 'bandwidth') {
      // 根据中心频率和带宽计算新的起始频率和终止频率
      let newStartFreq = centerFreq - bandwidth / 2
      let newEndFreq = centerFreq + bandwidth / 2

      // 确保起始频率不低于最小阈值，例如20 MHz
      if (newStartFreq < 20e6) {
        newStartFreq = 20e6
        newEndFreq = newStartFreq + bandwidth
      }

      // 更新频率存储
      freStore.startFreq = newStartFreq
      freStore.endFreq = newEndFreq

      // 确保终止频率不低于0
      if (newEndFreq < 0) {
        freStore.endFreq = 0
      }

      formatValue('startFreq', freStore.startFreq)
      formatValue('endFreq', freStore.endFreq)
    }
  }

  const formatValue = async (key, value) => {
    const newFormVal = numToPlot(value)
    if (newFormVal.search('KHz') != -1) {
      form.value[key] = newFormVal.replace('KHz', '')
      selectForm.value[key] = 'KHz'
    } else if (newFormVal.search('MHz') != -1) {
      form.value[key] = newFormVal.replace('MHz', '')
      selectForm.value[key] = 'MHz'
    } else if (newFormVal.search('GHz') != -1) {
      form.value[key] = newFormVal.replace('GHz', '')
      selectForm.value[key] = 'GHz'
    }
    await nextTick()
    scanStore.updateSpectrumUnit('signal', key, selectForm.value[key])
  }

  onMounted(() => {
    dataChangeFun()
  })

  defineExpose({
    selectForm,
    dataChangeFun
  })
</script>
