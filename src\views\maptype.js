import { XYZ } from 'ol/source'
const baseUrl = import.meta.env.DEV
  ? import.meta.env.VITE_GLOB_MAP_BASE_API
  : window.__PRODUCTION__KYCUSTOMER__CONF__.VITE_GLOB_MAP_BASE_API
let list = [
  {
    name: '街道图',
    value: new XYZ({
      url: baseUrl + '/tiles/{z}/{x}/{y}.png'
    }),
    id: '0'
  },
  {
    name: '卫星地图',
    value: new XYZ({
      url: baseUrl + '/Satellite/{z}/{x}/{y}.jpg'
    }),
    id: '1'
  },
  {
    name: '地形图',
    value: new XYZ({
      url: baseUrl + '/terrain/{z}/{x}/{y}.jpg'
    }),
    id: '2'
  },
  {
    name: '地名',
    value: new XYZ({
      url: baseUrl + '/overlay/{z}/{x}/{y}.png'
    }),
    id: '3'
  }
]

export default list
