<template>
  <el-tabs class="mt-6">
    <el-tab-pane>
      <template #label>
        <el-button class="tabs-button">
          信号
        </el-button>
      </template>
      <div class="px-2">
        <RadarTable v-if="monitor.type" :signal-list="signalList" />
        <div v-else>
          <DirectionFinding v-if="monitor.commandType" />
          <SignalTable v-else :signal-list="signalList" />
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane>
      <template #label>
        <el-button class="tabs-button">
          IQ采集
        </el-button>
      </template>
      <Gather />
    </el-tab-pane>
    <el-tab-pane>
      <template #label>
        <el-button class="tabs-button">
          IQ回放
        </el-button>
      </template>
      <Playback />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup name="BottomMonitorTabs">
import Gather from './Gather.vue'
import Playback from './Playback.vue'
import SignalTable from './SignalTable.vue'
import RadarTable from './RadarTable.vue'
import DirectionFinding from '@/components/DirectionFinding'
import useScanMonitorStore from '@/store/modules/scanMonitor'

const {monitor} = useScanMonitorStore()
const props = defineProps({
  signalList: {
    type: Array,
    default: () => []
  }
})

</script>