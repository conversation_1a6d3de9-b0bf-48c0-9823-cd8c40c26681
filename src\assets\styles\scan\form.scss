.content-font {
  color: var(--scan-text-color);
  display: inline-block;
  padding-right: 20px;
  line-height: 30px;
  width: 100%;
}
select {
  cursor: pointer;
}

.gather-item {
  background-color: var(--list-color);
  border: 1px solid var(--main-border-color);
  padding: 16px 24px;
  display: flex;
  flex-wrap: wrap;
  height: 260px;

  .left {
    width: 40%;
    .el-form-item__content > .el-input,
    .el-select {
      width: 80%;
    }
  }

  .right {
    flex: 1;
    padding-top: 12px;

    .info-list {
      padding: 0 0 20px 36px;

      .label {
        color: var(--table-scrollbar-color);
        width: 100px;
        display: inline-block;
      }

      .value {
        color: var(--scan-text-color);
      }
    }
  }
}

.sidebar-settings {
  .el-form {
    padding-left: 16px;
    width: 80%;
    :deep(.el-form-item__label) {
      width: 80px !important;
    }
  }
}

.scantabs {
  display: flex;
  font-size: 8px;
  color: rgb(255, 255, 255);
  margin: 12px 16px;
  .st-item {
    width: 120px;
    height: 22px;
    background: url(@/assets/images/u66.svg);
    white-space: nowrap;
    line-height: 22px;
    text-align: center;
    opacity: 0.5;
    overflow: hidden;
    cursor: pointer;
    &.active {
      opacity: 1;
    }
  }
}
