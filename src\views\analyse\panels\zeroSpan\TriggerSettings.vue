<template>
  <data-form :model="model" :fields="fields" />
  <el-button @click="editFMT">编辑 FMT</el-button>
  <el-dialog v-model="dialogVisble" title="FMT编辑器">
    <el-row :gutter="20">
      <el-col :span="4" class="fmt-form">
        <el-button type="primary">添加</el-button>
        <el-button type="danger" class="mt-3">删除</el-button>
        <el-divider />
        <el-button type="danger">清空</el-button>
        <el-button class="mt-3">从信号流创建</el-button>
        <el-divider />
        <el-button>导入</el-button>
        <el-button class="mt-3">导出</el-button>
        <el-divider />
        <label>X偏移</label>
        <el-input label="X偏移" />
        <label class="mt-3">Y偏移</label>
        <el-input label="Y偏移" />
        <el-button class="mt-3">应用偏移</el-button>
      </el-col>
      <el-col :span="8">
        <vxe-table
          border
          :edit-config="{ trigger: 'click', mode: 'row' }"
          :data="tableData"
        >
          <vxe-table-column field="num" title="序号" width="40" />
          <vxe-table-column field="rate" title="频率" :edit-render="{ name: 'input' }" />
          <vxe-table-column field="amplitude" title="幅度" :edit-render="{ name: 'input' }" />
        </vxe-table>
      </el-col>
      <el-col :span="12">
        <Spectrum v-if="dialogVisble" id="spectrumInDialog" :has-header="false" />
      </el-col>
    </el-row>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import Spectrum from '../../modules/Spectrum.vue'
  import { levelUnits } from '@/constant/units'
  import ModelData from '@/common/classes/modelData';

  defineProps({
    model: Object
  })

  defineComponent([Spectrum])
  const dialogVisble = ref(false)
  const messuseTypes = [
    { label: '立即触发', value: 1 },
    { label: '视频触发', value: 2 },
    { label: '外部触发', value: 3 },
    { label: '频率屏蔽触发', value: 4 }
  ]

  const fields = [
    { target: 'triggerType', name: '触发类型', options: messuseTypes, isSelect: true },
    {
      target: 'triggerSide',
      name: '触发沿',
      options: [
        { label: '上升沿', value: 1 },
        { label: '下降沿', value: 2 }
      ],
      isSelect: true
    },
    {
      target: 'triggerLevel',
      name: '触发电平',
      appendSelect: true,
      options: levelUnits
    },
    { target: 'triggerPosition', name: '触发位置', appendText: '%' }
  ]
  const tableData = ref([])
  const editFMT = () => {
    dialogVisble.value = true
  }
  const close = () => {
    dialogVisble.value = false
  }
  const initTableData = (num = 5) => {
    for (let i = 0; i < num; i++) {
      tableData.value.push({
        num: i + 1,
        rate: '0.00MHz',
        amplitude: '0.00'
      })
    }
  }
  const model = reactive(new ModelData(fields, 'zp'))

  onMounted(() => {
    initTableData()
  })
</script>

<style scoped lang="scss">
  .fmt-form {
    .el-button {
      width: 100%;
      margin-left: 0;
    }
    .el-divider {
      margin: 18px 0;
    }
  }
</style>
