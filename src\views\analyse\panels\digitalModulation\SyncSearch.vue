<template>
  <data-form :model="model" :fields="fields" />
</template>

<script setup>

import DataForm from '@/components/DataForm'
import ModelData from '@/common/classes/modelData'

const fields = [
  { target: 'enabled', name: "功能开关", isCheckbox: true },
  { target: 'pattern', name: "模式(Hex)"},
  { target: 'syncCodeLen', name: "同步码长度", appendText: '符号' },
  { target: 'searchLen', name: "搜索长度", appendText: '符号' },
  { target: 'offset', name: "偏移", appendText: '符号' },
]

const model = reactive(new ModelData(fields, 'dm'))
</script>
