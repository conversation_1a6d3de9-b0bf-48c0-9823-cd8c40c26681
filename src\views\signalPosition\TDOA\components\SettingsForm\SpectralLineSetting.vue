<template>
  <div>
    <el-form class="left">
      <el-form-item label="参考电平">
        <el-input v-model="refLevel" type="number">
          <template #append>
            <span class="pl-1 pr-1">dBm</span>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup>
  import useScanStore from '@/store/modules/scanMonitor'

  const refLevel = defineModel()

  // const spectrum = useScanStore().spectrumCopy
</script>
<style scoped></style>
