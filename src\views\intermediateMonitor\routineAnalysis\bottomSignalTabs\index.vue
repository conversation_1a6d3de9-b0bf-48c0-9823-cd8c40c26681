<template>
  <el-tabs class="mt-6">
    <el-tab-pane>
      <template #label>
        <el-button class="tabs-button"> 信号 </el-button>
      </template>
      <DirectionFinding v-if="spectrum.commandType" :table-data="tableData" />
      <AnalyseTable v-else :table-data="tableData" @position="position" @analyze="analyze" />
      <div class="flex justify-between items-center">
        <div class="scantabs">
          <div
            v-for="(item, index) in spectrumList"
            :key="item.title"
            :class="{ active: activeKey === index }"
            class="st-item"
            @click="tabClick(index)"
          >
            {{ item.title }}
          </div>
        </div>
        <div
          v-if="warmList.length <= 0"
          class="text-[#fff] mr-2 px-1.5 py-1 border border-[#56bcbe] rounded-sm"
          >当前设备 ：{{ deviceInfo.label }} ；工作状态 ：{{
            spectrum.status ? '频段扫描' : '断连'
          }}
          ；IP地址 ：{{ deviceInfo.ip }}</div
        >
        <!-- warning -->
        <div v-else class="scrolling-text">
          <div
            >{{ getAlarmType(scrollingMessage.type) }}：信号频率 ：{{
              numToPlot(scrollingMessage.freqHz)
            }}
            信号带宽 ：{{ numToPlot(scrollingMessage.bwHz) }} 信号幅度 ：{{
              scrollingMessage.level
            }}dBm</div
          >
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane>
      <template #label>
        <el-button class="tabs-button"> 采集 </el-button>
      </template>
      <Gather />
      <div class="flex justify-end items-center">
        <div
          v-if="warmList.length <= 0"
          class="text-[#fff] mr-2 px-1.5 py-1 border border-[#56bcbe] rounded-sm"
          >当前设备 ：{{ deviceInfo.label }} ；工作状态 ：{{
            spectrum.status ? '频段扫描' : '断连'
          }}
          ；IP地址 ：{{ deviceInfo.ip }}</div
        >
        <!-- warning -->
        <div v-else class="scrolling-text">
          <div
            >{{ getAlarmType(scrollingMessage.type) }}：信号频率 ：{{
              numToPlot(scrollingMessage.freqHz)
            }}
            信号带宽 ：{{ numToPlot(scrollingMessage.bwHz) }} 信号幅度 ：{{
              scrollingMessage.level
            }}dBm</div
          >
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane lazy>
      <template #label>
        <el-button class="tabs-button"> 回放 </el-button>
      </template>
      <Playback />
      <div class="flex justify-end items-center">
        <div
          v-if="warmList.length <= 0"
          class="text-[#fff] mr-2 px-1.5 py-1 border border-[#56bcbe] rounded-sm"
          >当前设备 ：{{ deviceInfo.label }} ；工作状态 ：{{
            spectrum.status ? '频段扫描' : '断连'
          }}
          ；IP地址 ：{{ deviceInfo.ip }}</div
        >
        <!-- warning -->
        <div v-else class="scrolling-text">
          <div
            >{{ getAlarmType(scrollingMessage.type) }}：信号频率 ：{{
              numToPlot(scrollingMessage.freqHz)
            }}
            信号带宽 ：{{ numToPlot(scrollingMessage.bwHz) }} 信号幅度 ：{{
              scrollingMessage.level
            }}dBm</div
          >
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane>
      <template #label>
        <el-button class="tabs-button"> 告警 </el-button>
      </template>
      <warning :table-data="warmList" />
      <div class="flex justify-end items-center">
        <div
          v-if="warmList.length <= 0"
          class="text-[#fff] mr-2 px-1.5 py-1 border border-[#56bcbe] rounded-sm"
          >当前设备 ：{{ deviceInfo.label }} ；工作状态 ：{{
            spectrum.status ? '频段扫描' : '断连'
          }}
          ；IP地址 ：{{ deviceInfo.ip }}</div
        >
        <!-- warning -->
        <div v-else class="scrolling-text">
          <div
            >{{ getAlarmType(scrollingMessage.type) }}：信号频率 ：{{
              numToPlot(scrollingMessage.bwHz)
            }}
            信号带宽 ：{{ numToPlot(scrollingMessage.freqHz) }} 信号幅度 ：{{
              scrollingMessage.level
            }}dBm</div
          >
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup name="BottomSignalTabs">
  import Gather from './Gather.vue'
  import Playback from './Playback.vue'
  import DirectionFinding from '@/components/DirectionFinding'
  import AnalyseTable from './AnalyseTable.vue'
  import warning from './warning.vue'
  import useScanStore from '@/store/modules/scanMonitor'
  import { numToPlot } from '@/utils/utils'
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const props = defineProps({
    signalList: {
      type: Array,
      default: () => []
    },
    deviceInfo: {
      type: Object,
      default: () => {}
    },
    warmList: {
      type: Object,
      default: () => []
    }
  })
  const activeKey = ref(0)
  const scanStore = useScanStore()
  const { signal } = useScanStore()
  const emit = defineEmits(['stop'])
  const spectrum = scanStore.routineAnalysis
  const spectrumList = computed(() => {
    const signalList = props.signalList
    const len = signalList.length
    signalList.sort((a, b) => a.centerFreIn - b.centerFreIn)
    const itemNum = Math.ceil(signalList.length / spectrum.freqSectionNum)
    const list = []
    let temp = []
    for (let i = 0; i < len; i++) {
      temp.push(signalList[i])
      if (temp.length === itemNum || i === len - 1) {
        const start = temp[0]
        const end = temp[temp.length - 1]
        const title = generateTabTitle(start, end)
        list.push({ title, body: temp })
        temp = []
      }
    }
    return list
  })
  const tableData = computed(() => {
    return spectrumList.value[activeKey.value]?.body || []
  })
  const generateTabTitle = (start, end) => {
    const diff = end.signalCenterFrequency - start.signalCenterFrequency
    return `${numToPlot(start.signalCenterFrequency, '', 0)} - ${numToPlot(
      end.signalCenterFrequency,
      '',
      0
    )}(${numToPlot(diff, '', 1)})`
  }
  const tabClick = index => {
    activeKey.value = index
  }

  const getAlarmType = type => {
    switch (type) {
      case 0:
        return '黑名单'
      case 2:
        return '功率告警'
      case 3:
        return '模板告警'
      default:
        return '未知类型'
    }
  }

  const scrollingIndex = ref(0)
  const scrollingMessage = ref(props.warmList[0] || '')

  let intervalId = null

  // 更新消息并确保在动画开始时更新
  const updateMessage = () => {
    if (props.warmList.length > 0) {
      // 动画周期结束后，更新信息
      setTimeout(() => {
        scrollingIndex.value = (scrollingIndex.value + 1) % props.warmList.length
        scrollingMessage.value = props.warmList[scrollingIndex.value]
      }, 3000) // 假设动画周期是6秒，我们在3秒后更新信息
    }
  }

  onMounted(() => {
    intervalId = setInterval(updateMessage, 6000) // 动画周期调整为6秒
  })

  onBeforeUnmount(() => {
    clearInterval(intervalId)
  })

  // 当 warmList 更新时，重新设置滚动信息，避免数组越界
  watch(
    () => props.warmList,
    newList => {
      if (scrollingIndex.value >= newList.length) {
        scrollingIndex.value = 0 // 重置索引以避免超出新列表长度
      }
      scrollingMessage.value = newList[scrollingIndex.value] || ''
    },
    { deep: true }
  )

  /**
   * 分析信号信息
   * @param row 信号信息对象
   * @param row.signalFrequency 信号频率
   * @param row.bandwidth1 信号带宽
   */
  const analyze = data => {
    emit('stop')
    // scanStore.setSpectrumData('signal', {
    //   centerFreq: data.signalFrequency,
    //   bandwidth: data.bandwidth1,
    //   startFre: data.signalFrequency - data.bandwidth1 / 2,
    //   endFre: data.signalFrequency + data.bandwidth1 / 2
    // })
    const queryData = { ...spectrum }
    queryData.centerFreq = data.signalCenterFrequency
    queryData.code = props.deviceInfo.value
    router.push({
      name: 'ScanSignalAnalyse',
      query: {
        data: JSON.stringify(queryData)
      }
    })
  }

  /**
   * 根据传入的行数据，跳转到信号定位TDOA页面
   * @param row 行数据
   */
  const position = data => {
    emit('stop')
    router.push({
      path: '/signalPosition/TDOA',
      query: {
        // centerFreq: data.signalCenterFrequency,
        // bandwidth: data.signalBandwidth,
        signalList: JSON.stringify(data)
      }
    })
  }
</script>

<style lang="scss" scoped>
  .scrolling-text {
    overflow: hidden;
    position: relative;
    width: 580px;
    height: 30px; // 定义一个固定高度
    line-height: 30px; // 保持文本垂直居中
    background: #fff;
    color: #c72a29;
    border: 1px solid #56bcbe;
    border-radius: 4px;
    text-align: center;
  }

  .scrolling-text div {
    position: absolute;
    width: 100%;
    height: 100%;
    text-align: center; // 文本居中显示
    animation: scrollUpDown 6s linear infinite;
  }

  @keyframes scrollUpDown {
    0%,
    100% {
      transform: translateY(100%); // 初始和结束位置在容器下方，使得内容不可见
    }
    25%,
    75% {
      transform: translateY(0); // 中间状态，内容完全可见
    }
  }
</style>
