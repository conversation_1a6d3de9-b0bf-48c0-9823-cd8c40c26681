<template>
  <el-table border class="w-full" :data="equipList">
    <el-table-column label="启用" align="center" width="50">
      <template #default="{ $index }">
        <el-checkbox v-model="selectedCodes[$index]" size="large" />
      </template>
    </el-table-column>
    <el-table-column label="设备" prop="name" align="center" />
    <el-table-column label="经度" prop="longitude" align="center" />
    <el-table-column label="纬度" prop="latitude" align="center" />
    <el-table-column label="操作" align="center">
      <template #default="{ row }">
        <div class="border-0 cover w-full rounded-md p-1 cursor-pointer" @click="setParams(row)">
          参数设置
        </div>
      </template>
    </el-table-column>
  </el-table>

  <!-- 弹出层  -->
  <el-dialog
    v-model="dialogTableVisible"
    title="设备参数设置"
    width="450"
    center
    class="dialog-class"
  >
    <div class="h-10 w-full bg-[#fae053] text-[#050505] leading-10 px-5 mb-5">
      当前设备：{{ currentDevice }}；IP地址：{{ IP }}
    </div>
    <el-form>
      <el-form-item label="测量模式">
        <el-select v-model="devicePara.measureMode" type="number">
          <el-option
            v-for="item in measure_mode"
            :key="Number(item.value)"
            :label="item.label"
            :value="Number(item.value)"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="检波方式">
        <el-select v-model="devicePara.detectionMode" type="number">
          <el-option
            v-for="item in detection_mode"
            :key="Number(item.value)"
            :label="item.label"
            :value="Number(item.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="检波速度">
        <el-input v-model="devicePara.detectionSpeed" type="number" />
      </el-form-item> -->
      <el-form-item label="衰减模式">
        <el-select v-model="devicePara.attenuationMode" type="number">
          <el-option
            v-for="item in attenuation_mode"
            :key="Number(item.value)"
            :value="Number(item.value)"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="衰减值">
        <el-input v-model="devicePara.attenuation" type="number">
          <template #append>
            <span class="pl-1 pr-1">dB</span>
          </template>
        </el-input>
      </el-form-item>
      <!-- <el-form-item label="增益模式">
        <el-select v-model="devicePara.gainMode" type="number">
          <el-option
            v-for="item in gain_mode"
            :key="Number(item.value)"
            :value="Number(item.value)"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="增益值">
        <el-input v-model="devicePara.gain" type="number">
          <template #append>
            <span class="pl-1 pr-1">dbm</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="驻留时间">
        <el-input v-model="devicePara.lingerTime" type="number">
          <template #append>
            <span class="pl-1 pr-1">ms</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="门限类型">
        <el-select v-model="devicePara.thresholdType">
          <el-option
            v-for="item in threshold_type"
            :key="Number(item.value)"
            :value="Number(item.value)"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="门限">
        <el-input v-model="devicePara.threshold" type="number">
          <template #append>
            <span class="pl-1 pr-1">dBμV/m</span>
          </template>
        </el-input>
      </el-form-item> -->
    </el-form>
    <div class="flex justify-center mt-6">
      <el-button class="ctl-btn" @click="commitSet"> 设置 </el-button>
      <el-button class="ctl-btn close" @click="cancelSet"> 取消 </el-button>
    </div>
  </el-dialog>
</template>

<script setup>
  import { saveOrUpdatePara } from '@/api/system/equipment'
  import { ElMessage } from 'element-plus'
  import { ref, getCurrentInstance } from 'vue'

  const { proxy } = getCurrentInstance()
  const { measure_mode, detection_mode, attenuation_mode, gain_mode, threshold_type } =
    proxy.useDict(
      'measure_mode',
      'detection_mode',
      'attenuation_mode',
      'gain_mode',
      'threshold_type'
    )
  const props = defineProps({
    equipList: {
      type: Array,
      default: () => []
    }
  })

  const dialogTableVisible = ref(false)
  const devicePara = ref(null)
  const deviceId = ref(null)
  const currentDevice = ref('')
  const IP = ref('')
  const selectedCodes = ref([true, true, true])

  const setParams = row => {
    if (row.devicePara === null) {
      ElMessage.error('设备参数为空')
      return
    }
    devicePara.value = row.devicePara
    deviceId.value = row.id
    currentDevice.value = row.name
    IP.value = row.ip
    dialogTableVisible.value = true
  }

  const commitSet = () => {
    saveOrUpdatePara(savePara())
      .then(response => {
        proxy.$modal.msgSuccess('参数设置成功')
        dialogTableVisible.value = false
        getList()
      })
      .catch(() => {
        proxy.$modal.msgError('参数设置失败')
      })
  }

  const cancelSet = () => {
    dialogTableVisible.value = false
  }

  const savePara = () => {
    const submitData = {
      deviceId: deviceId.value,
      id: devicePara.value.id,
      measureMode: devicePara.value.measureMode,
      detectionMode: devicePara.value.detectionMode,
      detectionSpeed: devicePara.value.detectionSpeed,
      attenuationMode: devicePara.value.attenuationMode,
      attenuation: devicePara.value.attenuation,
      gainMode: devicePara.value.gainMode,
      gain: devicePara.value.gain,
      lingerTime: devicePara.value.lingerTime,
      thresholdType: devicePara.value.thresholdType,
      threshold: devicePara.value.threshold
    }
    return submitData
  }

  const dealCode = () => {
    let codeArr = []
    selectedCodes.value.map((item, index) => {
      if (item) codeArr.push(props.equipList[index].code)
    })
    return codeArr
  }

  defineExpose({
    dealCode
  })
</script>

<style lang="scss" scoped>
  .el-table {
    width: 95%;
    background: transparent;
    --el-table-border: 1px solid #555555;
    --el-table-border-color: #555555;
    --el-border-color: #555555;
    --el-table-row-hover-bg-color: var(--list-color);
    --el-table-tr-bg-color: transparent;
    color: #ffffff;
  }

  :deep(th.el-table__cell) {
    background: url(@/assets/images/tabletrbg.png) no-repeat;
    background-size: 100% 100%;
    font-weight: 300;
    color: var(--scan-text-color);
    .cell {
      padding: 0;
    }
  }
  .cover {
    background: url(@/assets/images/tableopbtn.png) no-repeat;
    background-size: cover;
  }
</style>

<style lang="scss">
  .dialog-class {
    background: #212121;
    .el-dialog__header {
      background-color: #01bfbf;
      margin-right: 0;
      padding: 10px;
      .el-dialog__title {
        color: #fff;
        font-weight: 600;
      }
      .el-dialog__headerbtn {
        top: 0px;
      }
      .el-dialog__headerbtn .el-dialog__close {
        font-size: 24px;
        color: #fff;
      }
    }
    .el-dialog__body {
      padding: 0 0 40px !important;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .el-select {
      width: 100%;
    }
  }
</style>
