<template>
  <div style="width: 100%">
    <quill-editor
      v-if="!disabled"
      ref="quillEditor"
      v-model:content="content"
      theme="snow"
      :read-only="disabled"
      :toolbar="disabled ? 'essential' : ''"
      content-type="html"
      :style="{ height: height || '300px' }"
    />
    <div v-else class="htmlcontentwrap" v-html="modelValue" />
  </div>
</template>
<script>
  import { QuillEditor } from '@vueup/vue-quill'
  import '@vueup/vue-quill/dist/vue-quill.snow.css'
  export default {
    components: {
      QuillEditor
    },
    props: ['modelValue', 'height', 'disabled'],
    data() {
      return {
        content: this.modelValue
      }
    },
    watch: {
      content: function () {
        this.$emit('update:modelValue', this.content)
      }
    }
  }
</script>
<style lang="less" scoped>
  .htmlcontentwrap {
    border: 1px solid #e4e7ed;
    line-height: 2;
    position: relative;
    padding: 3px 10px;
    border-radius: 4px;
    min-height: 140px;
  }
</style>
