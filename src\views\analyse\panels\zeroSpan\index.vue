<template>
  <el-collapse v-model="activeItems" class="collapse-panels">
    <el-collapse-item
      v-for="panel in panels"
      :key="panel.value"
      :title="panel.title"
      :name="panel.value"
    >
      <component :is="dom[panel.value]" />
    </el-collapse-item>
  </el-collapse>
</template>

<script setup name="zeroSpanPanels">
  import CaptureSettings from './CaptureSettings.vue'
  import TriggerSettings from './TriggerSettings.vue'
  import FftSettings from './FftSettings.vue'
  import MessuseSettings from './MessuseSettings.vue'

  const panels = [
    {
      title: '捕获设置',
      value: 'CaptureSettings',
    },
    {
      title: '触发设定',
      value: 'TriggerSettings',
    },
    {
      title: '测量间隔设置',
      value: 'MessuseSettings',
    },
    {
      title: 'FFT 设置',
      value: 'FftSettings',
    }
  ]

  const emit = defineEmits(['update:modelValue'])

  const dom = shallowReactive({
    CaptureSettings,
    TriggerSettings,
    MessuseSettings,
    FftSettings
  })

  const activeItems = ref(panels.map(panel => panel.value))
</script>

<style scoped lang="scss">
  .panels-leave-active,
  .panels-enter-active {
    transition: all 0.8s ease-in-out;
  }

  .panels-enter-from {
    transform: translateY(-200px);
    opacity: 0;
  }
  .panels-leave-to {
    transform: translateY(-200px);
    opacity: 0;
  }
</style>
