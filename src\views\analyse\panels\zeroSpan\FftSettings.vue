<template>
  <data-form :fields="fields" :model="model" />
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData';
  import { timeUnits, rateUnits } from '@/constant/units'

  const fields = ref([
    { target: 'rbw', name: 'RBW', appendSelect: true, options: rateUnits, },
    {
      target: 'autOverlap',
      name: '自动重叠',
      isCheckbox: true
    },
    { target: 'overlap', name: '重叠%' },
    { target: 'maxFft', name: '最大FFT数' },
    { target: 'step', name: '步进长度', appendSelect: true, options: timeUnits }
  ])

  const model = reactive(new ModelData(fields.value, 'zp'))
</script>
