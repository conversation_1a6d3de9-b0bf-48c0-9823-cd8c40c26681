import request from '@/utils/request'

//文件上传
export const uploadFile = (data, onUploadProgress = () => {}) => {
  return request({
    method: 'post',
    url: '/file/largeFile',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data,
    onUploadProgress
  })
}
export const mergeChunks = (data, params) => {
  return request({
    method: 'post',
    url: '/file/mergeFile',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data,
    params
  })
}

export const uploadFilePre = params => {
  return request({
    method: 'post',
    url: '/file/largeFilePre',
    params: params
  })
}



//文件上传
export const uploadRadarFile = (data, onUploadProgress = () => {}) => {
  return request({
    method: 'post',
    url: '/radar/largeFile',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data,
    onUploadProgress
  })
}
export const mergeRadarChunks = (data, params) => {
  return request({
    method: 'post',
    url: '/radar/mergeFile',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data,
    params
  })
}

export const uploadRadarFilePre = params => {
  return request({
    method: 'post',
    url: '/radar/largeFilePre',
    params: params
  })
}

