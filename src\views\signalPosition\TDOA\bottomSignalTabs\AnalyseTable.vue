<template>
  <el-table class="max-h-[270px] h-[270px]" border :height="270" :data="tableData">
    <el-table-column label="序号" type="index" width="60" align="center" />
    <el-table-column label="中心频率" prop="signalCenterFrequency" width="160" align="center">
      <template #default="{ row }">
        {{ numToPlot(row.signalCenterFrequency) }}
      </template>
    </el-table-column>
    <el-table-column label="信号带宽" prop="signalBandwidth" align="center">
      <template #default="{ row }">
        {{ numToPlot(row.signalBandwidth) }}
      </template>
    </el-table-column>
    <el-table-column label="幅度(dBm)" prop="signalLevel" align="center">
      <template #default="{ row }">
        {{ round(row.signalLevel, 3) }}
      </template>
    </el-table-column>
    <!-- <el-table-column label="最大(dBm)" prop="max" align="center" /> -->
    <!-- <el-table-column label="最小(dBm)" prop="min" align="center" />
    <el-table-column label="首次发现时间" prop="firstTime" align="center" />
    <el-table-column label="最新更新时间" prop="lastTime" align="center" /> -->
    <el-table-column label="操作" width="120" align="center">
      <template #default="{ row }">
        <el-button class="cover" size="small" @click="emit('pick', row)" :disabled="true"
          >定位</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
  import { numToPlot } from '@/utils/utils'
  import { round } from 'lodash'

  const props = defineProps({
    tableData: {
      type: Array,
      default: () => []
    }
  })
  const emit = defineEmits(['pick'])
</script>
