<template>
  <div class="flex items-center pr-[10px]">
    <el-tooltip content="分析模式" placement="top" effect="light">
      <el-dropdown trigger="click">
        <x-icon icon="analysemode" size="20" source="cus" />
        <template #dropdown>
          <el-dropdown-menu>
            <!-- 分析模式（零扫宽/数字调制分析） -->
            <el-dropdown-item v-for="item in modeTypes" :key="item.value" @click="switchMode(item)">
              {{ item.title }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-tooltip>
    <!-- 头部菜单栏（零扫宽 ZeroSpan/数字调制分析 DmHeader） -->
    <temp v-if="temp" />
    <!-- 设置按钮 对应  零扫宽/数字调制分析-->
    <el-tooltip v-if="temp" content="设置" placement="top" effect="light">
      <x-icon
        icon="setting"
        source="el"
        size="20"
        style="cursor: pointer"
        class="mx-4"
        @click="togglePanels"
      />
    </el-tooltip>
    <!-- 设置面板 -->
    <Panels v-model="settingPaneVisible" class="panels-box" />
  </div>
</template>

<script setup>
  import Panels from '@/views/analyse/panels'
  import { useRoute } from 'vue-router'
  // import useChartsStore from '@/store/modules/charts'
  import router from '@/router'

  const temp = ref(null)
  const settingPaneVisible = ref(false)
  const route = useRoute()
  const modeTypes = [
    { title: '零扫宽', value: 'ZeroSpan' },
    { title: '数字调制分析', value: 'DigitalModulation' }
    // { title: '谐波', value: 'Harmonic' }
  ]
  /**
   * 切换面板显示状态
   * @returns 无返回值
   */
  const togglePanels = () => {
    settingPaneVisible.value = !settingPaneVisible.value
  }
  /**
   * 切换模式
   * @param item 切换模式的对象
   * @param item.value 切换后的模式名称
   */
  const switchMode = item => {
    router.push({
      name: item.value
    })
  }

  // 动态组件
  const comps = {
    zeroSpan: defineAsyncComponent(() => import('./ZeroSpanHeader.vue')),
    digitalModulation: defineAsyncComponent(() => import('./DmHeader.vue'))
  }

  watchEffect(() => {
    if (route?.path.includes('zeroSpan')) {
      temp.value = comps['zeroSpan']
    } else if (route?.path.includes('digitalModulation')) {
      temp.value = comps['digitalModulation']
    } else {
      temp.value = null
    }
  })
</script>
