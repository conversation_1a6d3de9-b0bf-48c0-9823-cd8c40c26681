<template>
  <div class="py-1 mx-5">
    <div class="flex justify-center cursor-pointer" @click="playBarVisible = !playBarVisible">
      <x-icon v-show="playBarVisible" source="cus" icon="arrowDoubleDown" />
      <x-icon v-show="!playBarVisible" source="cus" icon="arrowDoubleUp" />
    </div>
    <el-row v-show="playBarVisible">
      <el-col>
        <div class="play-tool flex">
          <!-- 播放 -->
          <el-button @click="togglePlay">
            <x-icon
              v-if="!isPlaying"
              icon="CaretRight"
              source="el"
              size="24"
              :disabled="isDisabled"
            />
            <x-icon v-else icon="pause" size="24" source="cus" :disabled="isDisabled" />
          </el-button>
          <!-- 停止按钮 -->
          <el-button @click="stopPlay">
            <x-icon icon="stop" size="24" source="cus" :disabled="isDisabled" />
          </el-button>
          <!-- 快退按钮 -->
          <el-button @click="fastBack">
            <x-icon icon="fastback" size="24" source="cus" :disabled="isDisabled" />
          </el-button>
          <!-- 后退按钮 -->
          <el-button @click="backByStep">
            <x-icon icon="back" size="24" source="cus" :disabled="isDisabled" />
          </el-button>
          <!-- 前进按钮 -->
          <el-button @click="forwardByStep">
            <x-icon icon="forward" size="24" source="cus" :disabled="isDisabled" />
          </el-button>
          <!-- 刷新按钮 -->
          <el-button @click="refreshPlay">
            <x-icon icon="Refresh" size="24" source="el" />
          </el-button>
          <!-- 当前播放文件信息 -->
          <div class="flex justify-center items-center ml-2">{{ statusText }}</div>
        </div>
        <!-- 进度条组件 -->
        <Slider
          v-model="curOffset"
          class="my-5"
          :min="0"
          :max="maxOffset"
          :is-drag="!isDisabled"
          @update:modelValue="setStartOffset"
          @change:startOffset="handleStartOffsetChange"
        />
        <!-- 信息显示 -->
        <div class="">
          <el-checkbox v-model="isInfinite" label="循环播放" size="large" />
          <div class="pb-2 pt-1"> 回放进度: {{ sweep }} </div>
          <div class="pt-1 w-1/4 flex">
            <div class="w-[100px] py-2">回放速度</div>
            <el-input v-model="playBackTime" :disabled="isPlaying">
              <template #suffix> ms </template>
            </el-input>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import Slider from '@/components/Slider/index.vue'
  import { usePlayback } from '@/common/hooks/usePlaybackControls' // 引入封装的 usePlayback Hook

  const props = defineProps({
    pollingFunctions: {
      type: Function,
      required: true
    }
  })

  const {
    isPlaying,
    startOffset,
    maxOffset,
    playBackTime,
    isInfinite,
    togglePlay,
    play,
    stopPlay,
    fastBack,
    backByStep,
    forwardByStep,
    refreshPlay,
    setStartOffset,
    clearTimer
  } = usePlayback(props.pollingFunctions) // 使用 usePlayback Hook

  const playBarVisible = ref(true)

  const curOffset = computed({
    get: () => {
      // 返回当前的 startOffset 或 maxOffset，如果两者都没有则返回 0
      return startOffset.value ? startOffset.value : maxOffset.value ? maxOffset.value : 0
    },
    set: value => {
      // 当 curOffset 改变时，更新 startOffset
      setStartOffset(value)
    }
  })

  // 计算扫描进度
  const sweep = computed(() => {
    if (maxOffset.value === 0) return '0%'
    if (maxOffset.value === 1 || (startOffset.value === 0 && maxOffset.value >= 1)) return '100%'
    const percent = (startOffset.value / maxOffset.value) * 100
    return `${percent.toFixed(2)}%`
  })

  const isDisabled = computed(() => {
    return maxOffset.value === 1
  })

  // 计算播放状态文本
  const statusText = computed(() => {
    if (isPlaying.value && maxOffset.value > 0) {
      return '播放中'
    } else if (
      (startOffset.value === maxOffset.value && !isPlaying.value && maxOffset.value > 0) ||
      maxOffset.value === 1
    ) {
      return '播放完毕'
    }
    return '等待播放'
  })

  // 处理开始偏移量变化
  const handleStartOffsetChange = value => {
    console.log('StartOffset changed:', value)
    clearTimer()
    play()
  }
</script>

<style scoped lang="scss">
  .container {
    font-weight: 100;
    width: 100%;
    .container-header {
      display: flex;
      justify-content: center;
      cursor: pointer;
    }

    .play-tool {
      margin: 20px 0;
      .el-button {
        padding: 4px 0;
        margin: 0;
      }
    }
  }
</style>
