<script setup lang="ts">
  // for now el-switch does not support customized icon in the dot
  // we will implement a simple version of el-switch then update the switch
  // component for this feature
</script>

<template>
  <button class="switch" role="switch">
    <div class="switch__action">
      <div class="switch__icon">
        <slot />
      </div>
    </div>
  </button>
</template>

<style scoped>
  .switch {
    margin: 0;
    display: inline-block;
    position: relative;
    width: 40px;
    height: 20px;
    border: 1px solid var(--el-border-color);
    outline: none;
    border-radius: 10px;
    box-sizing: border-box;
    background: var(--el-color-info-light-9);
    cursor: pointer;
    transition: border-color 0.3s, background-color 0.3s;
  }
  .switch__action,
  .switch__icon {
    width: 16px;
    height: 16px;
  }
  .switch__action {
    position: absolute;
    top: 1px;
    left: 1px;
    border-radius: 50%;
    background-color: var(--el-bg-color);
    transform: translate(0);
    color: var(--text-color-light);
    transition: border-color var(--el-transition-duration),background-color var(--el-transition-duration),transform var(--el-transition-duration);
  }
  .switch__action,
  .switch__icon {
    width: 16px;
    height: 16px;
  }
  .switch__icon {
    position: relative;
  }
</style>
