import { AM_UNIT, RATE_UNIT, TIME_UNIT } from '@/constant'
import useScanStore from '@/store/modules/scanMonitor'
import * as XLSX from 'xlsx';

/**
 * 通用js方法封装处理
 */

// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time
        .replace(new RegExp(/-/gm), '/')
        .replace('T', ' ')
        .replace(new RegExp(/\.[\d]{3}/gm), '')
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields()
  }
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  let search = params
  search.params =
    typeof search.params === 'object' && search.params !== null && !Array.isArray(search.params)
      ? search.params
      : {}
  dateRange = Array.isArray(dateRange) ? dateRange : []
  if (typeof propName === 'undefined') {
    search.params['beginTime'] = dateRange[0]
    search.params['endTime'] = dateRange[1]
  } else {
    search.params['begin' + propName] = dateRange[0]
    search.params['end' + propName] = dateRange[1]
  }
  return search
}

// 回显数据字典
export function selectDictLabel(datas, value) {
  if (value === undefined) {
    return ''
  }
  var actions = []
  Object.keys(datas).some(key => {
    if (datas[key].value == '' + value) {
      actions.push(datas[key].label)
      return true
    }
  })
  if (actions.length === 0) {
    actions.push(value)
  }
  return actions.join('')
}

// 回显数据字典（字符串数组）
export function selectDictLabels(datas, value, separator) {
  if (value === undefined) {
    return ''
  }
  var actions = []
  var currentSeparator = undefined === separator ? ',' : separator
  var temp = value.split(currentSeparator)
  Object.keys(value.split(currentSeparator)).some(val => {
    var match = false
    Object.keys(datas).some(key => {
      if (datas[key].value == '' + temp[val]) {
        actions.push(datas[key].label + currentSeparator)
        match = true
      }
    })
    if (!match) {
      actions.push(temp[val] + currentSeparator)
    }
  })
  return actions.join('').substring(0, actions.join('').length - 1)
}

// 字符串格式化(%s )
export function sprintf(str) {
  var args = arguments,
    flag = true,
    i = 1
  str = str.replace(/%s/g, function () {
    var arg = args[i++]
    if (typeof arg === 'undefined') {
      flag = false
      return ''
    }
    return arg
  })
  return flag ? str : ''
}

// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str) {
  if (!str || str == 'undefined' || str == 'null') {
    return ''
  }
  return str
}

// 数据合并
export function mergeRecursive(source, target) {
  for (var p in target) {
    try {
      if (target[p].constructor == Object) {
        source[p] = mergeRecursive(source[p], target[p])
      } else {
        source[p] = target[p]
      }
    } catch (e) {
      source[p] = target[p]
    }
  }
  return source
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  let config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  }

  var childrenListMap = {}
  var nodeIds = {}
  var tree = []

  for (let d of data) {
    let parentId = d[config.parentId]
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = []
    }
    nodeIds[d[config.id]] = d
    childrenListMap[parentId].push(d)
  }

  for (let d of data) {
    let parentId = d[config.parentId]
    if (nodeIds[parentId] == null) {
      tree.push(d)
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t)
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]]
    }
    if (o[config.childrenList]) {
      for (let c of o[config.childrenList]) {
        adaptToChildrenList(c)
      }
    }
  }
  return tree
}

/**
 * 参数处理
 * @param {*} params  参数
 */
export function tansParams(params = {}) {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    var part = encodeURIComponent(propName) + '='
    if (value !== null && value !== '' && typeof value !== 'undefined') {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== '' && typeof value[key] !== 'undefined') {
            let params = propName + '[' + key + ']'
            var subPart = encodeURIComponent(params) + '='
            result += subPart + encodeURIComponent(value[key]) + '&'
          }
        }
      } else {
        result += part + encodeURIComponent(value) + '&'
      }
    }
  }
  return result
}

// 返回项目路径
export function getNormalPath(p) {
  if (p.length === 0 || !p || p == 'undefined') {
    return p
  }
  let res = p.replace('//', '/')
  if (res[res.length - 1] === '/') {
    return res.slice(0, res.length - 1)
  }
  return res
}

// 验证是否为blob格式
export async function blobValidate(data) {
  try {
    const text = await data.text()
    JSON.parse(text)
    return false
  } catch (error) {
    return true
  }
}

// 数字格式转化为频率格式
export function numToPlot(data, separator = '', digit = 6, isUnit = true) {
  let symbol = '',
    unit = ''
  if (data < 0) {
    symbol = '-'
    data = -data
  }
  if (data / 1000 >= 1 && data / 1000 < 1000) {
    isUnit && (unit = 'KHz')
    return symbol + (data / 1000).toFixed(digit) + separator + unit
  } else if (data / 1000000 >= 1 && data / 1000000 < 1000) {
    isUnit && (unit = 'MHz')
    return symbol + (data / 1000000).toFixed(digit) + separator + unit
  } else if (data / 1000000000 >= 1) {
    isUnit && (unit = 'GHz')
    return symbol + (data / 1000000000).toFixed(digit) + separator + unit
  } else {
    isUnit && (unit = 'Hz')
    return symbol + parseInt(data) + separator + unit
  }
}

export function numToTime(data, separator = '') {
  if (data / 1000 >= 1 && data / 1000 < 1000) {
    return (data / 1000).toFixed(2) + separator + 'ms'
  } else if (data / 1000000 >= 1 && data / 1000000 < 1000) {
    return (data / 1000000).toFixed(2) + separator + 's'
  } else {
    return data + separator + 'us'
  }
}

export function getNumAndUnit(data, unitType) {
  let result
  switch (unitType) {
    case TIME_UNIT.key:
      result = numToTime(data, '-').split('-')
      break
    case AM_UNIT.key:
      result = [data, 'dBm']
      break
    default:
      result = numToPlot(data, '-').split('-')
  }
  const [value, unit] = result
  return { value, unit }
}

export function timeToNum(data) {
  const numPart = parseFloat(data)
  if (data.endsWith('us')) {
    return numPart
  } else if (data.endsWith('ms')) {
    return parseFloat(numPart * 1e3)
  } else {
    return parseFloat(numPart * 1e6)
  }
}

export function mergeNumAndUnit(data, unitType) {
  let result
  switch (unitType) {
    case TIME_UNIT.key:
      result = timeToNum(data)
      break
    case AM_UNIT.key:
      result = parseInt(data)
      break
    default:
      result = plotToNum(data)
  }
  return parseInt(result)
}

// 频率格式转数字格式
export function plotToNum(data) {
  const numPart = parseFloat(data)
  if (data.endsWith('GHz')) {
    return parseFloat(numPart * 1e9)
  } else if (data.endsWith('MHz')) {
    return parseFloat(numPart * 1e6)
  } else if (data.endsWith('KHz')) {
    return parseFloat(numPart * 1e3)
  } else {
    return numPart
  }
}

/**
 *
 * @param {number} root
 * @param {number} base
 * @returns root是base的几次方
 */
export function getNumPow(root, base) {
  let result = 0
  while (root > 1) {
    root = root / base
    result += 1
  }
  return result
}

// pSrc 输入的两个点的y坐标，一头一尾，pDst 输出
// 正弦插值画法
export function waveSinP(pSrc, insNum, isReserve = true) {
  let sum = 0
  let temp = 0
  const srcLen = 2
  const WAVE_LEN = srcLen + insNum
  const sa_buf = []
  const interval = insNum + 1
  const pDst = []
  pDst[0] = pSrc[0]
  pDst[WAVE_LEN - 1] = pSrc[1]
  for (let i = 1; i < WAVE_LEN; i++) {
    temp = 0
    sum = 0
    let n = i
    for (let j = 0; j < srcLen; j++) {
      //计算内插系数    （-1, 0）
      temp = Math.PI * (i / interval - j) // j 取值 0 1；i是遍历来的系数
      temp -= 2 * temp // 取反
      if (temp === 0) {
        sa_buf[j] = 1 // 重置
      } else {
        sa_buf[j] = Math.sin(temp) / temp // 正弦插值， 当temp无限小， 该值接近 1，
      }
      if (pSrc[0] * pSrc[1] > 0 && Math.abs(pSrc[0]) > Math.abs(pSrc[1]) && isReserve) {
        // 首位方向一样，且 首大于尾时候转一下方向，接近1的值变为接近 0
        sa_buf[j] = 1 - sa_buf[j]
        n = WAVE_LEN - i - 1
      }
    }
    for (let j = 0; j < srcLen; j++) {
      sum += sa_buf[j] * pSrc[j] // 正弦计算的两个值与输入首尾的两个值首尾相乘然后相加所得
    }
    pDst[n] = sum
  }
  return pDst
}

// 根据坐标寻找当前频率
export function indexToPlot(val, model = {}) {
  const { resultLen = 1000 } = model
  let left = null
  let num = null
  if (typeof (model.centerFreq) === 'number') {
    left = model.centerFreq - model.bandwidth / 2
    num = (val / resultLen) * model.bandwidth + left
  } else {
    left = plotToNum(model.centerFreq + model.centerFreqUnit) - plotToNum(model.bandwidth + model.bandwidthUnit) / 2
    num = (val / resultLen) * plotToNum(model.bandwidth + model.bandwidthUnit) + left
  }
  // const left = model.centerFreq - model.bandwidth / 2
  // const num = (val / resultLen) * model.bandwidth + left
  return numToPlot(num)
}

/**
 * @description 处理中心频率和信号带宽
 * @param {*} data
 * @returns
 */
export function interfaceUnitConversion(data) {
  if (/[a-zA-Z]/.test(data)) {
    return data
  } else {
    if (data / 1000 >= 0 && data / 1000 < 1000) {
      return data / 1000 + 'KHz'
    } else if (data / 1000000 > 0 && data / 1000000 < 1000) {
      return data / 1000000 + 'MHz'
    } else if (data / 1000000000 > 0) {
      return data / 1000000000 + 'GHz'
    }
    return data
  }
}

/**
 * @description 处理时间戳
 * @param {number} timestamp_ms 时间戳
*/
export function convertTimestampToDateTimeString(timestamp_ms) {
  // 检查时间戳是否为空
  if (!timestamp_ms) {
    return '--'
  }

  // 将时间戳转换为日期对象
  const date = new Date(timestamp_ms);

  // 格式化日期时间字符串
  const date_time_str = date.toISOString().slice(0, 19).replace('T', ' ');

  return date_time_str;
}


// 根据映射表生成中文标题的Excel
export function exportToExcelWithCustomHeaders(data, headerMapping, filename) {
  // 检查文件名是否带有 .xlsx 后缀
  if (!filename.endsWith(".xlsx")) {
    filename += ".xlsx";  // 如果没有后缀，添加 .xlsx
  }

  // 提取中文标题
  const headers = Object.values(headerMapping);

  // 生成数据数组，并使用字段映射表替换字段名为中文标题
  const processedData = data.map(item => {
    const newItem = {};
    for (const key in headerMapping) {
      newItem[headerMapping[key]] = item[key]; // 替换字段名为中文标题
    }
    return newItem;
  });

  // 生成 Excel sheet
  const ws = XLSX.utils.json_to_sheet(processedData, { header: headers });

  // 创建工作簿
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "Sheet1");

  // 导出 Excel 文件，确保文件名带有 .xlsx 后缀
  XLSX.writeFile(wb, filename);
}