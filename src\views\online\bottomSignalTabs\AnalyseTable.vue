<script setup>
  import { numToPlot, convertTimestampToDateTimeString } from '@/utils/utils'
  import { round } from 'lodash'

  const props = defineProps({
    tableData: {
      type: Array,
      default: () => []
    }
  })
  const emit = defineEmits(['pick', 'position'])

  /**
   * 根据传入的行数据，跳转到信号定位TDOA页面
   * @param row 行数据
   */
  const position = row => {
    emit('position', row)
  }
</script>
<template>
  <el-table class="ml-2 max-h-[270px] h-[270px]" border :height="270" :data="tableData">
    <el-table-column label="序号" type="index" width="60" align="center" />
    <el-table-column label="中心频率" prop="signalCenterFrequency" width="200" align="center">
      <template #default="{ row }">
        {{ numToPlot(row.signalCenterFrequency, '', 6) }}
      </template>
    </el-table-column>
    <el-table-column label="带宽" prop="signalBandwidth" align="center">
      <template #default="{ row }">
        {{ numToPlot(row.signalBandwidth) }}
      </template>
    </el-table-column>
    <el-table-column label="实时(dBm)" prop="signalLevel" align="center">
      <template #default="{ row }">
        {{ round(row.signalLevel, 3) }}
      </template>
    </el-table-column>
    <el-table-column label="信噪比(dBm)" prop="signalNoiseRatio" align="center">
      <template #default="{ row }">
        {{ round(row.signalNoiseRatio, 3) }}
      </template>
    </el-table-column>
    <el-table-column label="最大(dBm)" prop="signalLevelMax" align="center">
      <template #default="{ row }">
        {{ round(row.signalLevelMax, 3) }}
      </template>
    </el-table-column>
    <el-table-column label="最小(dBm)" prop="signalLevelMin" align="center">
      <template #default="{ row }">
        {{ round(row.signalLevelMin, 3) }}
      </template>
    </el-table-column>
    <el-table-column label="首次发现时间" prop="firstTime" align="center">
      <template #default="{ row }">
        {{ convertTimestampToDateTimeString(row.firstTime) }}
      </template>
    </el-table-column>
    <el-table-column label="最新更新时间" prop="lastUpdateTime" align="center">
      <template #default="{ row }">
        {{ convertTimestampToDateTimeString(row.lastUpdateTime) }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="120" align="center">
      <template #default="{ row }">
        <el-button size="small" @click="emit('pick', row)">监测</el-button>
        <el-button size="small" @click="position(row)">定位</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>
