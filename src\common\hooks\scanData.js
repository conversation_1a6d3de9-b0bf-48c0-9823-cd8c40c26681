import { computed, ref, watch } from 'vue'

export default function () {
  const scanData = ref(null)
  const dataList = computed(() => {
    if (!scanData.value) {
      return {
        current: [],
        average: [],
        max: [],
        min: [],
        limit: []
      }
    }
    const data = scanData.value.subtaskResultBody
    return {
      current: data.fieldStrength,
      average: data.average,
      max: data.maximum,
      min: data.minimum,
      limit: data.threshold
    }
  })
  const signalList = computed(() => {
    if (!scanData.value) {
      return []
    }
    const { signalCount, signalCenterFrequency, signalBandwidth, signalFieldStrength } =
      scanData.value.subtaskResultBody
    const result = []
    for (let i = 0; i < signalCount; i++) {
      const centerFreIn = signalCenterFrequency[i]
      const bandWidth = signalBandwidth[i]
      const current = signalFieldStrength[i]
      result.push({ centerFreIn, bandWidth, current })
    }
    return result
  })

  return {
    scanData,
    dataList,
    signalList
  }
}
