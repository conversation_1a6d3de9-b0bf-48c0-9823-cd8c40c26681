<template>
  <el-col :span="4">
    <div class="ptitle">课程信息</div>
    <el-tree
      ref="deptTreeRef"
      :data="deptOptions"
      node-key="id"
      :expand-on-click-node="false"
      :current-node-key="selectedKey"
      highlight-current
      default-expand-all
      @node-click="handleNodeClick"
    />
  </el-col>
</template>
<script>
  import { listCourse } from '@/api/business/course'
  export default {
    name: 'ChapterLeftTree',
    emits: ['change'],
    data() {
      return {
        selectedKey: '',
        deptOptions: []
      }
    },
    created() {
      this.getCourList()
    },
    methods: {
      demo() {}
    },
    methods: {
      getCourList() {
        listCourse({ pageSize: 100, pageNum: 1 }).then(res => {
          let children = res.rows.map(v => {
            return {
              label: v.courName,
              id: v.courId
            }
          })
          this.deptOptions = children
          // this.deptOptions.unshift({ label: '全部', id: '', children })
          this.selectedKey = this.deptOptions[0]?.id
          this.$emit('change', this.selectedKey)
        })
      },
      handleNodeClick(data) {
        this.$emit('change', data.id)
      }
    }
  }
</script>
<style lang="less"></style>
