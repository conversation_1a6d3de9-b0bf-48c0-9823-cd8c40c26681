<template>
  <div key="online-container" class="online-container app-container">
    <el-row key="online-main" class="main">
      <el-col :span="17" class="chart-info">
        <ScanSection>
          <!--地图  -->
          <div class="charts w-full h-[760px]">
            <Map :fixed-data="fixedData" :dynamic-data="dynamicData" />
          </div>
          <!-- 信号、采集、回放功能按钮模块 -->

          <div class="tabs w-full flex">
            <Spectrum
              class="w-2/5"
              ref="spectrumRef"
              key="position"
              usekey="position"
              :show-lines="checkList"
              :selected-fremarkers="selectedFreStds"
              :is-active="isActive"
              :is-top-follow="isTopFollow"
              :data-list="dataList"
              :model="spectrum"
            />
            <BottomSignalTabs
              class="w-3/5"
              :signal-list="signalScanList"
              :position-list="positionList"
            />
          </div>
        </ScanSection>
      </el-col>
      <el-col :span="7" class="pl-[10px]">
        <ScanSection class="charts">
          <div class="form w-full h-full">
            <!-- 右侧参数设置模块 -->
            <RightSettings
              :status="isConnected"
              :model="spectrum"
              @start="startPosWS"
              @stop="closePosWS"
            >
              <!-- 定位信号参数  -->
              <template #signal>
                <positionSignal
                  ref="positionSignalRef"
                  v-model:positionSignalForm="positionSignalForm"
                />
              </template>
              <!-- 定位设备列表 -->
              <template #equipment>
                <TrackEquip ref="trackEquipRef" :equip-list="equipList" />
              </template>
            </RightSettings>
          </div>
        </ScanSection>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Scan">
  import ScanSection from '@/components/SpectrumSignal/ScanSection.vue'
  import Map from '../components/map.vue'
  // import Map from '../components/map-bd-tran.vue'
  // import Map from '../components/map-bd-notran.vue'
  import RightSettings from './components/SettingsForm/index.vue'
  import TrackEquip from './components/trackEquip/index.vue'
  import positionSignal from './rightSettings/positionSignal.vue'
  import BottomSignalTabs from './bottomSignalTabs/index.vue'
  import Spectrum from '@/components/SpectrumSignal/Spectrum.vue'
  import useScanStore from '@/store/modules/scanMonitor'
  import { ElMessage } from 'element-plus'
  import { ref } from 'vue'
  import { TDOA_IQ_CODE } from '@/constant/funCodes'
  import { linkWs } from '@/api/upperComputer/scan'
  import { getEquipList } from '@/api/system/equipment'
  import { plotToNum } from '@/utils/utils'
  import { compact } from 'lodash'

  const checkList = ref(['实时', '平均', '最大', '最小', '门限'])
  const spectrumRef = ref(null)
  const selectedFreStds = ref([])
  const isActive = ref(true)
  const isTopFollow = ref(false)
  const dataList = ref({
    current: [],
    average: [],
    max: [],
    min: [],
    limit: [],
    occupancy: []
  })

  const route = useRoute()
  const scanStore = useScanStore()
  const spectrum = scanStore.spectrum

  // 设备列表查询参数
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10
  })

  const equipList = ref([]) //设备列表
  const fixedData = ref([]) // 固定定位点坐标

  // 定位信号表单
  const positionSignalForm = ref({
    centerFreq: 300000000,
    bandwidth: 80000000,
    bandwidthOther: 0,
    sampleRate: 0,
    samplingRate: 50000000,
    samplingInterval: 500,
    startTime: 10,
    updateCycle: '1',
    triggerMode: '0',
    amplitudeThreshold: 0,
    collectData: '1',
    singleSamplingTime: 2000,
    fftPoints: '0',
    reportStr: ''
  })

  watch(
    positionSignalForm,
    newVal => {
      spectrum.bandwidth = newVal.bandwidth
      spectrum.centerFreq = newVal.centerFreq
      console.log(spectrum)
    },
    { deep: true, immediate: true }
  )

  const positionSignalRef = ref(null) //定位信号参数ref
  const trackEquipRef = ref(null) //定位设备ref
  const ws = ref(null) // 定位websocket
  const isConnected = ref(false) //定位websocket是否连接

  const getList = async () => {
    await getEquipList(queryParams).then(response => {
      equipList.value = response.data.list
      fixedData.value = []
      equipList.value.forEach(item => {
        fixedData.value.push({
          name: item.name,
          value: [Number(item.longitude), Number(item.latitude)]
        })
      })
    })
  }

  const signalScanList = ref([]) //信号列表

  const connectWS = async () => {
    positionList.value = []
    // 获取启用设备的设备编码
    const codeList = trackEquipRef.value.dealCode()
    // 获取常规分析跳转定位的扫描参数
    const scanParams = JSON.parse(localStorage.getItem('scanParams') || '{}')
    // 获取主机信息
    const hostItem = equipList.value.find(item => item.type === 0)
    // console.log(hostItem)
    // 主机deviceCode判空处理（老数据没有deviceCode属性）
    let paraCode = hostItem.devicePara?.deviceCode
    let scanCode = hostItem.deviceScan?.deviceCode
    hostItem.devicePara.deviceCode = paraCode ? paraCode : hostItem.code
    hostItem.deviceScan.deviceCode = scanCode ? scanCode : hostItem.code
    // console.log(hostItem)
    // 处理上报信号数组
    const reportedSignalNumsList = compact(positionSignalForm.value.reportStr.split('\n')).map(
      item => {
        const [centerFreq, bandwidth] = item.split('|')
        return {
          startChannel: plotToNum(centerFreq),
          effectiveChannelWidth: plotToNum(bandwidth)
        }
      }
    )
    // 处理TDOA指令请求参数
    const requestData = {
      polarization: scanParams.polarization,
      sampleStartTime: positionSignalForm.value.startTime,
      sampleIntervalDuration: positionSignalForm.value.samplingInterval,
      centerFrequency: plotToNum(
        positionSignalForm.value.centerFreq + positionSignalRef.value.selectForm.centerFreq
      ),
      sampleBw: positionSignalForm.value.bandwidth,
      sampleRate: positionSignalForm.value?.sampleRate,
      collectDatatype: positionSignalForm.value.collectData,
      collectTriggerType: positionSignalForm.value.triggerMode,
      amplitudeTriggerThreshold: positionSignalForm.value.amplitudeThreshold,
      singleSamplingTime: positionSignalForm.value.singleSamplingTime,
      fftPoint: positionSignalForm.value.fftPoints,
      reportedSignalNums: positionSignalForm.value.reportStr.split(',').length,
      reportedSignalNumsList: reportedSignalNumsList
    }
    if (ws.value) {
      ws.value.close()
    }
    let deviceScanData = {}
    if (JSON.stringify(scanParams) === '{}') {
      deviceScanData = hostItem.deviceScan
    } else {
      deviceScanData = scanParams
    }

    // 连接websocket
    ws.value = await linkWs({
      host: hostItem.ip,
      port: hostItem.port,
      taskFunCode: TDOA_IQ_CODE,
      sendTime: Number(positionSignalForm.value.updateCycle) * 1000,
      deviceCodeList: codeList,
      devicePara: encodeURIComponent(JSON.stringify(hostItem.devicePara)),
      deviceScan: encodeURIComponent(JSON.stringify(deviceScanData)),
      data: encodeURIComponent(JSON.stringify(requestData))
    })
    isConnected.value = true
    ws.value.onmessage = event => {
      try {
        const { data } = JSON.parse(event.data)
        const dataToJson = data?.result?.locationResult
        const newData = dataToJson?.tdoa_OUT || [] // 假设这是从服务器获取的新数据数组
        updatePositionList(dataToJson) // 更新定位数据列表
        // 遍历每个新接收的数据项
        updateEquipList(dataToJson.deviceList)
        // console.log(newData, 'Received new data')
        updateChartData(dataToJson)
        updateLine(newData)
        // console.log(data, 'onmessage Info')
      } catch (err) {
        console.log(err)
      }
    }
  }

  /**
   * 更新线路数据
   * @param trackData 线路数据数组
   */
  const updateLine = trackData => {
    trackData.forEach(item => {
      let isMatched = false
      // 检查新数据项是否与已存在的数据项匹配
      for (const key in dynamicData.value) {
        const data = dynamicData.value[key]
        const freqRange = data.bandwidth / 2 // 计算频率的范围
        // 检查频率是否匹配
        if (
          plotToNum(spectrum.centerFreq + spectrum.centerFreqUnit) >= data.frequency - freqRange &&
          plotToNum(spectrum.centerFreq + spectrum.centerFreqUnit) <= data.frequency + freqRange
        ) {
          // 如果匹配，将新的位置添加到对应的数据项中
          data.positions.push([item.dlon_out, item.dlat_out])
          isMatched = true
          break
        }
      }
      // 如果没有匹配的现有数据项，创建一个新的
      if (!isMatched) {
        const frequency = plotToNum(spectrum.centerFreq + spectrum.centerFreqUnit)
        const bandwidth = positionSignalForm.value.bandwidthOther
          ? positionSignalForm.value.bandwidthOther
          : spectrum.bandwidth
        const newKey = `${frequency}_${bandwidth}`
        dynamicData.value[newKey] = {
          frequency: frequency,
          bandwidth: bandwidth,
          positions: [[item.dlon_out, item.dlat_out]]
        }
      }
    })
  }

  /** 开启定位websocket逻辑 */
  const startPosWS = async () => {
    if (positionSignalForm.value.startTime < 5) {
      ElMessage.error('起始时间不能小于5s')
      return
    }
    await connectWS()
  }

  /** 关闭定位websocket */
  const closePosWS = () => {
    console.log('关闭定位')
    if (ws.value) {
      ws.value.close()
      ws.value = null
      isConnected.value = false
    }
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }

  let timer = null
  const positionList = ref([]) // 存储定位数据
  const dynamicData = ref({}) // 存储归类后的数据

  /**
   * 更新图表数据
   * @param data 包含新数据的对象
   * @param data.ddataout 图表数据数组，如果为空则使用空数组代替
   */
  const updateChartData = data => {
    if (!data || !data.ddataout) return
    dataList.value.current = data.ddataout || []
  }

  /**
   * 更新位置列表
   * @param newData 新数据
   */
  const updatePositionList = newData => {
    if (!newData || !newData.tdoa_OUT) return
    newData.tdoa_OUT.forEach(element => {
      element.lfreq = plotToNum(spectrum.centerFreq + spectrum.centerFreqUnit)
      element.lbw = positionSignalForm.value.bandwidthOther
        ? positionSignalForm.value.bandwidthOther
        : spectrum.bandwidth
      element.locationDate = newData.locationDate
    })
    positionList.value.push(...newData.tdoa_OUT)
  }

  /** 更新设备列表 */
  const updateEquipList = incomingData => {
    if (!incomingData) return
    // 遍历传入的数据
    incomingData.forEach(incomingItem => {
      // 在equipList中查找code相同的项
      let foundItem = equipList.value.find(equipItem => equipItem.code === incomingItem.code)
      // 如果找到了相同code的项，则更新其坐标
      if (foundItem) {
        foundItem.longitude = incomingItem.longitude
        foundItem.latitude = incomingItem.latitude
      }
    })
    // 同步更新fixedData
    equipList.value.forEach(item => {
      // 查找fixedData中name相同的项
      let fixedItem = fixedData.value.find(fixed => fixed.name === item.name)
      if (fixedItem) {
        // 如果找到了相同name的项，则更新其value
        fixedItem.value = [item.longitude, item.latitude]
      } else {
        // 如果没有找到，则添加新的项
        fixedData.value.push({
          name: item.name,
          value: [item.longitude, item.latitude]
        })
      }
    })
  }

  onMounted(async () => {
    await getList()
    // 频谱扫描跳转 —— 判断路由参数是否包含定位信号（自启动websocket）
    if (route.query.centerFreq) {
      positionSignalForm.value.centerFreq = Number(route.query.centerFreq)
      positionSignalForm.value.bandwidth = positionSignalForm.value.bandwidthOther = Number(
        route.query.bandwidth
      )
      positionSignalForm.value.singleSamplingTime = Number(route.query.singleSamplingTime)
      const signalList = JSON.parse(route.query.signalList)
      signalScanList.value.push(signalList)
      positionSignalRef.value.dataChangeFun()
    }
    // 常规分析跳转 —— 从路由参数中获取定位信号参数
    else if (route.query.signalList) {
      const signalList = JSON.parse(route.query.signalList)
      positionSignalForm.value.centerFreq = Number(signalList.signalCenterFrequency)
      positionSignalForm.value.bandwidthOther = Number(signalList.signalBandwidth)
      signalScanList.value.push(signalList)
      positionSignalRef.value.dataChangeFun()
    } else {
      signalScanList.value = []
    }
  })

  onBeforeUnmount(() => {
    closePosWS()
  })
</script>

<style scoped lang="scss">
  @import '@/assets/styles/mixin.scss';

  .online-container {
    @include boxBg('@/assets/images/specscanbg.png');
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 1rem;
    overflow-y: auto;
    .main {
      height: 100%;
    }
  }
</style>
