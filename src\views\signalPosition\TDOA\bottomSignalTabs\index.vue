<template>
  <el-tabs class="mt-6">
    <el-tab-pane>
      <template #label>
        <el-button class="tabs-button"> 信号 </el-button>
      </template>
      <AnalyseTable :table-data="tableData" @pick="pick" />
      <div v-if="tableData.length > 0" class="scantabs">
        <div
          v-for="(item, index) in spectrumList"
          :key="item.title"
          :class="{ active: activeKey === index }"
          class="st-item"
          @click="tabClick(index)"
        >
          {{ item.title }}
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane>
      <template #label>
        <el-button class="tabs-button"> 定位轨迹 </el-button>
      </template>
      <TrackTable :position-list="positionList" />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup name="BottomSignalTabs">
  import AnalyseTable from './AnalyseTable.vue'
  import TrackTable from './Track.vue'
  import useScanStore from '@/store/modules/scanMonitor'
  import { numToPlot } from '@/utils/utils'
  import { ElMessage } from 'element-plus'
  import { useRouter } from 'vue-router'
  const router = useRouter()

  const props = defineProps({
    signalList: {
      type: Array,
      default: () => []
    },
    positionList: {
      type: Array,
      default: () => []
    }
  })
  const activeKey = ref(0)
  const { spectrum, monitor } = useScanStore()
  const spectrumList = computed(() => {
    const signalList = props.signalList
    const len = signalList.length
    signalList.sort((a, b) => a.signalCenterFrequency - b.signalCenterFrequency)
    const itemNum = Math.ceil(signalList.length / spectrum.freNum)
    const list = []
    let temp = []
    for (let i = 0; i < len; i++) {
      temp.push(signalList[i])
      if (temp.length === itemNum || i === len - 1) {
        const start = temp[0]
        const end = temp[temp.length - 1]
        const title = generateTabTitle(start, end)
        list.push({ title, body: temp })
        temp = []
      }
    }
    return list
  })
  const tableData = computed(() => {
    return spectrumList.value[activeKey.value]?.body || []
  })
  const generateTabTitle = (start, end) => {
    const diff = end.signalCenterFrequency - start.signalCenterFrequency
    return `${numToPlot(start.signalCenterFrequency, '', 0)} - ${numToPlot(
      end.signalCenterFrequency,
      '',
      0
    )}(${numToPlot(diff, '', 1)})`
  }
  const tabClick = index => {
    activeKey.value = index
  }

  const pick = row => {
    ElMessage({
      message: '定位',
      type: 'warning'
    })
  }
</script>
