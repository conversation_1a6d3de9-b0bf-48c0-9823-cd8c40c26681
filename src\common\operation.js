import * as NAME_SETS from '@/constant/chartConsts'
import useChartsStore from '@/store/modules/charts'
import ci from '@/common/chartInstances'

const updateTypes = {
  refLevel: (instances, val) => {
    const { SQUENTIAL, SPECTRUM, AM_VS_TIME } = NAME_SETS
    const keys = [SQUENTIAL, SPECTRUM, AM_VS_TIME]
    for (const item of instances) {
      if (keys.includes(item.name)) {
        item.instance.update(
          {
            yAxis: {
              min: val * 1 - 100,
              max: val
            }
          },
          true
        )
      }
    }
  }
  // centerFreqIn: (instances, value) => {
  //   const { SPECTRUM } = NAME_SETS
  //   const { global } = useChartsStore()
  //   const spectrumChart = instances.find(item => item.name === SPECTRUM)
  //   if (!spectrumChart || spectrumChart.destroyed) {
  //     return
  //   }
  //   spectrumChart.instance.update({
  //     xAxis: {
  //       min: value - global.intermediateFrequencyBandwidth,
  //       max: value + global.intermediateFrequencyBandwidth
  //     }
  //   })
  // }
}

export default {
  updateOptions(key, value) {
    if (!updateTypes[key]) {
      return
    }
    const instances = ci.get()
    updateTypes[key](instances, value)
  }
}
