<template>
  <div class="app-container">
    <div class="ptitle">信号模板</div>
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="模板名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入模板名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:export']"
          type="warning"
          plain
          icon="upload"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:edit']"
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
      <!-- <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar> -->
    </el-row>

    <el-table
      v-loading="loading"
      class="fit-table"
      :data="signalList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="模板名称" align="center" prop="name" />
      <el-table-column label="中心频率（MHz）" align="center" prop="centerFreq">
        <template #default="scope">
          {{ scope.row.centerFreq / 1000000 }}
        </template>
      </el-table-column>

      <el-table-column label="信号带宽（KHz）" align="center" prop="signalBandwidth">
        <template #default="scope">
          {{ scope.row.signalBandwidth / 1000 }}
        </template>
      </el-table-column>
      <el-table-column label="步长" align="center" prop="step" />
      <el-table-column label="模板文件" align="center" prop="fileName" />
      <el-table-column label="操作" fixed="right">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:post:edit']"
            link
            icon="Edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['system:post:remove']"
            link
            icon="Delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改信号模板对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="signalRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="中心频率" prop="centerFreq">
          <el-input
            v-model="form.centerFreq"
            placeholder="请输入中心频率"
            maxlength="20"
            @input="
              form.centerFreq = form.centerFreq.replace(
                /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                ''
              )
            "
          />
        </el-form-item>
        <el-form-item label="信号带宽" prop="signalBandwidth">
          <el-input v-model="form.signalBandwidth" placeholder="请输入信号带宽" />
        </el-form-item>
        <el-form-item label="步长" prop="step">
          <el-input v-model="form.step" placeholder="请输入信号步长" />
        </el-form-item>
        <el-form-item label="模板文件" prop="fileName">
          <el-input v-model="form.fileName" disabled placeholder="请上传模板文件" />
          <div class="flex w-full justify-end gap-x-1 mt-2">
            <!-- <el-button @click="upLoad" :disable="true">上传</el-button> -->
            <el-button @click="selectFrom">从文件库中选择</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>

  <FileManager v-model="tempOpen" @selectFile="selectFile" />
</template>

<script setup name="Post">
  import {
    getSignalTempList,
    delSignalTemp,
    addOrUpdateSignalTemp,
    getSignalTempDetail,
    batchDelSignalTemp
  } from '@/api/warning/signalTemp'
  import { interfaceUnitConversion } from '@/utils/utils'
  import { VXETable } from 'vxe-table'
  import FileManager from './components/fileManage.vue'
  import { disable } from 'ol/rotationconstraint'

  const { proxy } = getCurrentInstance()

  /**信号模板列表 */
  const signalList = ref([])
  const open = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      name: ''
    },
    rules: {
      name: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
      centerFreq: [{ required: true, message: '中心频率不能为空', trigger: 'blur' }],
      signalBandwidth: [{ required: true, message: '信号带宽不能为空', trigger: 'blur' }],
      step: [{ required: true, message: '信号步长不能为空', trigger: 'blur' }],
      fileName: [{ required: true, message: '模板文件不能为空', trigger: 'blur' }]
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  const tempOpen = ref(false) // 模板弹窗

  /** 查询信号模板列表 */
  function getList() {
    loading.value = true
    getSignalTempList(queryParams.value).then(response => {
      signalList.value = response.data.list
      total.value = response.data.total || 0
      loading.value = false
    })
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false
    reset()
  }
  /** 表单重置 */
  function reset() {
    form.value = {
      id: undefined,
      centerFreq: undefined,
      name: undefined,
      signalBandwidth: undefined,
      fileId: undefined,
      fileName: undefined
    }
    proxy.resetForm('signalRef')
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    handleQuery()
  }
  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = []
    selection.forEach(item => {
      ids.value.push(item.id)
    })
    single.value = selection.length != 1
    multiple.value = !selection.length
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset()
    open.value = true
    title.value = '添加信号模板'
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset()
    const signalTempId = row.id || ids.value
    getSignalTempDetail(signalTempId).then(response => {
      form.value = response.data
      open.value = true
      title.value = '修改信号模板'
    })
  }
  /** 提交按钮 */
  const submitForm = async () => {
    proxy.$refs['signalRef'].validate(async valid => {
      if (valid) {
        const formData = new FormData()
        formData.append('name', form.value.name)
        formData.append('centerFreqStr', interfaceUnitConversion(form.value.centerFreq))
        formData.append('signalBandwidthStr', interfaceUnitConversion(form.value.signalBandwidth))
        formData.append('step', form.value.step)
        formData.append('fileId', form.value.fileId)
        formData.append('fileName', form.value.fileName)
        open.value = false
        if (form.value.id != undefined) {
          formData.append('id', form.value.id)
          await addOrUpdateSignalTemp(formData).then(response => {
            proxy.$modal.msgSuccess('修改成功')
            open.value = false
            getList()
          })
        } else {
          await addOrUpdateSignalTemp(formData).then(response => {
            proxy.$modal.msgSuccess('新增成功')
            open.value = false
            getList()
          })
        }
      }
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除')
      .then(function () {
        if (ids.value.length > 0) {
          return batchDelSignalTemp(ids.value)
        } else {
          return delSignalTemp({ id: row.id })
        }
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess('删除成功')
      })
      .catch(() => {})
  }
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      '/alert/signalTemp/export',
      {
        ...queryParams.value
      },
      `post_${new Date().getTime()}.xlsx`
    )
  }

  // const upLoad = async () => {
  //   try {
  //     const { file } = await VXETable.readFile()
  //     const reader = new FileReader()
  //     reader.readAsArrayBuffer(file)
  //     reader.onload = () => {
  //       const binaryStr = reader.result
  //       form.value.fileId = new Blob([binaryStr], { type: file.type })
  //       form.value.fileName = file.name
  //     }
  //     reader.onerror = error => {
  //       console.error('File reading error:', error)
  //     }
  //   } catch (e) {
  //     console.error('File upload error:', e)
  //   }
  // }

  const selectFrom = () => {
    tempOpen.value = true
  }

  const selectFile = data => {
    console.log(data)
    form.value.fileName = data.fileName
    form.value.fileId = data.id
  }

  getList()
</script>
