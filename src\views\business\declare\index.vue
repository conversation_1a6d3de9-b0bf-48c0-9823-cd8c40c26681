<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="项目申报名称" prop="prjName">
        <el-input
          v-model="queryParams.prjName"
          placeholder="请输入项目申报名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="项目类型" prop="prjType">
        <el-input
          v-model="queryParams.prjType"
          placeholder="请输入$项目类型"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:declare:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:declare:edit']"
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:declare:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:declare:export']"
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar v-model:show-search="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="declareList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="prjId" />
      <el-table-column label="项目申报名称" align="center" prop="prjName" />
      <el-table-column label="项目类型" align="center" prop="prjType" />
      <el-table-column label="项目介绍" align="center" prop="prjIntroduce" />

      <el-table-column label="操作" fixed="right">
        <template #default="scope">
          <el-button
            v-hasPermi="['business:declare:edit']"
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['business:declare:remove']"
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改项目申报信息对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目申报名称" prop="prjName">
          <el-input v-model="form.prjName" placeholder="请输入项目申报名称" />
        </el-form-item>
        <el-form-item label="项目类型" prop="prjType">
          <el-input v-model="form.prjType" placeholder="请输入项目类型" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import {
    listDeclare,
    getDeclare,
    delDeclare,
    addDeclare,
    updateDeclare
  } from '@/api/business/declare'

  export default {
    name: 'Declare',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 项目申报信息表格数据
        declareList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          prjName: null,
          prjType: null,
          prjIntroduce: null,
          prjCost: null,
          prjAchievement: null,
          declareBy: null,
          declareTime: null,
          approvalBy: null,
          approvalTime: null,
          approvalSchedule: null,
          status: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {}
      }
    },
    created() {
      this.getList()
    },
    methods: {
      /** 查询项目申报信息列表 */
      getList() {
        this.loading = true
        listDeclare(this.queryParams).then(response => {
          this.declareList = response.rows
          this.total = response.total
          this.loading = false
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          prjId: null,
          prjName: null,
          prjType: null,
          prjIntroduce: null,
          prjCost: null,
          prjAchievement: null,
          declareBy: null,
          declareTime: null,
          approvalBy: null,
          approvalTime: null,
          approvalSchedule: null,
          status: '0',
          delFlag: null,
          remark: null
        }
        this.resetForm('form')
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryForm')
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.prjId)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset()
        this.open = true
        this.title = '添加项目申报信息'
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset()
        const prjId = row.prjId || this.ids
        getDeclare(prjId).then(response => {
          this.form = response.data
          this.open = true
          this.title = '修改项目申报信息'
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.prjId != null) {
              updateDeclare(this.form).then(response => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
            } else {
              addDeclare(this.form).then(response => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const prjIds = row.prjId || this.ids
        this.$modal
          .confirm('是否确认删除项目申报信息编号为"' + prjIds + '"的数据项？')
          .then(function () {
            return delDeclare(prjIds)
          })
          .then(() => {
            this.getList()
            this.$modal.msgSuccess('删除成功')
          })
          .catch(() => {})
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download(
          'business/declare/export',
          {
            ...this.queryParams
          },
          `declare_${new Date().getTime()}.xlsx`
        )
      }
    }
  }
</script>
