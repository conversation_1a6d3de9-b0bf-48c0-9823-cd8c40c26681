<template>
  <div class="right-tool">
    <ul>
      <li>
        <el-tooltip class="box-item" content="标点" placement="left">
          <el-icon :size="30" @click="addPoint">
            <img src="@/assets/images/point.svg" alt="">
          </el-icon>
        </el-tooltip>
      </li>
      <hr>
      <li>
        <el-tooltip class="box-item" content="标线" placement="left">
          <el-icon :size="30" @click="addLine">
            <img src="@/assets/images/line.svg" alt="">
          </el-icon>
        </el-tooltip>
      </li>
      <hr>
      <li>
        <el-tooltip class="box-item" content="标面" placement="left">
          <el-icon :size="30" @click="addpolygon">
            <img src="@/assets/images/polygon.svg" alt="">
          </el-icon>
        </el-tooltip>
      </li>
      <hr>
      <li>
        <el-tooltip class="box-item" content="画圆" placement="left">
          <el-icon :size="30" @click="addCircle">
            <img src="@/assets/images/circle.svg" alt="">
          </el-icon>
        </el-tooltip>
      </li>
      <hr>
      <li>
        <el-tooltip class="box-item" content="画矩形" placement="left">
          <el-icon :size="30" @click="addRectangle">
            <img src="@/assets/images/rectangle.svg" alt="">
          </el-icon>
        </el-tooltip>
      </li>
      <hr>
      <li>
        <el-tooltip class="box-item" content="测距" placement="left">
          <el-icon :size="30" @click="computedDistance">
            <img src="@/assets/images/measure.svg" alt="">
          </el-icon>
        </el-tooltip>
      </li>
      <hr>
      <li>
        <el-tooltip class="box-item" content="取消绘制" placement="left">
          <el-icon :size="30" @click="deleteDraw">
            <img src="@/assets/images/cancal.svg" alt="">
          </el-icon>
        </el-tooltip>
      </li>
      <hr>
      <li>
        <el-tooltip class="box-item" content="清除" placement="left">
          <el-icon :size="30" @click="clearallMap">
            <img src="@/assets/images/trash.svg" alt="">
          </el-icon>
        </el-tooltip>
      </li>
    </ul>
  </div>
</template>
<script setup>
import {drawPoint, drawLine, drawPolygon, drawCircle, clearMap, distance, cancal, drawRectangle, cancalDraw } from '@/api/tool/mapDraw/index'
const props = defineProps({
  mapvalue:{
    type:Object
  }
})
let { mapvalue } = toRefs(props)
const addPoint = () => {
  drawPoint(mapvalue.value);
}
const addLine = () => {
  drawLine(mapvalue.value);
}
const addpolygon = () => {
  drawPolygon(mapvalue.value);
}
const addCircle = () => {
  drawCircle(mapvalue.value);
}
const addRectangle = () => {
  drawRectangle(mapvalue.value);
}
const clearallMap = () => {
  clearMap(mapvalue.value)
  cancal(mapvalue.value)
}
const deleteDraw = () => {
  cancalDraw(mapvalue.value)
}
const computedDistance = () => {
  distance(mapvalue.value)
}

</script>
<style scoped>
  .right-tool {
    position: absolute;
    top: 58px;
    right: 18px;
    width: 38px;
    height: 270px;
    z-index: 999;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 1px 1px 1px 1px rgba(0, 0, 0, 0.3);
    background: white;
  }
</style>
