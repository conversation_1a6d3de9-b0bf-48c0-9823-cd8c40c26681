@import "./variables.module.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./btn.scss";
@import "./charts.scss";
@import "./common.scss";
@import "./scan/reset.scss";
@import "./scan/form.scss";
body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  color: #454545;
}
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
ul,
ol,
dl {
  list-style: none;
}
label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.dark .switch__action {
  transform: translate(20px) !important;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

//main-container全局样式
// .app-container {
// }

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 16px;
  .el-pagination {
    justify-content: end;
  }
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

#app,
body,
html {
  height: 100%;
  overflow: hidden;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    "\5FAE\8F6F\96C5\9ED1",
    Arial,
    sans-serif;
  // font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei,
  //   '\5FAE\8F6F\96C5\9ED1', Arial, sans-serif;
  line-height: 1.5;
  color: var(--text-color);
  font-size: 14px;
  background-color: var(--el-bg-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  .vxe-table--render-default .vxe-table--body-wrapper,
  .vxe-table--render-default .vxe-table--footer-wrapper {
    background-color: var(--el-bg-color);
    color: var(--text-color);
  }
}

//重置样式
.anticon,
.el-icon {
  color: inherit;
  svg {
    display: inline-block;
    vertical-align: initial;
  }
}

a {
  color: #57a3f3;
  background: transparent;
  text-decoration: none;
  outline: none;
  cursor: pointer;
  transition: color 0.2s ease;
}

:focus-visible {
  outline: none;
}

a:active,
a:hover {
  outline-width: 0;
}

a:hover {
  color: #57a3f3;
}

a:active {
  color: #2b85e4;
}

a:active,
a:hover {
  outline: 0;
  text-decoration: none;
}

/* 滚动条凹槽的颜色，还可以设置边框属性 */
*::-webkit-scrollbar-track-piece {
  background-color: #f8f8f8;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

/* 滚动条的宽度 */
*::-webkit-scrollbar {
  width: 9px;
  height: 9px;
}

/* 滚动条的设置 */
*::-webkit-scrollbar-thumb {
  background-color: #ddd;
  background-clip: padding-box;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

/* 滚动条鼠标移上去 */
*::-webkit-scrollbar-thumb:hover {
  background-color: #bbb;
}

/* router view transition */
.zoom-fade-enter-active,
.zoom-fade-leave-active {
  transition:
    transform 0.35s,
    opacity 0.28s ease-in-out;
}

.zoom-fade-enter-from {
  opacity: 0;
  transform: scale(0.97);
}

.zoom-fade-leave-to {
  opacity: 0;
  transform: scale(1.03);
}

.form-header {
  font-size: 17px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  margin: 1em 0;
  font-weight: bold;
  padding-bottom: 0.5em;
  &:first-child {
    margin-top: 0;
  }
}

.el-button + .el-dropdown,
.el-dropdown + .el-button {
  margin-left: 12px;
}

.el-input__prefix-inner {
  align-items: center;
}

.page-container {
  width: 100%;
  border-radius: 4px;
  padding: 50px 0;
  height: 100vh;
  .text-center {
    h1 {
      color: #666;
      padding: 20px 0;
    }
  }
  img {
    width: 350px;
    margin: 0 auto;
  }
}
.el-drawer__header,
.ptitle,
.ftitle {
  border-bottom: 1px solid var(--el-border-color);
  padding-bottom: 0.5em;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  font-weight: bold;
  overflow: hidden;
  &::before {
    width: 5px;
    margin-right: 10px;
    background-color: var(--el-color-primary);
    content: "";
    border-radius: 3px;
    display: inline-block;
    vertical-align: middle;
    height: 1.1em;
  }

  .action {
    float: right;
    .el-radio-group,
    .el-button {
      vertical-align: top;
    }
  }
}
.el-drawer__header {
  margin-bottom: 1rem !important;
  &::before {
    top: 50%;
  }
}

.ftitle {
  font-size: 0.875rem;
  padding-bottom: 8px;
  margin-bottom: 1rem;
  line-height: 1.5rem;
  &::before {
    height: 0.4rem;
    width: 0.4rem;
    border-radius: 4px;
    top: 0.7rem;
  }
}

.el-link + .el-link,
.el-link + .el-dropdown {
  margin-left: 0.5rem;
}
.el-table {
  --el-table-header-background-color: var(--el-table-header-background);
  border-top: 1px solid var(--el-border-color) !important;
  &.el-table--border {
    border-bottom: 1px solid var(--el-border-color) !important;
    border-top: none !important;
  }

  th.el-table__cell {
    color: #4e5861;
  }

  .el-table__header-wrapper tr th.el-table-fixed-column--left,
  .el-table__header-wrapper tr th.el-table-fixed-column--right {
    background-color: var(--el-table-header-bg-color);
  }

  .el-input-number {
    width: auto;
  }
  .cell .el-form-item {
    margin-bottom: 0;
    &.is-error {
      margin-top: 1rem;
      margin-bottom: 1rem;
    }
  }
}

.el-button {
  .el-icon,
  .x-icon {
    color: var(--text-color);
  }
}

.el-button--primary,
.el-button--danger,
.el-button--info,
.el-button--success,
.el-button--warning,
.is-disabled {
  .el-icon,
  .x-icon {
    color: inherit;
  }
}

.hidden {
  display: none;
}
.input {
  width: 100%;
  height: 32px;
  border-radius: 4px;
  outline: 0;
  margin: 0 15px;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
  padding: 0 0.6em;
  color: #606266;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  box-shadow: none;
}
