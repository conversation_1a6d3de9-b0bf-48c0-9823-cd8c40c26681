.chart-bar {
  display: flex;
  justify-content: space-between;
  .chart-title {
    font-weight: 700;
    font-size: 16px;
  }
  .chart-tool {
    vertical-align: middle;
    .el-dropdown, .chart-tool-item {
      margin: 0 10px;
      &:last-of-type {
        margin-right: 0;
      }
    }
    .closeable {
      cursor: pointer;
      border: 1px solid $--color-info;
      margin-left: 20px;
      box-shadow: 1px;
    }
  }
}

.chart-item {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.drawer-panels {
  width: 300px !important;
}

.collapse-panels {
  width: 100%;

  .el-collapse-item__header {
    font-weight: 700;
    font-size: 16px;
  }
}

.panels-box {
  width: 500px;
  height: 100%;
  top: 0;
  overflow: auto;
  position: absolute;
  right: 0;
}