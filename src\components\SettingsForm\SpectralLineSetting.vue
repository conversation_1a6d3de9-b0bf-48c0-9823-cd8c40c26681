<template>
  <div>
    <el-form class="left">
      <el-form-item label="参考电平">
        <el-input v-model="spectrum.refLevel" type="number">
          <template #append>
            <span class="pl-1 pr-1">dBuv</span>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup>
  import useScanStore from '@/store/modules/scanMonitor'

  const spectrum = useScanStore().spectrum
</script>
<style scoped></style>
