<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="课程id" prop="stId">
        <el-input
          v-model="queryParams.stId"
          placeholder="请输入课程id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学号" prop="stuNo">
        <el-input
          v-model="queryParams.stuNo"
          placeholder="请输入学号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="参课时间" prop="inTime">
        <el-date-picker
          v-model="queryParams.inTime"
          clearable
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择参课时间"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="creatTime">
        <el-date-picker
          v-model="queryParams.creatTime"
          clearable
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择创建时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:attend:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:attend:edit']"
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:attend:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:attend:export']"
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar v-model:show-search="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="attendList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="attId" />
      <el-table-column label="课程id" align="center" prop="stId" />
      <el-table-column label="学号" align="center" prop="stuNo" />
      <el-table-column label="参课时间" align="center" prop="inTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(row.inTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="creatTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(row.creatTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" fixed="right">
        <template #default="scope">
          <el-button
            v-hasPermi="['business:attend:edit']"
            type="text"
            icon="Edit"
            @click="handleUpdate(row)"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['business:attend:remove']"
            type="text"
            icon="Delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改学生参课信息对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课程id" prop="stId">
          <el-input v-model="form.stId" placeholder="请输入课程id" />
        </el-form-item>
        <el-form-item label="学号" prop="stuNo">
          <el-input v-model="form.stuNo" placeholder="请输入学号" />
        </el-form-item>
        <el-form-item label="参课时间" prop="inTime">
          <el-date-picker
            v-model="form.inTime"
            clearable
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择参课时间"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="creatTime">
          <el-date-picker
            v-model="form.creatTime"
            clearable
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择创建时间"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { listAttend, getAttend, delAttend, addAttend, updateAttend } from '@/api/business/attend'

  export default {
    name: 'Attend',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 学生参课信息表格数据
        attendList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          stId: null,
          stuNo: null,
          inTime: null,
          creatTime: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {}
      }
    },
    created() {
      this.getList()
    },
    methods: {
      /** 查询学生参课信息列表 */
      getList() {
        this.loading = true
        listAttend(this.queryParams).then(response => {
          this.attendList = response.rows
          this.total = response.total
          this.loading = false
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          attId: null,
          stId: null,
          stuNo: null,
          inTime: null,
          creatTime: null,
          remark: null
        }
        this.resetForm('form')
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryForm')
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.attId)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset()
        this.open = true
        this.title = '添加学生参课信息'
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset()
        const attId = row.attId || this.ids
        getAttend(attId).then(response => {
          this.form = response.data
          this.open = true
          this.title = '修改学生参课信息'
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.attId != null) {
              updateAttend(this.form).then(response => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
            } else {
              addAttend(this.form).then(response => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const attIds = row.attId || this.ids
        this.$modal
          .confirm('是否确认删除学生参课信息编号为"' + attIds + '"的数据项？')
          .then(function () {
            return delAttend(attIds)
          })
          .then(() => {
            this.getList()
            this.$modal.msgSuccess('删除成功')
          })
          .catch(() => {})
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download(
          'business/attend/export',
          {
            ...this.queryParams
          },
          `attend_${new Date().getTime()}.xlsx`
        )
      }
    }
  }
</script>
