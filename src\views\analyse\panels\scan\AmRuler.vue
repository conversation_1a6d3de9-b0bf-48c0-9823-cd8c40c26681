<template>
  <data-form :fields="fields" :model="model" />
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData'
  import { rateUnits, levelUnits } from '@/constant/units'

  const fields = ref([
    {
      target: 'refLevel',
      name: '参考电平',
      options: levelUnits,
      appendSelect: true
    },
    {
      target: 'amScale',
      name: '刻度',
      appendText: 'dB'
    }
  ])

  const model = reactive(new ModelData(fields.value, 'scan'))
</script>
