<template>
  <div>
    <el-form class="left">
      <el-form-item label="中心频率">
        <el-input
          v-model="positionSignalForm.centerFreq"
          type="number"
          @change="dataChangeFun('centerFreq')"
        >
          <template #append>
            <el-select v-model="selectForm.centerFreq" @change="unitChange('centerFreq')">
              <el-option v-for="unit in units" :key="unit" :label="unit" :value="unit" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="定位带宽">
        <el-select v-model="positionSignalForm.bandwidth" class="w-full">
          <el-option
            v-for="item in positionBw"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="定位带宽">
        <el-input
          v-model="positionSignalForm.bandwidth"
          type="number"
          @change="dataChangeFun('bandwidth')"
        >
          <template #append>
            <el-select v-model="selectForm.bandwidth" @change="unitChange('bandwidth')">
              <el-option v-for="unit in units" :key="unit" :label="unit" :value="unit" />
            </el-select>
          </template>
        </el-input>
      </el-form-item> -->

      <!-- <el-form-item label="采样率">
        <el-input v-model="positionSignalForm.sampleRate" type="number" @change="dataChangeFun('startFre')">
          <template #append>
            <el-select v-model="selectForm.sampleRate" @change="unitChange('sampleRate')">
             <el-option
                v-for="unit in units"
                :key="unit.value"
                :label="unit.label"
                :value="unit.value"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item> -->

      <el-form-item label="采样间隔">
        <el-input v-model="positionSignalForm.samplingInterval" type="number">
          <template #append>
            <span class="pl-1 pr-1">ms</span>
          </template>
        </el-input>
      </el-form-item>

      <!-- <el-form-item label="起始时间">
        <el-input v-model="positionSignalForm.startTime" type="number">
          <template #append>
            <span class="pl-1 pr-1">秒后</span>
          </template>
        </el-input>
      </el-form-item> -->

      <el-form-item label="更新周期">
        <div class="flex w-full">
          <el-select v-model="positionSignalForm.updateCycle" class="w-full">
            <el-option
              v-for="item in update_cycle"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <div class="pl-1 pr-1 text-[#fff]">秒</div>
        </div>
      </el-form-item>

      <el-form-item label="触发方式">
        <el-select v-model="positionSignalForm.triggerMode" class="w-full" @change="triggerChange">
          <el-option
            v-for="item in trigger_method"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-if="positionSignalForm.triggerMode === '1'" label="幅度门限">
        <el-input v-model="positionSignalForm.amplitudeThreshold" type="number">
          <template #append>
            <span class="pl-1 pr-1">dBm</span>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="采集数据">
        <el-select
          v-model="positionSignalForm.collectData"
          class="w-full"
          @change="collectDataChange"
        >
          <el-option
            v-for="item in data_collection"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-if="positionSignalForm.collectData === '1'" label="单次采样时间">
        <el-input v-model="positionSignalForm.singleSamplingTime" type="number">
          <template #append>
            <span class="pl-1 pr-1">us</span>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item v-if="positionSignalForm.collectData === '2'" label="FFT点数">
        <el-select v-model="positionSignalForm.fftPoints" class="w-full">
          <el-option
            v-for="item in fft_point"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div v-if="positionSignalForm.collectData === '2'" class="px-4 flex">
      <div class="flex flex-col gap-3 w-[120px]">
        <span class="text-[#fff]">上报信号</span>
        <el-button class="w-10 ml-2" @click="addItem">添加</el-button>
        <span class="text-[#fff]">频率|带宽</span>
        <el-button class="w-10 ml-2" @click="clear">清空</el-button>
      </div>
      <div>
        <textarea
          v-model="positionSignalForm.reportStr"
          class="bg-[#01171e] text-[#fff] resize-none border border-solid border-[#00bfbf]"
          readonly
          rows="7"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
  import { numToPlot, plotToNum } from '@/utils/utils'
  import { ElMessage } from 'element-plus'
  import { ref } from 'vue'
  import { positionBw } from '@/constant/types'
  import useScanStore from '@/store/modules/scanMonitor'

  // 字典项
  const { proxy } = getCurrentInstance()
  const { trigger_method, data_collection, update_cycle, fft_point } = proxy.useDict(
    'trigger_method',
    'data_collection',
    'update_cycle',
    'fft_point'
  )
  const positionSignalForm = defineModel('positionSignalForm')

  const scanStore = useScanStore()
  const spectrum = scanStore.spectrum

  const selectForm = ref({
    centerFreq: 'Hz',
    bandwidth: 'Hz',
    startFreq: 'Hz',
    endFreq: 'Hz'
  })
  const units = ['GHz', 'MHz', 'KHz', 'Hz']

  const dataChangeFun = async key => {
    let formValue = {
      centerFreq: positionSignalForm.value.centerFreq
    }
    if (key) {
      const value = plotToNum(positionSignalForm.value[key] + selectForm.value[key])
      await nextTick()
      formatValue(key, value)
      linkage(key)
      return
    }
    Object.keys(selectForm.value).forEach(async key => {
      if (formValue[key]) formatValue(key, formValue[key])
      linkage(key)
      await nextTick()
    })
  }
  const unitChange = key => {
    dataChangeFun(key)
  }

  const linkage = key => {
    const freStore = spectrum
    // console.log(freStore)
    const startFreq = freStore.startFre
    const endFreq = freStore.endFre
    const bandwidth = freStore.bandwidth
    const centerFreq = plotToNum(freStore.centerFreq + selectForm.value.centerFreq)
    // console.log(centerFreq, '中心频率')
    // console.log(bandwidth, '带宽')
    // console.log(startFreq, '起始频率')
    // console.log(endFreq, '终止频率')

    if (key === 'centerFreq' || key === 'bandwidth') {
      freStore.startFre = centerFreq * 1 - (bandwidth / 2) * 1
      if (freStore.startFre < 20000000) {
        freStore.startFre = 20000000
        freStore.bandwidth = 2 * (freStore.centerFreq * 1 - freStore.startFre)
      }
      freStore.endFre =
        centerFreq * 1 + (bandwidth / 2) * 1 < 0 ? 0 : centerFreq * 1 + (bandwidth / 2) * 1
      formatValue('startFreq', freStore.startFre)
      formatValue('endFreq', freStore.endFre)
    }
  }

  const formatValue = (key, value) => {
    const newFormVal = numToPlot(value)
    // console.log(key, newFormVal)
    if (newFormVal.search('KHz') != -1) {
      positionSignalForm.value[key] = newFormVal.replace('KHz', '')
      spectrum[key] = newFormVal.replace('KHz', '')
      selectForm.value[key] = 'KHz'
    } else if (newFormVal.search('MHz') != -1) {
      positionSignalForm.value[key] = newFormVal.replace('MHz', '')
      spectrum[key] = newFormVal.replace('MHz', '')
      selectForm.value[key] = 'MHz'
    } else if (newFormVal.search('GHz') != -1) {
      positionSignalForm.value[key] = newFormVal.replace('GHz', '')
      spectrum[key] = newFormVal.replace('GHz', '')
      selectForm.value[key] = 'GHz'
    } else {
      positionSignalForm.value[key] = newFormVal.replace('Hz', '')
      spectrum[key] = newFormVal.replace('Hz', '')
      selectForm.value[key] = 'Hz'
    }
    spectrum[key + 'Unit'] = selectForm.value[key]
  }

  /** 触发方式变化函数 */
  const triggerChange = () => {
    positionSignalForm.value.amplitudeThreshold = 0
  }

  /** 收集数据变化函数 */
  const collectDataChange = () => {
    positionSignalForm.value.singleSamplingTime = 0
    positionSignalForm.value.fftPoints = '0'
    positionSignalForm.value.reportStr = ''
  }

  /** 添加频率项 */
  const addItem = () => {
    const centerFreq = positionSignalForm.value.centerFreq + selectForm.value.centerFreq
    const bandwidth = positionSignalForm.value.bandwidth + selectForm.value.bandwidth
    const itemStr = `${centerFreq}|${bandwidth}\n`
    if (positionSignalForm.value.reportStr.includes(itemStr)) {
      ElMessage({
        message: '该频率已存在',
        type: 'error'
      })
      return
    }
    positionSignalForm.value.reportStr += itemStr
  }

  /**
   * 清空函数
   * @description 清空 positionSignalForm.value.reportStr 的值
   */
  const clear = () => {
    positionSignalForm.value.reportStr = ''
  }

  onMounted(() => {
    dataChangeFun()
  })

  defineExpose({
    selectForm,
    dataChangeFun
  })
</script>
<style scoped></style>
