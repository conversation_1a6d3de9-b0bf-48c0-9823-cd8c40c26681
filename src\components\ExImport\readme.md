# 导入导出

## 导入

```js
<c-import ref="refsImport">
```

```js

this.$refs.refsImport.show({
  tmpUrl: '/basic-datacenter/enterpriseMaterial/importTemplate',//导入模板url,不传不显示下载模板按钮
  checkUrl: '/basic-datacenter/enterpriseMaterial/verifyImport',//校验上传文件接口，没有默认不传
  uploadUrl: '/basic-datacenter/enterpriseMaterial/verifyImportData',//上传接口
  ok: res => {
    //上传成功回调
    console.log('ok==', res)
  },
  fail: err => {
    //上传失败回调
  console.log('fail==', err)
  }
})
```

|参数|名称|类型|必填|默认值|
|-|-|-|-|-|
|title|导出弹窗title|string|false|上传文件|
|tmpName|导入模板名称|string|false|下载导入模板|
|tmpUrl|导入模板url|string|false||
|checkUrl|校验上传文件接口|string|false||
|uploadUrl|上传接口|string|true||
|ok|上传成功回调|function|false||
|fail|上传失败回调|function|false||
