import { computed } from 'vue';
import { usePlaybackStore } from '@/store/modules/playback';

export function usePlayback(pollingFunction) {
  const playbackStore = usePlaybackStore();

  // Computed properties for playback state
  const isPlaying = computed(() => playbackStore.isPlaying);
  const startOffset = computed(() => playbackStore.startOffset);
  const maxOffset = computed(() => playbackStore.maxOffset);
  const playBackTime = computed(() => playbackStore.playBackTime);
  const isRequestInProgress = computed(() => playbackStore.isRequestInProgress);
  const isFetching = computed(() => playbackStore.isFetching);
  const isFinished = computed(() => playbackStore.isFinished);
  const isInfinite = computed({
    get: () => playbackStore.isInfinite,
    set: (value) => {
      playbackStore.isInfinite = value;
    },
  });

  // Actions
  const togglePlay = () => {
    playbackStore.togglePlay(pollingFunction);
  };

  const play = () => {
    playbackStore.play(pollingFunction, playbackStore.startOffset);
  };

  const stopPlay = () => {
    playbackStore.stopPlay();
  };

  const fastBack = async () => {
    await playbackStore.fastBack(pollingFunction);
  };

  const backByStep = async () => {
    await playbackStore.backByStep(pollingFunction);
  };

  const forwardByStep = async () => {
    await playbackStore.forwardByStep(pollingFunction);
  };

  const refreshPlay = async () => {
    await playbackStore.refreshPlay(pollingFunction);
  };

  const setStartOffset = (value) => {
    playbackStore.setStartOffset(value);
  };

  const setMaxOffset = (value) => {
    playbackStore.setMaxOffset(value);
  };

  const clearTimer = () => {
    playbackStore.clearTimer();
  };

  return {
    isPlaying,
    startOffset,
    maxOffset,
    playBackTime,
    isRequestInProgress,
    isFetching,
    isFinished,
    isInfinite,
    togglePlay,
    play,
    stopPlay,
    fastBack,
    backByStep,
    forwardByStep,
    refreshPlay,
    setStartOffset,
    setMaxOffset,
    clearTimer,
  };
}
