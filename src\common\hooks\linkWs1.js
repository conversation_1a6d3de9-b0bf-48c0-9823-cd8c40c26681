import { ref, onActivated, onDeactivated } from 'vue';
import { linkWs } from '@/api/upperComputer/scan';
import { DISPERSED_CODE, SIGNAL_MONITOR_CODE } from '@/constant/funCodes';
import useScanStore from '@/store/modules/scanMonitor';
import { settings } from '@/utils/settings';

export default function (spectrum, taskFunCode) {
  let ws = null;
  const pageData = ref(null);
  const processedData = ref(null);
  console.log(import.meta.env.VITE_GLOB_APP_BASE_API);

  // 初始化 Web Worker
  const worker = new Worker(new URL('@/workers/dataWorker.js', import.meta.url));

  worker.onmessage = (e) => {
    processedData.value = e.data;
  };

  /**
   * 扫描链接并返回扫描结果
   * @param params 扫描参数
   * @returns 扫描结果
   */
  const linkScan = async (params, host, port, deviceCode) => {
    useScanStore().playFfts = [];
    if (ws) {
      ws.close();
    }
    ws = await linkWs({
      host: host || settings.VITE_GLOB_HOST,
      port: port || settings.VITE_GLOB_PORT,
      taskFunCode: taskFunCode.value,
      sendTime: 10,
      deviceCode: deviceCode,
      data: encodeURIComponent(JSON.stringify(params))
    });
    spectrum.status = 1;
    ws.onmessage = event => {
      try {
        const { data } = JSON.parse(event.data);
        pageData.value = data.result;

        // 发送数据到 Web Worker 处理
        worker.postMessage({ spectrum: spectrum, rawData: data.result, taskFunCode: taskFunCode.value });
      } catch (err) {
        console.log(err);
      }
    };
    ws.onclose = function (event) {
      console.log("连接已关闭", event);
      spectrum.status = 0;
    };

    ws.onerror = function (event) {
      console.log("WebSocket 错误", event);
    };
  };

  const closeScan = () => {
    if (ws) {
      ws.close();
      useScanStore().playFfts = [];
      useScanStore().playSignalList = [];
    }
  };


  onActivated(async () => {
    console.log(ws, 'linkWs  websocket');
    // if (ws) {
    //   await linkScan(paramsForm, hostRemember, portRemember, deviceCodeRemember)
    // }
  })
  onDeactivated(() => {
    if (ws) {
      ws.close();
      useScanStore().playFfts = [];
      useScanStore().playSignalList = [];
    }
  });

  return {
    linkScan,
    closeScan,
    pageData,
    processedData
  };
}
