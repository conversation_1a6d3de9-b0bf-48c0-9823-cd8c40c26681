<template>
  <data-chart
    v-if="model"
    :id="SQUENTIAL"
    title="时序图"
    :data="model.data"
    :config="model.config"
  />
</template>

<script setup>
  import DataChart from '@/components/DataChart'
  import useChartsStore from '@/store/modules/charts'
  import useChartOptions from '@/common/hooks/chartOptions'
  import { SQUENTIAL } from '@/constant/chartConsts'

  const chartsStore = useChartsStore()
  // 时序图
  const model = ref(null)
  // 请求图表数据
  const requestChartsData = async () => {
    const { data } = await chartsStore.querySequence()
    const { valueList } = data
    const { chartOptions } = useChartOptions('sequential', valueList.length - 1)
    generateModel(valueList, chartOptions)
  }
  // 生成模型（包含数据和图表配置等数据）
  const generateModel = (valueList, chartOptions) => {
    model.value = {
      title: '时序图',
      data: valueList,
      config: {
        type: 'static',
        chart: computed(() => chartOptions.value),
        viewNum: 'all'
      }
    }
  }

  onMounted(() => {
    requestChartsData()
  })
</script>
