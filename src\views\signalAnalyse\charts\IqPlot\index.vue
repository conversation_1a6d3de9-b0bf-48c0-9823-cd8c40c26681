<template>
  <div id="iqContainer" />
</template>

<script setup name="IqPlot">
  import Highcharts from '@/plugins/highcharts'
  import config from './config'
  import mockFn from './mock'

  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })

  let instance = null

  const updateData = data => {
    if (!instance) {
      return
    }
    const dataArr = Object.values(data)
    instance.series.map((s, i) => {
      s.setData(dataArr[i])
    })
  }
  onMounted(() => {
    instance = new Highcharts.Chart('iqContainer', config)
    // const data = [mockFn.getData(1000), mockFn.getData(1000)]
    // updateData(data)
  })

  watch(
    () => props.data,
    val => {
      if (val.i.length > 0) {
        updateData(val)
      }
    }
  )
</script>
