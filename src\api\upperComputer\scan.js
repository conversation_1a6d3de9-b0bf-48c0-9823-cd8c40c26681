import { settings } from '@/utils/settings'

// 建立ws链接通道
export function linkWs(params = {}) {
  let url = settings.VITE_GLOB_WS + '/websocket'
  Object.keys(params).forEach((key, index) => {
    if (index === 0) {
      url += '?'
    } else {
      url += '&'
    }
    url += `${key}=${params[key]}`
  })
  console.log('url ', decodeURIComponent(url))
  return new Promise(resolve => {
    const ws = new WebSocket(url)
    ws.onopen = () => {
      console.log('WebSocket 连接已建立！')
      resolve(ws)
    }
    // ws.onclose = () => {
    //   console.log('WebSocket 连接已关闭！')
    // }
  })
}
