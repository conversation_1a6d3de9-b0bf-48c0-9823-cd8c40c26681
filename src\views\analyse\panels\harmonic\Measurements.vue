<template>
  <data-form :model="model" :fields="fields" />
</template>

<script setup>

import DataForm from '@/components/DataForm'
import ModelData from '@/common/classes/modelData';

const measureTypes = [
  { label: '峰值', value: 0 },
  { label: '信道功率', value: 1 }
]
const traceTypes = [
  { label: '实时更新', value: 0 },
  { label: '最大保持', value: 1 }
]

const fields = [
  { target: 'num', name: "谐波数" },
  { target: 'measureType', name: "测量类型", options: measureTypes, isSelect: true },
  { target: 'traceType', name: "迹线类型", options: traceTypes, isSelect: true },
  { target: 'isPeakTrack', name: "峰值跟踪", isCheckbox: true },
]
const model = reactive(new ModelData(fields, 'harmonic'))
</script>
