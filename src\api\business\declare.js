import request from '@/utils/request'

// 查询项目申报信息列表
export function listDeclare(query) {
  return request({
    url: '/business/declare/list',
    method: 'get',
    params: query
  })
}

// 查询项目申报信息详细
export function getDeclare(prjId) {
  return request({
    url: '/business/declare/' + prjId,
    method: 'get'
  })
}

// 新增项目申报信息
export function addDeclare(data) {
  return request({
    url: '/business/declare',
    method: 'post',
    data: data
  })
}

// 修改项目申报信息
export function updateDeclare(data) {
  return request({
    url: '/business/declare',
    method: 'put',
    data: data
  })
}

// 删除项目申报信息
export function delDeclare(prjId) {
  return request({
    url: '/business/declare/' + prjId,
    method: 'delete'
  })
}
