// 文件类型与一字节对应几个比特值
export const FILE_TYPE_BITS = [
  { type: 'OQPSK', bits: 4 },
  { type: 'Pi/4 DQPSK', bits: 4 },
  { type: 'BPSK', bits: 2 },
  { type: 'D8PSK', bits: 8 },
  { type: 'QAM16', bits: 16 },
  { type: 'QAM32', bits: 32 },
  { type: 'QAM64', bits: 64 },
  { type: 'QAM256', bits: 256 },
  { type: 'QAM1024', bits: 1024 },
  { type: '2FSK', bits: 2 },
  { type: '4FSK', bits: 4 },
  { type: 'ASK', bits: 2 },
  { type: 'QPSK', bits: 4 },
  { type: 'BPSK', bits: 2 },
  { type: 'QBPSK', bits: 2 },
  { type: 'DBPSK', bits: 2 }
]

export const FILE_TYPES = [
  { label: 'OQPSK', value: '0' },
  { label: 'Pi/4 DQPSK', value: '1' },
  { label: '8PSK', value: '2' },
  { label: 'D8PSK', value: '3' },
  { label: 'QAM16', value: '4' },
  { label: 'QAM32', value: '5' },
  { label: 'QAM64', value: '6' },
  { label: 'QAM256', value: '7' },
  { label: 'QAM1024', value: '8' },
  { label: '2FSK', value: '9' },
  { label: '4FSK', value: '10' },
  { label: 'ASK', value: '11' },
  { label: 'QPSK', value: '12' },
  { label: 'BPSK', value: '13' },
  { label: 'QBPSK', value: '14' },
  { label: 'DBPSK', value: '15' }
]
