import ElementPlus from 'element-plus'
import 'element-plus/theme-chalk/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
// 分页组件
import Pagination from '@/components/Pagination'
//导入
import CImport from '@/components/ExImport/cimport.vue' //导入
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 文件上传组件
import FileUpload from '@/components/FileUpload'
// 图片上传组件
import ImageUpload from '@/components/ImageUpload'
// 图片预览组件
import ImagePreview from '@/components/ImagePreview'
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect'
// pdf预览
import PdfView from '@/components/PdfView'
// 字典标签组件
import DictTag from '@/components/DictTag'
import DictSelect from '@/components/DictSelect'
import CForm from '@/components/CForm'
import CTable from '@/components/TableForm'
import ActionBar from '@/components/TableForm/actions'
import CTag from '@/components/CTag'
import XIcon from '@/components/xicon/index'
import CuSelect from '@/components/CuSelect/index'
import FileListPreview from '@/components/FileListPreview/index'
import Editor from '@/components/Editor'
import Addr from '@/components/Addr/index.vue'
// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

export function regComponents(app) {
  // 全局组件挂载
  app.component('DictTag', DictTag)
  app.component('CImport', CImport)
  app.component('Pagination', Pagination)
  app.component('TreeSelect', TreeSelect)
  app.component('FileUpload', FileUpload)
  app.component('ImageUpload', ImageUpload)
  app.component('ImagePreview', ImagePreview)
  app.component('RightToolbar', RightToolbar)
  app.component('CForm', CForm)
  app.component('Addr', Addr)
  app.component('TableForm', CTable)
  app.component('CTag', CTag)
  app.component('ActionBar', ActionBar)
  app.component('DictSelect', DictSelect)
  app.component('XIcon', XIcon)
  app.component('CuSelect', CuSelect)
  app.component('FileListPreview', FileListPreview)
  app.component('SvgIcon', SvgIcon)
  app.component('PdfView', PdfView)
  app.component('Editor', Editor)
  app.use(ElementPlus)
  app.use(elementIcons)
}
