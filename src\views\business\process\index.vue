<template>
  <div class="app-container">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      @selectionChange="rows => (selection = rows)"
    />
    <DetailInfo ref="refsMt" @actSuccess="getList()" />
    <FileListPreview ref="refsFile" />
  </div>
</template>
<script>
  import * as mtApi from '@/api/business/process'
  import DetailInfo from './detail.vue'
  export default {
    name: 'ProcessList',
    components: { DetailInfo },
    props: {},
    data () {
      return {
        selection: [],
        tableFormConfig: {
          title: '实验进度',
          searchConfig: {
            formConfig: [
              {
                title: '实验项目',
                name: 'expId',
                type: 'cuselect',
                queryInfo: 'business/experiment/list?pageNum=1&pageSize=100',
                replaceFields: { label: 'expName', value: 'expId' }
              },
              { title: '进度名称', name: 'expProcess' }
            ],
            loadData: p => {
              return mtApi.listProcess(p)
            }
          },
          actions: [
            {
              label: '新增',
              type: 'primary',
              icon: 'el_Plus',
              permission: ['business:process:add'],
              click: () => {
                this.$refs.refsMt.show()
              }
            },
            {
              label: '导出',
              icon: 'el_upload',
              isPlain: true,
              type:'warning',
              permission: ['business:process:export'],
              click: row =>
                this.download(
                  'business/process/export',
                  {
                    // ...queryParams.value
                  },
                  `实验进度_${new Date().getTime()}.xlsx`
                )
            },
            {
              type: 'danger',
              label: '删除',
              permission: ['business:process:remove'],
              disabled: () => !this.selection.length,
              isPlain: true,
              icon: 'el_Delete',
              click: row => this.deleteAll()
            }
          ],
          tableConfig: {
            isChecked: true,
            cols: [
              {
                label: '实验项目',
                prop: 'expId',
                minWidth: 200,
                renderFun: (h, { row }) => {
                  return h('span', row.srExperiment?.expName)
                }
              },
              { label: '实验进度', prop: 'expProcess', minWidth: 200 },
              { label: '创建人', prop: 'creatBy' },
              { label: '创建时间', prop: 'creatTime' },
              {
                label: '操作',
                type: 'action',
                width: '220',
                actions: [
                  {
                    label: '编辑',
                    icon: 'el_Edit',
                    permission: ['business:process:edit'],
                    click: row => this.$refs.refsMt.show(row.objId)
                  },
                  {
                    label: '进度附件',
                    icon: 'el_Memo',
                    permission: ['business:experiment:files'],
                    click: row =>
                      mtApi.getProcess(row.objId).then(res => {
                        this.$refs.refsFile.show(res.data.srFileList || [])
                      })
                  },
                  {
                    label: '删除',
                    permission: ['business:process:remove'],
                    icon: 'el_Delete',
                    click: row => this.deleteAll(row.objId)
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList (data = null) {
        this.$refs.refsTableForm.loadData(data)
      },
      //批量删除模板
      deleteAll (id = null) {
        if (!this.selection.length && !id) return this.$modal.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选实验进度吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.objId)
          mtApi.delProcess(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.getList()
          })
        })
      }
    }
  }
</script>
