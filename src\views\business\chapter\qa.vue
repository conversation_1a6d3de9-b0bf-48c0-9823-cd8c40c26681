<template>
  <el-dialog v-model="dia" size="800px" append-to-body :title="id ? '修改问题' : '新增问题'">
    <c-form v-bind="formConfig" :key="form.pointId" ref="refsForm" v-model="form" class="mr-4" />
    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button type="primary" @click="sub">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
  import { getQa, updateQa, addQa } from '@/api/business/qa'
  export default {
    name: 'QaAddDia',
    emits: ['actSuccess'],
    data() {
      return {
        dia: false,
        loading: false,
        id: '',
        form: {},
        formConfig: {
          splice: 2,
          labelWidth: '7em',
          layout: 'block',
          autoEmit: false,
          hideBtn: true,
          formInfo: [
            {
              title: '问题',
              name: 'qaTitle',
              rules: { required: true, message: '请输入问题' }
            },
            {
              title: '问题内容',
              isFull: true,
              name: 'qaContent',
              type: 'editor'
            },
            {
              title: '备注',
              isFull: true,
              name: 'remark',
              type: 'textarea'
            }
          ]
        }
      }
    },
    methods: {
      show(id, pointId = '') {
        this.id = id
        this.dia = true
        this.form = {
          pointId,
          status: 1,
          qaContent: ''
        }
        id && this.getInfo(id)
      },
      getInfo(id) {
        this.loading = true
        getQa(id)
          .then(res => {
            this.form = res.data
          })
          .finally(() => {
            this.loading = false
          })
      },
      sub() {
        this.$refs.refsForm.subForm(form => {
          let act = this.form.qaId ? updateQa : addQa
          act(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.form = {}
            this.$emit('actSuccess')
          })
        })
      }
    }
  }
</script>
<style></style>
