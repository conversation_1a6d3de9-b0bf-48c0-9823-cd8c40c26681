<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData'
  import { rateUnits } from '@/constant/units'
  import useScanFormStore from '@/store/modules/form/scanForm'

  const fields = ref([
    {
      target: 'ddcFlag',
      name: '下变频',
      isCheckbox: true
    },
    {
      target: 'pickCf',
      name: '中心频率',
      appendSelect: true,
      options: rateUnits
    }
  ])

  const emit = defineEmits(['operation'])
  const model = reactive(new ModelData(fields.value, 'scan'))
  const scanFormStore = useScanFormStore()
  watch(
    () => [scanFormStore.ddcFlag, scanFormStore.pickCf],
    () => {
      model.update()
    },
    { deep: true }
  )
</script>

<template>
  <data-form :fields="fields" :model="model" />
</template>
