import request from '@/utils/request'

// 查询课程信息列表
export function listCurriculum(query) {
  return request({
    url: '/business/curriculum/list',
    method: 'get',
    params: query
  })
}

// 查询课程信息详细
export function getCurriculum(curriculumId) {
  return request({
    url: '/business/curriculum/' + curriculumId,
    method: 'get'
  })
}

// 新增课程信息
export function addCurriculum(data) {
  return request({
    url: '/business/curriculum',
    method: 'post',
    data: data
  })
}

// 修改课程信息
export function updateCurriculum(data) {
  return request({
    url: '/business/curriculum',
    method: 'put',
    data: data
  })
}

// 删除课程信息
export function delCurriculum(curriculumId) {
  return request({
    url: '/business/curriculum/' + curriculumId,
    method: 'delete'
  })
}
