<template>
  <div v-if="chartData">
    <data-chart
      :id="id"
      title="频谱图"
      :data="chartData"
      :config="config"
    />
  </div>
</template>

<script setup>
  import DataChart from '@/components/DataChart'
  import useChartsStore from '@/store/modules/charts'
  import usePlayControl from '@/store/modules/playControl'
  import useChartOptions from '@/common/hooks/chartOptions'
  import Interval from '@/common/classes/interval'
  import { SPECTRUM } from '@/constant/chartConsts'

  const props = defineProps({
    id: {
      type: String,
      default: SPECTRUM
    },
    hasHeader: {
      type: Boolean,
      default: true
    }
  })
  const chartsStore = useChartsStore()
  // 频谱图
  const config = ref({})
  const chartData = ref(null)
  const playControl = usePlayControl()
  // 请求图表数据
  const requestChartsData = async () => {
    const { data } = await chartsStore.querySequence()
    const { valueList } = data
    chartData.value = valueList
    const { chartOptions } = useChartOptions('spectrum', valueList.length)
    config.value = {
      type: 'static',
      chart: computed(() => chartOptions.value),
      viewNum: 'all',
      hideBar: !props.hasHeader,
      start: 0
    }
  }

  onMounted(async () => {
    await requestChartsData()
  })
</script>
