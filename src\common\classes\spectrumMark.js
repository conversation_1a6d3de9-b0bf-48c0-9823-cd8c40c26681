import { indexToPlot } from '@/utils/utils'

export default class SpectrumMarker {
  constructor(chart, index = 1, dragMarker, model) {
    this.chart = chart // 图表实例
    this.index = index
    this.allNum = 6
    this.marker = null // 标记点renderer定义 svg > path
    this.plotX = 0 // 坐标轴x位置
    this.point = null // 该频标在实时线上的基准点
    if (!chart.specturmMarkers) {
      chart.specturmMarkers = [this]
    } else {
      chart.specturmMarkers.push(this)
    }
    this.model = model
    this.visible = true
    this.dragMarker = dragMarker
    this.plotX = (this.chart.plotSizeX * this.index) / this.allNum
    this.markerPoint()
  }

  // 定位标记点
  markerPoint() {
    const point = this.findClosestPoint(this.chart.series[0])
    this.setPointInfo(point)
    this.drawCustomShape()
  }

  // 绘制自定义形状
  drawCustomShape() {
    this.destroy()
    if (!this.point) {
      return
    }
    const renderer = this.chart.renderer
    const x = this.chart.xAxis[0].toPixels(this.point.x)
    const y = this.chart.plotTop + this.chart.plotSizeY // 确保y轴在x轴底部
    const path = [] // 添加路径
    path.push('M', x - 12, y) // 移动到起始点
    path.push('L', x + 12, y) // 画一条线
    path.push('L', x, y - 24) // 画一条线
    path.push('Z') // 关闭路径
    path.push('M', x, y - 24)
    path.push('L', x, this.chart.plotTop)
    const strokeColor = this.isActive ? '#557395' : '#aeadad'
    const fillColor = this.isActive ? '#0d2b56' : '#aeadad'
    this.marker = renderer
      .path(path)
      .attr({ stroke: strokeColor, 'stroke-width': 1, fill: fillColor, zIndex: 4 })
    this.text = renderer.text(this.index, x - 5, y - 2).attr({
      zIndex: 5,
      fill: 'rgba(1, 23, 13, 1)',
      style: `fill: ${this.isActive ? '#f3f3f3' : '#333'};`
    })
    this.marker.add()
    this.text.add()
    if (this.isActive) {
      this.addDialog(x, y)
    }
    this.registerDragEvents()
  }

  // 设置标记点信息
  setPointInfo(point) {
    this.point = point
    this.coorX = point.x
    this.plotX = point.plotX
  }

  // 添加对话框
  addDialog(x, y) {
    const renderer = this.chart.renderer
    const path = [
      'M',
      x + 36.5,
      y - 31.5,
      'L',
      x + 43.5,
      y - 37.5,
      'M',
      x + 43.5,
      y - 31.5,
      'L',
      x + 36.5,
      y - 37.5
    ]
    const ctx = this
    this.dialog = {
      rect: renderer
        .rect(x - 50, y - 44, 100, 20, 5)
        .attr({ stroke: '#557395', fill: '#0d2b56', zIndex: 3 }),
      circle: renderer
        .circle(x + 40, y - 34, 5)
        .attr({ stroke: '#557395', fill: '#0d2b56', zIndex: 4, style: `cursor: pointer` }),
      lines: renderer.path(path).attr({ stroke: '#557395', zIndex: 4 }),
      text: renderer.text('频率:' + indexToPlot(this.point.x, this.model), x - 46, y - 30).attr({
        zIndex: 4,
        style: `font-size: 0.6em; fill: #f3f3f3`
      }),
      destroy() {
        this.rect.destroy()
        this.circle.destroy()
        this.lines.destroy()
        this.text.destroy()
        ctx.dialog = null
      },
      hide() {
        this.rect.hide()
        this.circle.hide()
        this.lines.hide()
        this.text.hide()
      },
      show() {
        this.rect.show()
        this.circle.show()
        this.lines.show()
        this.text.show()
      }
    }
    this.dialog.rect.add()
    this.dialog.circle
      .on('click', () => {
        this.dialog.destroy()
      })
      .add()
    this.dialog.lines.add()
    this.dialog.text.add()
  }

  // 获取点
  getPoints(item) {
    const { points, data, xData, yData } = item
    if (points && points.length > 0) {
      return points
    } else if (data && data.length > 0) {
      return data.map(d => ({ x: d.x, y: d.y, plotX: this.chart.xAxis[0].toPixels(d.x), plotY: this.chart.yAxis[0].toPixels(d.y) }))
    } else if (xData && xData.length > 0) {
      return xData.map((x, i) => ({
        x,
        y: yData[i],
        plotX: this.chart.xAxis[0].toPixels(x),
        plotY: this.chart.yAxis[0].toPixels(yData[i])
      }))
    }
    return []
  }

  // 找到最近的点
  findClosestPoint(item) {
    let minDist = Infinity
    let closestPoint = null
    const points = this.getPoints(item)

    points.forEach(point => {
      const dist = Math.abs(this.plotX - point.plotX)
      if (dist < minDist) {
        minDist = dist
        closestPoint = point
      }
    })
    return closestPoint
  }

  // 更新标记点
  update() {
    this.updatePoint()
  }

  // 更新标记点
  updatePoint() {
    const points = this.getPoints(this.chart.series[0])
    const currentPoint = points.find(p => p.x === this.coorX)
    if (currentPoint && (currentPoint.plotX !== this.plotX || this.point.hidden)) {
      this.point.hidden = false
      this.setPointInfo(currentPoint)
      this.visible && this.drawCustomShape()
    } else if (!currentPoint) {
      this.point.hidden = true
      this.destroy()
    }
  }

  // 更新图表
  updateChart(chart) {
    this.chart = chart
    if (!chart.specturmMarkers) {
      chart.specturmMarkers = [this]
    } else {
      chart.specturmMarkers.push(this)
    }
    this.updatePoint()
  }

  // 注册拖动事件
  registerDragEvents() {
    this.text.on('mousedown', event => {
      this.dragEvent(event)
    })
    this.marker.on('mousedown', event => {
      this.dragEvent(event)
    })
  }

  // 更新的拖动事件处理
  dragEvent(event) {
    event.preventDefault(); // 阻止浏览器默认的拖拽行为
    const startX = event.clientX; // 记录拖动起始位置
    const initialPlotX = this.plotX; // 记录初始 plotX

    // 设置当前标记为 activeMarker
    if (this.chart.activeMarker !== this) {
      this.chart.activeMarker?.removeActive(); // 移除先前的 activeMarker
      this.chart.activeMarker = this; // 设置当前标记为 activeMarker
      this.addActive(); // 添加激活状态
    }

    // 更新图表以在拖动期间不显示缩放
    this.chart.update({ chart: { zoomType: '' } });

    document.onmousemove = (ev) => {
      this.isMove = true;
      const deltaX = ev.clientX - startX; // 计算位移
      const newPlotX = initialPlotX + deltaX; // 基于位移计算新的 plotX
      this.plotX = newPlotX;
      this.markerPoint(); // 重新绘制频标到新位置

      // 更新 activeMarker 的状态
      this.chart.activeMarker = this;
      this.chart.activeMarker.plotX = newPlotX;
      this.chart.activeMarker.markerPoint();
    };

    document.onmouseup = () => {
      document.onmousemove = null;
      document.onmouseup = null;
      this.chart.update({ chart: { zoomType: 'xy' } }); // 恢复缩放
      if (!this.isMove) { // 检查是否有实际移动
        this.clickEvent(); // 如果没有移动，视为点击
      }
      this.isMove = false;
      this.markerPoint();

      // 确保 activeMarker 的状态已更新
      this.chart.activeMarker = this;
      this.chart.activeMarker.markerPoint();
    };
  }


  // 更新的点击事件处理
  clickEvent() {
    if (!this.isActive) {
      this.isActive = true;
      this.drawCustomShape(); // 使用激活样式重新绘制
      this.chart.activeMarker = this; // 设置为活动频标
    } else {
      this.isActive = false;
      this.drawCustomShape(); // 使用非激活样式重新绘制
      this.chart.activeMarker = null; // 清除活动频标
    }
  }

  // 移除激活状态
  removeActive() {
    this.isActive = false
    this.drawCustomShape()
    if (!this.visible) {
      this.hide()
    }
    document.onkeydown = null
  }

  // 添加激活状态
  addActive() {
    this.isActive = true
    document.onkeydown = event => {
      const { key } = event
      if (key === 'ArrowRight' || key === 'ArrowLeft') {
        const points = this.getPoints(this.chart.series[0])
        const offset = key === 'ArrowRight' ? 1 : -1
        const index = this.point.x + offset
        if (index > points.length - 1 || index < 0) {
          return
        }
        this.point = points[index]
        this.plotX = this.point.plotX
        this.drawCustomShape()
      }
    }
    this.drawCustomShape()
  }

  // 隐藏标记
  hide() {
    this.visible = false
    try {
      this.marker.hide()
      this.text.hide()
      this.dialog?.hide()
    } catch {
      /* empty */
    }
  }

  // 显示标记
  show() {
    if (this.visible) {
      return
    }
    this.visible = true
    try {
      this.marker.show()
      this.text.show()
      this.dialog?.show()
    } catch {
      /* empty */
    }
  }

  // 销毁标记
  destroy() {
    if (this.marker) {
      this.marker.destroy()
    }
    if (this.text) {
      this.text.destroy()
    }
    if (this.dialog) {
      this.dialog.destroy()
    }
  }

  // 移除标记
  remove() {
    if (this.chart.activeMarker === this) {
      this.chart.activeMarker = null
    }
    this.destroy()
    const index = this.chart.specturmMarkers.indexOf(this)
    if (index > -1) {
      this.chart.specturmMarkers.splice(index, 1)
    }
  }
}
