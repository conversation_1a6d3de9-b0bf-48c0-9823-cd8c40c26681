import { random } from 'lodash'

export default [
  {
    url: '/mock/signal/getEye', // 注意，这里只能是string格式
    method: 'post',
    response: () => {
      return {
        data: {
          I: [
            1, -0.71192862, -0.714514553, 0.690294408, -0.714514553, 0.690294408, -0.715697188,
            0.690294408, -0.715697188, 0.729765942, -0.715697188, 0.729765942, -0.727240829,
            0.729765942, -0.727240829, -0.694620533, -0.727240829, -0.694620533, -0.700438909,
            -0.694620533, -0.700438909, 0.722643281, -0.700438909, 0.722643281, 0.706493874,
            0.722643281, 0.706493874, 0.694794494, 0.706493874, 0.694794494, 0.7129008, 0.694794494,
            0.7129008, 0.702584247, 0.7129008, 0.702584247, 0.68268191, 0.702584247, 0.68268191,
            -0.705598862, 0.68268191, -0.705598862, 0.725005989, -0.705598862, 0.725005989,
            0.705701852, 0.725005989, 0.705701852, 0.719020027, 0.705701852, 0.719020027,
            0.705651134, 0.719020027, 0.705651134, -0.715698724, 0.705651134, -0.715698724,
            -0.698344872, -0.715698724, -0.698344872, 0.714237417, -0.698344872, 0.714237417,
            -0.719915597, 0.714237417, -0.719915597, 0.718296994, -0.719915597, 0.718296994,
            -0.707493816, 0.718296994, -0.707493816, -0.708710316, -0.707493816, -0.708710316,
            -0.712743209, -0.708710316, -0.712743209, -0.708772698, -0.712743209, -0.708772698,
            0.71573879, -0.708772698, 0.71573879, 0.702863986, 0.71573879, 0.702863986, 0.693063929,
            0.702863986, 0.693063929, -0.699284562
          ],
          Q: []
        }
      }
    }
  },
  {
    url: '/mock/signal/getModulation',
    method: 'post',
    response: () => {
      return {
        data: {
          demoData: [
            -0.698132336, 0.697332203, -0.692840636, 0.575826526, -0.66307658, 0.446807563,
            -0.609946549, 0.312659442, -0.535360634, 0.175966054, -0.441959918, 0.039483115,
            -0.332998633, -0.093914814, -0.212234706, -0.221302763, -0.083773896, -0.339776427,
            0.048062444, -0.446528137, 0.178894699, -0.538937092, 0.304445058, -0.614655674,
            0.420657992, -0.671683073, 0.523849249, -0.708456755, 0.610810637, -0.723916829,
            0.678898394, -0.71756506, 0.726093769, -0.689508736, 0.751059115, -0.640483499,
            0.753149688, -0.571849942, 0.732391059, -0.485579848, 0.689472675, -0.384226292,
            0.625682712, -0.270831466, 0.54284066, -0.148872301, 0.443214327, -0.022146938,
            0.329436451, 0.105353549, 0.20440577, 0.229549184, 0.071180992, 0.346433967,
            -0.067099817, 0.452191919, -0.207316771, 0.543331623, -0.346450984, 0.616815567,
            -0.48165819, 0.670168281, -0.610312641, 0.701569438, -0.730056643, 0.709914267,
            -0.838843644, 0.694870591, -0.934945405, 0.656884849, -1.016963243, 0.597170591,
            -1.083824039, 0.517682433, -1.134778738, 0.421039909, -1.169378638, 0.310433716,
            -1.187446594, 0.189531147, -1.189069748, 0.062333319, -1.174571157, -0.066953935,
            -1.144486189, -0.194060773, -1.099539161, -0.314819068, -1.040623784, -0.425290614,
            -0.968792558, -0.521898448, -0.885236144, -0.60153985, -0.79128778, -0.661697507,
            -0.688394606, -0.700508296, -0.578123271, -0.716807485, -0.462154239, -0.710163057,
            -0.342241555, -0.680870354, -0.220240846, -0.629925191, -0.098072991, -0.558963954,
            0.022307031, -0.470200032, 0.138915643, -0.366326213, 0.249779597, -0.250408173,
            0.352963597, -0.125769734, 0.446617454, 0.004132688, 0.529006958, 0.135824442,
            0.598553598, 0.265942186, 0.65388304, 0.391332954, 0.693849802, 0.50915432, 0.717580318,
            0.616958678, 0.72450918, 0.712744117, 0.714386463, 0.795015275, 0.687308133,
            0.862800837, 0.643717766, 0.915648162, 0.584397316, 0.953617573, 0.510466039,
            0.977246106, 0.423346251, 0.987494707, 0.324747533, 0.985685527, 0.216612622,
            0.973428667, 0.101077333, 0.952545941, -0.019570684, 0.924991608, -0.14297764,
            0.892758429, -0.266760319, 0.857806444, -0.388560981, 0.821992338, -0.506118715,
            0.787001312, -0.617320776, 0.754297256, -0.720239937, 0.725070834, -0.813176513,
            0.700231314, -0.894705415, 0.680377007, -0.963693798, 0.665794253, -1.019306183,
            0.656482279, -1.061018586, 0.65216291, -1.088617086, 0.652325809, -1.102174282,
            0.656260669, -1.102027774, 0.663113236, -1.08875525, 0.671932995, -1.063144922,
            0.681724489, -1.026143193, 0.691510379, -0.978817344, 0.700377941, -0.922322869,
            0.707526565, -0.8578493, 0.712297916, -0.786582768, 0.714215517, -0.709672213,
            0.713009119, -0.628199577, 0.708632767, -0.543157458, 0.701252103, -0.455431968,
            0.691246867, -0.365787625, 0.679201722, -0.274867803, 0.66585803, -0.183202356,
            0.652098656, -0.091219343, 0.638902664, 0.000735185, 0.627289116, 0.092378572,
            0.618267596, 0.183455482, 0.612790346, 0.273713499, 0.611700833, 0.362867504,
            0.615676284, 0.450568587, 0.625187695, 0.536392808, 0.640460789, 0.619795561,
            0.66143471, 0.700104058, 0.687755704, 0.776519358, 0.718765795, 0.848095417,
            0.753491461, 0.913766146, 0.790677428, 0.972354114, 0.828798354, 1.022589564,
            0.86611867, 1.063163638, 0.900740445, 1.092763901, 0.930645347, 1.11013937, 0.953798354,
            1.114138484, 0.968206108, 1.103764892, 0.972001493, 1.078264475, 0.963534892,
            1.037158608, 0.941440523, 0.980302989, 0.904711366, 0.907941878, 0.852773607,
            0.820726097, 0.785534859, 0.719740748, 0.703414738, 0.606510103, 0.607365012,
            0.482991964, 0.498867989, 0.351551682, 0.379931778, 0.214910179, 0.253038138,
            0.076086715, 0.12108285, -0.06167629, -0.012706545, -0.195004731, -0.144866005,
            -0.320496976, -0.271793902, -0.434828579, -0.389887512, -0.534862697, -0.495663285,
            -0.61776942, -0.585884392, -0.681129158, -0.657681525, -0.72303021, -0.708678484,
            -0.742155075, -0.737096429, -0.737848461, -0.741829634, -0.710162222, -0.722519517,
            -0.659873307, -0.679587781, -0.588495016, -0.614247918, -0.498238355, -0.528484762,
            -0.391947299, -0.42500782, -0.273040175, -0.307184398, -0.14538984, -0.178926796,
            -0.013207948, -0.044560719, 0.119094282, 0.091306202, 0.247073188, 0.223954678,
            0.366373926, 0.348720342, 0.472907364, 0.461162865, 0.563002288, 0.557244539,
            0.633521736, 0.633461177, 0.681995451, 0.68699348, 0.706703305, 0.715827942,
            0.706759095, 0.718828082, 0.682134211, 0.695795655, 0.633666813, 0.647492349,
            0.563051581, 0.575620592, 0.472758263, 0.482763767, 0.365966856, 0.372304767,
            0.246445075, 0.248301953, 0.118407235, 0.115346879, -0.013622909, -0.021612309,
            -0.144989967, -0.157452196, -0.27106753, -0.287062705, -0.387425363, -0.405558735,
            -0.489998519, -0.508446097, -0.575222194, -0.591813207, -0.640156686, -0.652482748,
            -0.68259722, -0.688119769, -0.701140881, -0.697345734, -0.695230246, -0.67978096,
            -0.665167511, -0.636066139, -0.612095177, -0.567845702, -0.537935793, -0.477695912,
            -0.445318848, -0.36903584, -0.337484598, -0.245990142, -0.218146726, -0.113231666,
            -0.091370709, 0.024192004, 0.038579296, 0.161066189, 0.167397037, 0.292188942,
            0.290868133, 0.412590176, 0.405017734, 0.517722368, 0.506238639, 0.603609204,
            0.591394365, 0.667031288, 0.657905817, 0.705625534, 0.703822494, 0.717972517,
            0.72785002, 0.703656554, 0.729379594, 0.663265944, 0.70847851, 0.598369777, 0.665841877,
            0.511453986, 0.602755725, 0.405817151, 0.521021128, 0.285437733, 0.422880381,
            0.15483427, 0.310921311, 0.018869204, 0.187981114, -0.117422864, 0.057058159,
            -0.249027118, -0.078793436, -0.371144801, -0.216540411, -0.479354084, -0.353252202,
            -0.569786131, -0.486165524, -0.639251292, -0.612742066, -0.685351133, -0.73071593,
            -0.706558943, -0.838103771, -0.702260792, -0.933233261, -0.672755897, -1.014757276,
            -0.619249821, -1.081630349, -0.543787777, -1.133101225, -0.449150294, -1.168702126,
            -0.338770807, -1.188221216, -0.216583252, -1.191679597, -0.086868264, -1.179316163,
            0.045888547, -1.151565433, 0.177159905, -1.109034538, 0.302551538, -1.052505016,
            0.41793558, -0.982911468, 0.519585669, -0.901331723, 0.604292989, -0.808998942,
            0.669450045, -0.707291067, 0.713136733, -0.59772867, 0.734142601, -0.481974036,
            0.731995046, -0.361821622, 0.706938028, -0.239188567, 0.659891427, -0.116100833,
            0.592399895, 0.005333123, 0.506556571, 0.122952752, 0.404908091, 0.234580055,
            0.290344834, 0.338066965, 0.166009396, 0.431342453, 0.03515888, 0.512466669,
            -0.098921642, 0.57968533, -0.233029991, 0.631494939, -0.364140272, 0.666684568,
            -0.489467323, 0.68437916, -0.606559515, 0.684098244, -0.713313699, 0.665774941,
            -0.808026314, 0.629776895, -0.889417231, 0.576914966, -0.956606925, 0.508428991,
            -1.009108782, 0.425960034, -1.046807766, 0.331523359, -1.069913864, 0.227449298,
            -1.078918576, 0.116299585, -1.074546576, 0.000816525, -1.057694435, -0.116182886,
            -1.029391527, -0.231899112, -0.990735888, -0.343627125, -0.942849576, -0.448858649,
            -0.886847079, -0.545364797, -0.823800921, -0.63127476, -0.754719317, -0.705123603,
            -0.680524647, -0.76591748, -0.602047801, -0.813159704, -0.52003485, -0.846849561,
            -0.435150146, -0.867482603, -0.347979277, -0.876016259, -0.259065092, -0.873832405,
            -0.168914527, -0.862655818, -0.078014843, -0.844480813, 0.013135014, -0.82148248,
            0.104023032, -0.795915723, 0.194098219, -0.770012498, 0.282762408, -0.74587661,
            0.369377047, -0.725390732, 0.453252554, -0.710115969, 0.533648133, -0.701232612,
            0.609795451, -0.699463069, 0.680908084, -0.705052257, 0.746197164, -0.71775198,
            0.804891407, -0.736793518, 0.856257021, -0.760939479, 0.899627268, -0.788534403,
            0.934405565, -0.817574799, 0.960101902, -0.84578228, 0.976346791, -0.870711029,
            0.982884705, -0.889859319, 0.979599595, -0.900791287, 0.966510475, -0.901249826,
            0.943772197, -0.889256477, 0.911665499, -0.863239646, 0.870583892, -0.822102785,
            0.821031034, -0.765306056, 0.763597548, -0.692912519, 0.698945701, -0.605612457,
            0.627789795, -0.504738271, 0.55087626, -0.392218113, 0.468970984, -0.270534754,
            0.38284868, -0.142651394, 0.293273419, -0.011910111, 0.200991288, 0.118081257,
            0.106742553, 0.243570477, 0.011237378, 0.360805213, -0.084829316, 0.466187239,
            -0.180767134, 0.556396902, -0.275897652, 0.628518283, -0.369519144, 0.680165529,
            -0.460922509, 0.709576309, -0.549375415, 0.715702415, -0.634094954, 0.698256254,
            -0.71425724, 0.657726109, -0.788997412, 0.595392168, -0.857417643, 0.513265133,
            -0.918576002, 0.414041936, -0.971510649, 0.301015198, -1.015262008, 0.177950025,
            -1.048887491, 0.048976414, -1.07149601, -0.081575304, -1.082280397, -0.209315062,
            -1.080555797, -0.329943359, -1.065777302, -0.439409375, -1.037588596, -0.534062624,
            -0.995867848, -0.610753953, -0.940737545, -0.666951001, -0.872594595, -0.700843275,
            -0.792136014, -0.711369038, -0.700355113, -0.698271215, -0.598546863, -0.662082374,
            -0.488306701, -0.604098022, -0.371491879, -0.526337683, -0.250199199, -0.431444973,
            -0.126726761, -0.322600722, -0.003511858, -0.203407809, 0.116917424, -0.077754281,
            0.23200886, 0.050316058, 0.339262247, 0.176736251, 0.436297625, 0.297554255,
            0.520913124, 0.409059197, 0.591155946, 0.507892847, 0.645395517, 0.591130674,
            0.682364762, 0.656379104, 0.701198816, 0.701823294, 0.701475382, 0.726240218,
            0.683224499, 0.729040921, 0.646932006, 0.710244358, 0.59353596, 0.67044735, 0.52439028,
            0.610784709, 0.441228062, 0.532864988, 0.346113145, 0.438697636, 0.241374075,
            0.330615669, 0.129540265, 0.211200997, 0.013257797, 0.083189584, -0.104793012,
            -0.050597776, -0.221971601, -0.187325761, -0.33575651, -0.324219376, -0.443820238,
            -0.458616942, -0.544093728, -0.58801198, -0.634816945, -0.710086763, -0.714593291,
            -0.822748125, -0.78241235, -0.924142659, -0.837657034, -1.012657642, -0.880120516,
            -1.086936712, -0.909986913, -1.145868659, -0.927805901, -1.18858397, -0.934456825,
            -1.214464903, -0.931097507, -1.223122001, -0.919110954, -1.214413881, -0.900043726,
            -1.1884408, -0.875543833, -1.14553225, -0.847283363, -1.086277723, -0.816906214,
            -1.011514783, -0.785975456, -0.922342837, -0.755907655, -0.820128143, -0.727926731,
            -0.706501961, -0.703037739, -0.583363831, -0.682009161, -0.452854693, -0.665347099,
            -0.317354023, -0.653304458, -0.179443732, -0.645888448, -0.041856688, -0.642875969,
            0.092556782, -0.643846571, 0.220896706, -0.648213565, 0.34028998, -0.655272722,
            0.44796744, -0.664242148, 0.541335821, -0.674300611, 0.61806941, -0.684635401,
            0.676202893, -0.694496632, 0.714191556, -0.703219295, 0.730990529, -0.710255027,
            0.72611165, -0.715201437, 0.699659109, -0.717811644, 0.652365446, -0.718012035,
            0.585579515, -0.715890646, 0.50125438, -0.71169132, 0.401909918, -0.705797911,
            0.290551096, -0.698709667, 0.170611218, -0.691014469, 0.045831736, -0.683353186,
            -0.079858631, -0.676387787, -0.202442557, -0.670765519, -0.3179757, -0.667081356,
            -0.42270866, -0.665842116, -0.513213813, -0.667450249, -0.586514652, -0.672168612,
            -0.640184581, -0.680101037, -0.672436059, -0.691186309, -0.682186961, -0.705186903,
            -0.669099152, -0.721695542, -0.633588552, -0.740140378, -0.576832592, -0.759803891,
            -0.500702679, -0.779836893, -0.407709926, -0.799288392, -0.300928563, -0.817134798,
            -0.183865607, -0.832309365, -0.060364973, -0.843738377, 0.065553427, -0.850375354,
            0.189814463, -0.851241767, 0.308421552, -0.845439851, 0.417615265, -0.83218801,
            0.513955712, -0.810863674, 0.594447434, -0.781008542, 0.656639278, -0.742337883,
            0.698668957, -0.694762707, 0.719319582, -0.638391018, 0.718036115, -0.573526263,
            0.69491601, -0.500663698, 0.650692225, -0.420473963, 0.586670458, -0.333795696,
            0.504675508, -0.241603836, 0.406977385, -0.144992888, 0.296177953, -0.045160953,
            0.17513068, 0.056623567, 0.046843734, 0.159054965, -0.085628331, 0.260813445,
            -0.219263047, 0.360583335, -0.351160944, 0.457089365, -0.47861889, 0.549108863,
            -0.59918803, 0.635507047, -0.710718334, 0.715244174, -0.811375678, 0.787378371,
            -0.899682581, 0.851102531, -0.974509656, 0.905729234, -1.035063624, 0.950706303,
            -1.08087194, 0.985617757, -1.111752868, 1.010178208, -1.127801895, 1.024235487,
            -1.129323363, 1.027753592, -1.116825342, 1.020819068, -1.090983629, 1.003624797,
            -1.05258739, 0.976460755, -1.002537489, 0.939710677, -0.941811144, 0.893839777,
            -0.871447146, 0.8393839, -0.792527616, 0.77694416, -0.70618248, 0.707177043,
            -0.613573909, 0.630792141, -0.515889764, 0.548549652, -0.414346099, 0.461237311,
            -0.310182363, 0.369684041, -0.204663172, 0.274754316, -0.099052042, 0.177329212,
            0.005378487, 0.078314468, 0.107373446, -0.021363704, 0.20571582, -0.120770901,
            0.299242258, -0.218972534, 0.386883318, -0.315032512, 0.467669278, -0.408029437,
            0.540770471, -0.497065663, 0.605522335, -0.581276178, 0.661445081, -0.659839809,
            0.708271444, -0.731986344, 0.745951295, -0.797011673, 0.774663389, -0.85428828,
            0.794808269, -0.903268874, 0.807002008, -0.9435004, 0.812064469, -0.974632204,
            0.81097728, -0.996407688, 0.804854453, -1.008679032, 0.794911146, -1.011400223,
            0.782407701, -1.004628897, 0.768598497, -0.988519549, 0.754695714, -0.963305116,
            0.741810203, -0.92931062, 0.730901659, -0.886927962, 0.722757578, -0.836606622,
            0.717944741, -0.778846562, 0.716788292, -0.714184642, 0.719370484, -0.643186986,
            0.725517511, -0.56644243, 0.734805584, -0.484561026, 0.746584177, -0.398158938,
            0.760007143, -0.307858914, 0.774056077, -0.214303151, 0.787588537, -0.118152954,
            0.799385846, -0.020079114, 0.808195591, 0.079219066, 0.81279242, 0.179002553,
            0.812015414, 0.278499275, 0.804799318, 0.376888037, 0.790234208, 0.473289579,
            0.767588496, 0.566776514, 0.736319602, 0.65637213, 0.696111321, 0.74104619, 0.646858335,
            0.819736719, 0.588665545, 0.891349435, 0.521861732, 0.954780519, 0.446963668,
            1.008948803, 0.364656776, 1.052808762, 0.275784463, 1.08539331, 0.181315765, 1.10583365,
            0.082314968, 1.113392472, -0.020070877, 1.107515454, -0.124640539, 1.087835312,
            -0.230164602, 1.054205298, -0.335389882, 1.006743073, -0.43906495, 0.945825219,
            -0.539945662, 0.87210238, -0.636795461, 0.786515236
          ],
          dots: [
            -0.698132336, 0.697332203, 0.726093769, -0.689508736, -0.730056643, 0.709914267,
            -0.688394606, -0.700508296, 0.72450918, 0.712744117, -0.720239937, 0.725070834,
            -0.709672213, 0.713009119, 0.700104058, 0.687755704, 0.719740748, 0.703414738,
            -0.710162222, -0.722519517, 0.706759095, 0.718828082, -0.701140881, -0.697345734,
            0.703822494, 0.717972517, -0.73071593, -0.706558943, -0.707291067, 0.713136733,
            0.684098244, -0.713313699, -0.705123603, -0.680524647, -0.699463069, 0.680908084,
            -0.692912519, 0.698945701, 0.698256254, -0.71425724, -0.711369038, -0.700355113,
            0.701823294, 0.701475382, -0.710086763, -0.714593291, -0.706501961, -0.703037739,
            0.699659109, -0.717811644, -0.682186961, -0.705186903, 0.698668957, -0.694762707,
            -0.710718334, 0.715244174, -0.70618248, 0.707177043, 0.708271444, -0.731986344,
            0.716788292, -0.714184642, 0.696111321, 0.74104619
          ]
        }
      }
    }
  },
  {
    url: '/mock/signal/getFFT',
    method: 'post',
    response: ctx => {
      const valueList = []
      const len = ctx.body.logarithm ? 8102 : 4096
      for (let i = 0; i < len; i++) {
        let item = 0
        if (i < 1500 || i > 2500) {
          item = -100 + 10 * Math.random()
        } else {
          item = -70 + 10 * Math.random()
        }
        valueList.push(item)
      }
      return {
        data: {
          pIQ_In: 16384,
          sig_results: [
            {
              cf: 80000000,
              bw: 15000000,
              amp: -85
            }
          ],
          valueList
        }
      }
    }
  }
]
