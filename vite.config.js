import { defineConfig, loadEnv } from 'vite';
import path from 'path';
// import fs from 'fs';
import { wrapperEnv } from './build/utils';
import { createVitePlugins } from './build/vite/plugin';
import { OUTPUT_DIR } from './build/constant';
import { createProxy } from './build/vite/proxy';

export default defineConfig(({ mode, command }) => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);
  const isBuild = command === 'build';
  const { VITE_PUBLIC_PATH, VITE_DROP_CONSOLE, VITE_PORT, VITE_PROXY } = viteEnv;

  return {
    base: './',
    plugins: createVitePlugins(viteEnv, isBuild),
    resolve: {
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src')
      },
      dedupe: ['vue'],
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      port: 8888,
      host: true,
      // https: {
      //   key: fs.readFileSync(path.resolve(__dirname, 'cert/server.key')),
      //   cert: fs.readFileSync(path.resolve(__dirname, 'cert/server.crt')),
      // },
      // http2: true, // 启用 HTTP/2
      proxy: createProxy(VITE_PROXY)
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {},
          javascriptEnabled: true,
          charset: false,
          additionalData: `@import "src/assets/styles/var.less";`
        }
      }
    },
    build: {
      // cssTarget: 'chrome83',
      target: 'es2015'
    },
    define: {
      '__VUE_OPTIONS_API__': JSON.stringify(true),
      '__VUE_PROD_DEVTOOLS__': JSON.stringify(false),
      '__VUE_PROD_HYDRATION_MISMATCH_DETAILS__': JSON.stringify(false)
    }
  }
});
