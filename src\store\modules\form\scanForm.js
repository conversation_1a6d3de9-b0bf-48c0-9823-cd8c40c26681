import { defineStore } from 'pinia'
import useChartsStore from '../charts'
import { round, cloneDeep } from 'lodash'

const { global } = useChartsStore()
export default defineStore('scanFormStore', {
  state: () => {
    const { file } = useChartsStore()
    return {
      // 信号文件参数
      fileCenterFreIn: 0,//信号文件中心频率
      fileDebugMode: 0,
      fileSamplingRate: 0,//信号文件采样率
      fileBitRate: 0,//信号文码速率
      fileIfbw: 0,//信号文件中频带宽
      iqNum: 0,//信号文件IQ点数
      iqReverse: false, // IQ 颠倒
      logarithm: false, // 取对数

      //下变频
      ddcFlag: false,//下变频启用标志
      downCenterFreIn: 0,//下变频中心频率

      //滤波
      filterFlag: false,//滤波启用标志
      filterorder: 7,//滤波阶数
      coefficientDown: 0.33,//滚降系数

      // 信号分选
      isITU: true,//ITU参数测量启用标志
      isSignalSort: true,//信号分选启用标志

      // 参考线
      refLine: false,
      level: -80,


      // 重叠处理方式
      overlapType: 1,//重叠处理方式   1：平均值 2：最大值

      // 分析设置
      allLen: 0,
      pickCf: 0,//分析设置中心频率
      pickBw: 0,//分析设置中频带宽
      startRate: 0,//分析设置起始频率
      endRate: 0,//分析设置终止频率

      //信号选取
      allPointLen: 0,
      startPoint: 0,
      endPoint: 65535,

      amScale: 10,
      capital: false,
      currentFileParams: file,
      path: 0
    }
  },
  getters: {
    len() {
      return round(this.endPoint) - round(this.startPoint) + 1
    },
    bandwidth() {
      return this.endRate - this.startRate
    },
    centerFreqIn() {
      return global.centerFreqIn
    },
    debugMode() {
      return global.debugMode
    },
    samplingRate() {
      return global.samplingRate
    },
    bitRate() {
      return global.bitRate
    },
    intermediateFrequencyBandwidth() {
      return global.intermediateFrequencyBandwidth
    },
    refLevel() {
      return global.refLevel
    },
    scale() {
      if (this.fileIfbw) {
        return this.allLen / this.fileIfbw
      }
    },
    left() {
      return this.fileCenterFreIn - this.fileIfbw / 2
    }
  },
  actions: {
    init(bw, cf) {
      this.pickCf = cf * 1
      this.pickBw = bw * 1
      this.setRateByBw()
      // this.setPoint()
    },
    // 根据中心频率，中频带宽设置起始点
    setRateByBw() {
      this.startRate = this.pickCf - this.pickBw / 2
      this.endRate = this.pickCf + this.pickBw / 2
    },
    setCfAndBwByRate() {
      this.pickBw = this.endRate - this.startRate
      this.pickCf = this.startRate + this.pickBw / 2
    },
    setRateByPoint() {
      if (!this.scale) {
        return
      }
      this.startRate = this.startPoint / this.scale + this.left
      this.endRate = (this.endPoint + 1) / this.scale + this.left
    },
    setPoint() {
      if (!this.scale) {
        return
      }
      this.startPoint = round(this.scale * (this.startRate - this.left))
      this.endPoint = round(this.scale * (this.endRate - this.left) - 1)
    },

    setFilterorder() {
      return this.filterorder
    },
    setCoefficientDown(val) {
      this.coefficientDown = parseFloat(val)
    },
    setCenterFre() {
      if (this.pickCf !== 0) {
        global.centerFreqIn = this.pickCf
      }
    },
    getFileParams() {
      return cloneDeep(this.currentFileParams)
    },
    setFileParams(data) {
      this.currentFileParams = data
    }
  }
})
