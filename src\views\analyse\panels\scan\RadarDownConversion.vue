<template>
  <data-form :fields="fields" :model="model" />
  <el-button style="width: 80%;margin: 0 10%;" @click="getDDCfft">下变频</el-button>
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData';
  import { rateUnits } from '@/constant/units'
  import useScanFormStore from '@/store/modules/form/radarScanForm'
  import useChartsStore from '@/store/modules/charts'
  import { getDDC } from '@/api/charts'
  import { cloneDeep } from 'lodash'

  const fields = ref([
    {
      target: 'downCenterFreIn',
      name: '中心频率',
      appendSelect: true,
      options: rateUnits
    },
  ])
  const model = reactive(new ModelData(fields.value, 'radarScan'))
  const scanFormStore = useScanFormStore()
  const chartsStore = useChartsStore()
  const uniqueId = `${Date.now()}${Math.floor(Math.random() * 1000)}`;
  let body = scanFormStore.getFileParams()
  body.doubleMapKey = uniqueId
  body.samplingRate = body.samplerateIn
  const getDDCfft = () => {
    if (!body.fileName) {
      body = scanFormStore.getFileParams()
      body.doubleMapKey = uniqueId
      body.samplingRate = body.samplerateIn
    }
    body.ddcFreq_In = scanFormStore.downCenterFreIn
    getDDC(body).then(res=>{
      chartsStore.setRadarDownConversion(res.data)
    })
  }
  watch(() => scanFormStore, () => {
    model.update()
  }, { deep: true })
</script>
