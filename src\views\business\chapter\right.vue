<template>
  <el-col :span="20" class="app-container pl-4">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      @selectionChange="rows => (selection = rows)"
    />
    <mtDetailInfo ref="refsMt" @actSuccess="getList()" />
    <c-import ref="refsImport" />
  </el-col>
  <el-dialog v-model="openOpints" title="" width="1000px" append-to-body>
    <el-row class="app-container" style="height: 500px">
      <!-- <left-tree @change="res => (chapterId = res)" /> -->
      <right-list :key="chapterId" :chapter-id="chapterId" />
    </el-row>
  </el-dialog>
  <c-import ref="refsImport" />
</template>

<script>
  import RightList from './points.vue'
  import points from './points.vue'
  import * as mtApi from '@/api/business/chapter'
  import mtDetailInfo from './detail.vue'
  import router from '@/router'
  import { ElMessage, ElMessageBox } from 'element-plus'

  export default {
    name: 'Chapter',
    components: { mtDetailInfo, RightList },
    props: ['courseId'],
    data () {
      return {
        selection: [],
        chapterId: '',
        openOpints: false,
        tableFormConfig: {
          title: '章节管理',
          searchConfig: {
            formConfig: [
              { title: '章节编码', name: 'chapterCode' },
              {
                title: '章节名称',
                name: 'chapterName'
              }
            ],
            loadData: p => {
              p.courseId = this.courseId
              return mtApi.listChapter(p)
            }
          },
          actions: [
            {
              label: '新增',
              type: 'primary',
              icon: 'el_Plus',
              permission: ['business:chapter:add'],
              click: () => {
                this.$refs.refsMt.show({
                  chapterPid: '0',
                  chapterId: '',
                  courseId: this.courseId
                })
              }
            }
            ,
            {
              label: '导出',
              icon: 'el_upload',
              type: 'warning',
              permission: ['business:teacher:export'],
              click: row =>
                this.download('/business/chapter/export?' + 'courseId=' + this.courseId, {}, `章节管理_${new Date().getTime()}.xlsx`)
            },
            {
             
              label: '导入',  
              type: 'info',       
              icon: 'el_download',
              click: row => {
                this.importFile()
              }
            }
            // {
            //   label: '展开/折叠',
            //   icon: 'el_Sort',
            //   click: () => {
            //     this.$refs.refsTableForm.$refs.table.toggleRowExpansion()
            //   }
            // }
          ],
          tableConfig: {
            rowKey: 'chapterId',
            hideIndex: true,
            parseTable: list => {
              return this.handleTree(list, 'chapterId', 'chapterPid')
            },
            cols: [
              { label: '序号', prop: 'orderNum', width: 70 },
              {
                label: '章节名称',
                prop: 'chapterName',
                minWidth: 200,
                renderFun: (h, { row }) => {
                  // if (row.chapterPid !== '0') {
                    return h(
                      'a',
                      {
                        onClick: () => {
                          this.openOpints = true
                          this.chapterId = row.chapterId
                          // router.push({
                          //   path: '/information/points',
                          //   query: { chapterId: row.chapterId }
                          // })
                        }
                      },
                      row.chapterName
                    )
                  // } else {
                  //   return h(
                  //     'span',
                  //     {
                  //       onClick: () => {}
                  //     },
                  //     row.chapterName
                  //   )
                  // }
                }
              },

              { label: '章节编码', prop: 'chapterCode' },
              { label: '序号', prop: 'orderNum' },
              {
                label: '操作',
                type: 'action',
                width: '80',
                actions: [
                  {
                    title: '编辑',
                    icon: 'el_Edit',
                    permission: ['business:chapter:edit'],
                    click: row =>
                      this.$refs.refsMt.show({ chapterId: row.chapterId, courseId: this.courseId })
                  },
                  {
                    title: '删除',
                    icon: 'el_Delete',
                    permission: ['business:chapter:remove'],
                    click: row => this.deleteAll(row.chapterId)
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList (data = null) {
        this.$refs.refsTableForm.loadData(data)
      },
      //批量删除模板
      deleteAll (id = null) {
        if (!this.selection.length && !id) return this.$model.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选章节吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.chapterId)
          mtApi.delChapter(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.getList()
          })
        })
      },
      importFile(){
        ElMessageBox.confirm('确定要在此课程下导入章节吗', '提示').then(res => {
          this.$refs.refsImport.show({
         tmpUrl: '/business/chapter/excelTemplate',
         tmpName:'章节管理',
         uploadUrl: '/business/chapter/importData?' + 'courseId=' + this.courseId,
          ok: res => {
            this.getList()
          }
        })
    })
        
      
      }
    }
  }
</script>
