<template>
  <div>
    <div id="map" ref="map" style="width: 100vw; height: 100vh" />
    <div id="overlay-box" />
  </div>
</template>
<script setup>
  import 'ol/ol.css'
  import TileLayer from 'ol/layer/Tile'
  import VectorLayer from 'ol/layer/Vector'
  import VectorSource from 'ol/source/Vector'
  import XYZ from 'ol/source/XYZ'
  import { Map, View, Feature } from 'ol'
  import { Style, Icon } from 'ol/style'
  import { Point } from 'ol/geom'
  import Overlay from 'ol/Overlay'
  import { onMounted } from 'vue'
  let map = ref(null)
  let pointLayer = {}
  // 地图坐标数据
  let mapData = [
    [104, 30],
    [105, 31],
    [106, 32],
    [107, 33],
    [108, 34]
  ]
  onMounted(() => {
    initMap()
  })
  const initMap = () => {
    map = new Map({
      target: 'map',
      layers: [
        new TileLayer({
          source: new XYZ({
            url: 'http://map.geoq.cn/ArcGIS/rest/services/ChinaOnlineStreetPurplishBlue/MapServer/tile/{z}/{y}/{x}'
          })
        })
      ],
      view: new View({
        projection: 'EPSG:4326',
        center: [104, 30],
        zoom: 7
      })
    })
    mapData.forEach(item => {
      addPoints(item)
    })
  }
  /**
   * 点击地图添加摄像头要素
   */
  // const clickMap = () => {
  //   map.on('click', e => {
  //     addPoints(e.coordinate)
  //   })
  // }
  /**
   * 根据经纬度坐标添加监测站要素
   */
  const addPoints = coordinate => {
    if (Object.keys(pointLayer).length == 0) {
      // 创建图层
      pointLayer = new VectorLayer({
        source: new VectorSource()
      })
      // 图层添加到地图上
      map.addLayer(pointLayer)
    }

    // 创建feature要素，一个feature就是一个点坐标信息
    const feature = new Feature({
      geometry: new Point(coordinate)
    })
    // 设置要素的图标
    feature.setStyle(
      new Style({
        // 设置图片效果
        image: new Icon({
          src: 'https://smart-garden-manage.oss-cn-chengdu.aliyuncs.com/shexiangtou.png',
          // anchor: [0.5, 0.5],
          scale: 0.8
        })
      })
    )
    // 要素添加到地图图层上
    // this.pointLayer.getSource().addFeatures([feature]);
    pointLayer.getSource().addFeature(feature)
    // 设置文字信息
    addText(coordinate)
  }
  const addText = coordinate => {
    const overlayBox = document.getElementById('overlay-box') //获取一个div
    const oSpan = document.createElement('span') //创建一个span
    oSpan.contentEditable = true //设置文字是否可编辑
    oSpan.id = coordinate[0] //创建一个id
    let pText = document.createTextNode('监测站' + coordinate[0].toFixed(0)) //创建span的文本信息
    // 设置颜色和字体大小
    oSpan.style = 'color: #fff; font-size: 16px'
    oSpan.appendChild(pText) //将文本信息添加到span
    overlayBox.appendChild(oSpan) //将span添加到div中
    let textInfo = new Overlay({
      position: coordinate, //设置位置
      element: document.getElementById(coordinate[0]),
      offset: [-35, -50] //设置偏移
    })
    map.addOverlay(textInfo)
  }
</script>
