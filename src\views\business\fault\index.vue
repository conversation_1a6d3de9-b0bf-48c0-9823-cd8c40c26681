<template>
  <div class="app-container">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      @selectionChange="rows => (selection = rows)"
    />
    <mtDetailInfo ref="refsMt" @actSuccess="getList()" />
    <PointDetail ref="refsPointDetail" />
  </div>
</template>
<script>
  import * as mtApi from '@/api/business/fault'
  import mtDetailInfo from './tmpInfo.vue'
  import PointDetail from '../points/detail.vue'
  import { withDirectives } from 'vue'
  import { ElPopper } from 'element-plus'
  export default {
    name: 'Sctudents',
    components: { mtDetailInfo, PointDetail },
    data() {
      return {
        selection: [],
        tableFormConfig: {
          title: '报修处理',
          searchConfig: {
            formConfig: [
              { title: '报修资源IP', name: 'repairResIp' },
              { title: '报修时间', name: 'repairTime', type: 'date' },
              {
                title: '报修状态',
                name: 'repairStatus',
                type: 'dict',
                dictName: 'vm_repair_status'
              }
            ],
            loadData: p => {
              return mtApi.listFault(p)
            }
          },
          actions: [
            // {
            //   label: '打印',
            //   type: 'primary',
            //   icon: 'el_Plus',
            //   permission: ['business:fault:add'],
            //   click: () => {
            //     this.$refs.refsPrint.printIt()
            //   }
            // },
            {
              label: '导出',
              icon: 'el_upload',
              type: 'warning',
              permission: ['business:fault:export'],
              click: row =>
                this.download(
                  'business/fault/export',
                  {
                    // ...queryParams.value
                  },
                  `报修处理_${new Date().getTime()}.xlsx`
                )
            },

            {
              type: 'danger',
              label: '删除',
              permission: ['business:fault:remove'],
              disabled: () => !this.selection.length,
              icon: 'el_Delete',
              click: row => this.deleteAll()
            }
          ],
          tableConfig: {
            isChecked: true,
            cols: [
              {
                label: '报修资源IP',
                prop: 'repairResIp'
              },
              { label: '资源名称', prop: 'repairResName', minWidth: 200 },
              { label: '报修人', prop: 'repairUserName' },
              {
                label: '故障描述',
                prop: 'repairBiz'
                // renderFun: (h, { row }) => {
                //   return h('span', {
                //     innerHTML: row.repairBiz
                //   })
                // }
              },
              {
                label: '故障原因',
                prop: 'repairReason'
                // showAll: false,
                // width: 300,
                // renderFun: (h, { row }) => {
                //   return h('span', {
                //     innerHTML: row.repairReason
                //   })
                // }
              },
              { label: '报修时间', prop: 'repairTime' },
              { label: '计划修理时间', prop: 'repairPlanTime' },
              { label: '修复完成时间', prop: 'repairDoneTime' },
              {
                label: '报修状态',
                prop: 'repairStatus',
                type: 'dict',
                dictName: 'vm_repair_status'
              },
              { label: '备注', prop: 'remark', width: 300 },
              {
                label: '操作',
                type: 'action',
                width: '100',
                actions: [
                  {
                    title: '报修处理',
                    icon: 'el_Edit',
                    permission: ['business:fault:edit'],
                    click: row => this.$refs.refsMt.show(row.faultId)
                  },
                  {
                    title: '删除',
                    icon: 'el_Delete',
                    permission: ['business:fault:remove'],
                    click: row => this.deleteAll(row.faultId)
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList(data = null) {
        this.$refs.refsTableForm.loadData(data)
      },
      //批量删除模板
      deleteAll(id = null) {
        if (!this.selection.length && !id) return this.$modal.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选保修单吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.faultId)
          mtApi.delFault(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.getList()
          })
        })
      }
    }
  }
</script>
