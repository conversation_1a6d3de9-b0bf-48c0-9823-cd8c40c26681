<template>
  <i class="x-icon" :style="styleInfo"
    ><slot>
      <component :is="iconName.icon" v-if="iconName.source != 'cus'" />
      <svg v-else aria-hidden="true" class="svg-icon">
        <use :xlink:href="'#icon-' + iconName.icon" />
      </svg>
    </slot>
  </i>
</template>
<script lang="ts">
  import { defineComponent, computed, ref } from 'vue'
  import { useIconsStore } from '@/store/modules/icons'
  const props = {
    cusClass: { type: String, default: null },
    icon: { type: String, default: '' },
    source: { type: String, default: '' },
    color: { type: String, default: '' },
    size: { type: [String, Number], default: 18 },
    sourceIcon: { type: String, default: '' }
  }
  export default defineComponent({
    name: 'Xicon',
    props,
    setup(props) {
      let selfSource = ref('')
      let Icons = useIconsStore()
      let styleInfo = computed(() => {
        return {
          fontSize: props.size ? props.size + 'px' : '',
          color: props.color || ''
        }
      })
      let iconName = computed(() => {
        let { icon, source, sourceIcon } = props
        if (sourceIcon) {
          icon = sourceIcon.split('_')[1]
          source = sourceIcon.split('_')[0]
        }
        let Icon = Icons[source] || Icons.el
        if (source == 'el') {
          icon = icon
            .split('-')
            .map(v => v.replace(/^\S/, s => s.toUpperCase()))
            .join('')
        }
        return { icon: Icon[icon] || icon, source }
      })
      return { styleInfo, iconName, selfSource }
    }
  })
</script>
<style lang="less">
  .x-icon {
    font-style: normal;
    vertical-align: text-bottom;
    height: 1em;
    line-height: 1em;
    text-align: center;
    display: inline-block;
    position: relative;
    fill: currentColor;
    transform: translateZ(0);
    svg {
      height: 1em;
      width: 1em;
      display: block;
    }
  }

  .el-button,
  .el-link {
    .x-icon {
      padding-right: 3px;
      font-size: var(--font-size);
    }
  }
  .el-button.is-circle {
    .x-icon {
      margin-right: 0;
    }
  }
</style>
