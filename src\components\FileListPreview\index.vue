<template>
  <el-dialog v-model="dia" width="800px" title="文件列表">
    <ul id="fileList">
      <li v-for="file in fileList" :key="file.uid" class="content">
        <div class="filename">{{ file.remark || file.fileName }} </div>
        <div class="action">
          <el-link
            v-loading="file.loading"
            :underline="false"
            icon="Download"
            @click="downLoadFile(file)"
          >
            下载
          </el-link>
          <el-link
            v-if="canPreview(file)"
            icon="View"
            :underline="false"
            @click="previewFile(file)"
          >
            预览
          </el-link>
        </div>
      </li>
    </ul>
    <!-- 图片预览 -->
    <el-image-viewer v-if="!!imageUrl.length" :url-list="imageUrl" @close="imageUrl = []" />
    <!-- pdf预览 -->
    <pdf-view ref="refPdf" />
  </el-dialog>
</template>
<script>
  export default {
    name: 'FileListPreview',
    data() {
      return {
        fileList: [],
        dia: false,
        imageUrl: []
      }
    },
    mounted() {},
    methods: {
      downLoadFile(file) {
        file.loading = true
        this.$download.resource(file.fileName, file.remark).finally(() => {
          file.loading = false
        })
      },
      show(list = []) {
        if (!list.length) return this.$modal.msgWarning('暂无文件')
        this.fileList = list
        this.dia = true
      },
      canPreview({ fileName = '' }) {
        let fileType = fileName.split('.').pop()
        return [
          'jpeg',
          'jpg',
          'webp',
          'png',
          'xlsx',
          'pdf',
          'mp3',
          'wav',
          'mp4',
          'mid',
          'docx',
          'doc',
          'ppt',
          'pptx',
          'pages',
          'xlsx'
        ].includes(fileType)
      },
      previewFile({ fileName, filePath, remark }) {
        let fileType = fileName.split('.').pop()
        if (['jpeg', 'jpg', 'webp', 'png'].includes(fileType)) {
          this.imageUrl = [filePath]
        }
        if (['pdf', 'mp3', 'wav', 'mid', 'mp4'].includes(fileType)) {
          this.$refs.refPdf.show(filePath, remark)
        }
        if (['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'excel'].includes(fileType)) {
          this.$refs.refPdf.show(filePath, remark)
        }
        // let filePreviewUrl = import.meta.env.VITE_PREVIEWURL + Base64(filePath)
      }
    }
  }
</script>
<style lang="less" scoped>
  #fileList {
    .content {
      padding: 5px 10px;
      margin-bottom: 8px;
      display: flex;
      flex-direction: row;
      background-color: #f5f5f5;
      &:hover {
        background-color: #efefef;
      }
      .filename {
        flex: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin-right: 1em;
      }
    }
  }
</style>
