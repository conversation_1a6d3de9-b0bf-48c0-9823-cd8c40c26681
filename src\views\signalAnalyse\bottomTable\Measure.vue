<script setup>
  import { round } from 'lodash'
  import { signalParamTypes } from '@/constant/types'

  const props = defineProps({
    paramList: {
      type: Object,
      default: () => {}
    }
  })

  const tableList = computed(() => {
    if (!props.paramList || typeof props.paramList !== 'object') {
      return []
    }
    return Object.keys(signalParamTypes).map(key => {
      const param = props.paramList[key]

      if (!param || typeof param !== 'object' || Object.keys(param).length === 0) {
        return {
          name: signalParamTypes[key] || '',
          value: null,
          max: null,
          min: null,
          average: null
        }
      }

      let val = param.value
      let max = param.max
      let min = param.min
      let average = param.average

      if (key === 'centerFrequency') {
        val = round(val / 1e6, 3)
        max = round(max / 1e6, 3)
        min = round(min / 1e6, 3)
        average = round(average / 1e6, 3)
      } else if (key === 'signalLevel') {
        val = val.toFixed(2)
        max = max.toFixed(2)
        min = min.toFixed(2)
        average = average.toFixed(2)
      } else if (
        key === 'frequencyOffset' ||
        key === 'bbandwidth' ||
        key === 'xdbBandwidth' ||
        key === 'codeRate' ||
        key === 'transferRate'
      ) {
        val = round(val / 1e3, 3)
        max = round(max / 1e3, 3)
        min = round(min / 1e3, 3)
        average = round(average / 1e3, 3)
      } else if (key === 'modulationDegree') {
        val = round(val * 100, 2) + '%'
        max = round(max * 100, 2) + '%'
        min = round(min * 100, 2) + '%'
        average = round(average * 100, 2) + '%'
      }
      return {
        name: signalParamTypes[key] || key,
        value: val,
        max: max,
        min: min,
        average: average
      }
    })
  })
</script>
<template>
  <el-table border :data="tableList">
    <el-table-column label="序号" type="index" width="60" align="center" />
    <el-table-column label="参数名称" prop="name" align="center" />
    <el-table-column label="瞬时值" prop="value" align="center" />
    <el-table-column label="最大值" prop="max" align="center" />
    <el-table-column label="最小值" prop="min" align="center" />
    <el-table-column label="平均值" prop="average" align="center" />
  </el-table>
</template>
