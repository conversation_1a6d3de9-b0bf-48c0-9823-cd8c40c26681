e<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="8em"
    >
      <el-form-item label="问题发起人" prop="doubtUser">
        <el-input
          v-model="queryParams.doubtUser"
          placeholder="请输入问题发起人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="解答人" prop="answerUser">
        <el-input
          v-model="queryParams.answerUser"
          placeholder="请输入解答人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="creatTime">
        <el-date-picker
          v-model="queryParams.creatTime"
          clearable
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择创建时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:answer:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:answer:edit']"
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:answer:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:answer:export']"
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar v-model:show-search="showSearch" @queryTable="getList" />
    </el-row>
    <div class="fit-table">
      <el-table
        v-loading="loading"
        border
        stripe
        :data="answerList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" prop="answerId" />
        <el-table-column label="知识点id" align="center" prop="knowId" />
        <el-table-column label="问题发起人" align="center" prop="doubtUser" />
        <el-table-column label="问题" align="center" prop="doubt" />
        <el-table-column label="解答人" align="center" prop="answerUser" />
        <el-table-column label="解答内容" align="center" prop="answer" />
        <el-table-column label="创建时间" align="center" prop="creatTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(row.creatTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <el-button
              v-hasPermi="['business:answer:edit']"
              type="text"
              icon="Edit"
              @click="handleUpdate(row)"
            >
              修改
            </el-button>
            <el-button
              v-hasPermi="['business:answer:remove']"
              type="text"
              icon="Delete"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改答疑互动记录对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="8em">
        <el-form-item label="问题发起人" prop="doubtUser">
          <el-input v-model="form.doubtUser" placeholder="请输入问题发起人" />
        </el-form-item>
        <el-form-item label="解答人" prop="answerUser">
          <el-input v-model="form.answerUser" placeholder="请输入解答人" />
        </el-form-item>
        <el-form-item label="创建时间" prop="creatTime">
          <el-date-picker
            v-model="form.creatTime"
            clearable
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择创建时间"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { listAnswer, getAnswer, delAnswer, addAnswer, updateAnswer } from '@/api/business/answer'

  export default {
    name: 'Answer',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 答疑互动记录表格数据
        answerList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          knowId: null,
          doubtUser: null,
          doubt: null,
          answerUser: null,
          answer: null,
          creatTime: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {}
      }
    },
    created() {
      this.getList()
    },
    methods: {
      /** 查询答疑互动记录列表 */
      getList() {
        this.loading = true
        listAnswer(this.queryParams).then(response => {
          this.answerList = response.rows
          this.total = response.total
          this.loading = false
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          answerId: null,
          knowId: null,
          doubtUser: null,
          doubt: null,
          answerUser: null,
          answer: null,
          creatTime: null,
          delFlag: null,
          remark: null
        }
        this.resetForm('form')
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryForm')
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.answerId)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset()
        this.open = true
        this.title = '添加答疑互动记录'
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset()
        const answerId = row.answerId || this.ids
        getAnswer(answerId).then(response => {
          this.form = response.data
          this.open = true
          this.title = '修改答疑互动记录'
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.answerId != null) {
              updateAnswer(this.form).then(response => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
            } else {
              addAnswer(this.form).then(response => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const answerIds = row.answerId || this.ids
        this.$modal
          .confirm('是否确认删除答疑互动记录编号为"' + answerIds + '"的数据项？')
          .then(function () {
            return delAnswer(answerIds)
          })
          .then(() => {
            this.getList()
            this.$modal.msgSuccess('删除成功')
          })
          .catch(() => {})
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download(
          'business/answer/export',
          {
            ...this.queryParams
          },
          `answer_${new Date().getTime()}.xlsx`
        )
      }
    }
  }
</script>
