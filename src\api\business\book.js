import request from '@/utils/request'

// 查询教材信息列表
export function listBook (query) {
  return request({
    url: '/business/book/list',
    method: 'get',
    params: query
  })
}

// 查询课程信息详细
export function getBook (boId) {
  return request({
    url: '/business/book/' + boId,
    method: 'get'
  })
}

// 新增课程信息
export function addBook (data) {
  return request({
    url: '/business/book',
    method: 'post',
    data: data
  })
}

// 修改课程信息
export function updateBook (data) {
  return request({
    url: '/business/book ',
    method: 'put',
    data: data
  })
}

// 删除课程信息
export function delbook (boId) {
  return request({
    url: '/business/book/' + boId,
    method: 'delete'
  })
}

// 复制课程
export function copyCourse (data) {
  return request({
    url: '/business/course/copySrCourse',
    method: 'post',
    data
  })
}
