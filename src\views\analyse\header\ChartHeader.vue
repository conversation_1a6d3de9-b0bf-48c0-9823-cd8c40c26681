<template>
  <div class="chart-bar">
    <div class="chart-title">{{ title }}</div>
    <div class="chart-tool">
      <slot name="tool" />
      <el-tooltip content="模块" placement="top" effect="light">
        <el-dropdown v-if="name" trigger="click" class="ml-4">
          <x-icon icon="dir" source="cus" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="item in dropdownList"
                :key="item.value"
                @click="eventHandler(item.value)"
              >
                <span>{{ item.title }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-tooltip>
      <x-icon class="chart-tool-item closeable" icon="close" source="el" @click="closeChart" />
    </div>
  </div>
</template>

<script setup>
  import useChartsStore from '@/store/modules/charts'
  import routeChart from '@/common/chartInstances'

  const props = defineProps({
    title: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    }
  })

  const route = useRoute()
  const chartsStore = useChartsStore()
  const dropdownList = [
    { title: '全屏', value: 'fullScreen' },
    { title: '峰值搜索', value: 'max' },
    { title: '最小值搜索', value: 'min' },
    { title: '导出', value: 'exportExcel' },
    { title: '保存为图片', value: 'exportPic' }
  ]
  const eventHandler = async val => {
    const { instance, name } = await routeChart.query(props.name)
    if (!instance) {
      return
    }
    switch (val) {
      case 'fullScreen':
        instance.fullscreen.open()
        break
      case 'exportExcel':
        instance.downloadCSV()
        break
      case 'exportPic':
        instance.exportChart({ filename: name })
        break
      case 'min':
        addPeakMarker(instance, val)
        break
      case 'max':
        addPeakMarker(instance, val)
        break
      default:
    }
  }
  // 极值标注
  const addPeakMarker = (instance, val) => {
    const data = instance.getDataRows()
    const peakvalue = getPeak(data, val)
    const { series } = instance
    let sIndex = 0
    const len = series[0].length
    while (peakvalue.index > len) {
      peakvalue.index = peakvalue.index - len
    }
    const point = series[sIndex].points[peakvalue.index]
    if (!point) return
    instance.customMarker.markerPoint(point, [sIndex])
  }
  const getPeak = (data, type) => {
    let target = data[1],
      index = 0
    for (let i = 2; i < data.length; i++) {
      const item = data[i]
      if (type === 'min' && target[1] > item[1]) {
        target = item
        index = i - 1
      }
      if (type === 'max' && target[1] < item[1]) {
        target = item
        index = i - 1
      }
    }
    return { target, index }
  }
  const closeChart = () => {
    let views = []
    if (route.name === 'ZeroSpan') {
      views = chartsStore.zeroSpanViews
    } else if (route.name === 'DigitalModulation') {
      views = chartsStore.dmViews
    }
    const index = views.findIndex(view => view.name.toLowerCase() === props.name.toLowerCase())
    if (index >= 0) {
      views.splice(index, 1)
    }
  }
</script>

<style scoped>
  .chart-bar {
    align-items: center;
    padding: 8px 12px;
    background: var(--chart-bar-bg);
    color: var(--chart-text-color);
  }
</style>
