import request from '@/utils/request'

// 查询学生参课信息列表
export function listAttend(query) {
  return request({
    url: '/business/attend/list',
    method: 'get',
    params: query
  })
}

// 查询学生参课信息详细
export function getAttend(attId) {
  return request({
    url: '/business/attend/' + attId,
    method: 'get'
  })
}

// 新增学生参课信息
export function addAttend(data) {
  return request({
    url: '/business/attend',
    method: 'post',
    data: data
  })
}

// 修改学生参课信息
export function updateAttend(data) {
  return request({
    url: '/business/attend',
    method: 'put',
    data: data
  })
}

// 删除学生参课信息
export function delAttend(attId) {
  return request({
    url: '/business/attend/' + attId,
    method: 'delete'
  })
}
