<template>
  <div class="left-tool">
    <span class="header-title">专题图层</span>
    <el-tooltip
      class="box-item"
      content="菜单"
      placement="bottom"
    >
      <Menu class="right-menu" @click="iscollapse = !iscollapse" />  
    </el-tooltip>
  </div>
  <div v-show="iscollapse" class="title-choose">
    <div class="title-left">
      <ul>
        <li :class="{ active: isActive}" class="static" @click="changeTitle">
          <div class="specialNav" title="专题" />
        </li>
        <li :class="{ active: isSearch}" class="static" @click="changeTitle">
          <div class="specialSearch" title="搜索" />
        </li>
      </ul>
    </div>
    <div v-if="isTitleShow" class="title-right">
      <div>
        <h3 class="title-style">专题</h3>
      </div>
      <br>
      <div class="demo-collapse">
        <el-collapse v-model="activeNames" @change="handleChange">
          <el-collapse-item title="文化教育" name="1">
            <el-checkbox-group v-model="education" class="checkbox-group-vertical" @change="getSelectedLabels(1)">
              <el-checkbox label="幼儿园">幼儿园</el-checkbox>
              <el-checkbox label="小学">小学</el-checkbox>
              <el-checkbox label="中学">中学</el-checkbox>
              <el-checkbox label="高等院校">高等院校</el-checkbox>
            </el-checkbox-group>
          </el-collapse-item>
          <el-collapse-item title="医疗卫生" name="2">
            <el-checkbox-group v-model="treatment" class="checkbox-group-vertical" @change="getSelectedLabels(2)">
              <el-checkbox label="综合医院">综合医院</el-checkbox>
              <el-checkbox label="专科医院">专科医院</el-checkbox>
              <el-checkbox label="诊所">诊所</el-checkbox>
            </el-checkbox-group>
          </el-collapse-item>
          <el-collapse-item title="玄奘之路" name="3">
            <el-checkbox-group v-model="road" class="checkbox-group-vertical" @change="getSelectedLabels(3)">
              <el-checkbox label="玄奘西行之路">玄奘西行之路</el-checkbox>
              <el-checkbox label="玄奘西行途径城市">玄奘西行途径城市</el-checkbox>
            </el-checkbox-group>
          </el-collapse-item>
          <el-collapse-item title="丝绸之路" name="4">
            <el-checkbox-group v-model="silkRoad" class="checkbox-group-vertical" @change="getSelectedLabels(4)">
              <el-checkbox label="海陆、丝绸之路">海陆、丝绸之路</el-checkbox>
              <el-checkbox label="丝路城镇及地区">丝路城镇及地区</el-checkbox>
              <el-checkbox label="文化遗址">文化遗址</el-checkbox>
              <el-checkbox label="古丝绸之路">古丝绸之路</el-checkbox>
            </el-checkbox-group>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <div v-if="isSearchShow" class="title-right">
      <h3 class="title-style">搜索</h3>
      <br>
      <el-input v-model="searchValue" class="search-input" />
      <el-button :icon="Search" class="search-button" @click="sendData" />
    </div>
  </div>
</template>
<script setup>
import { Search } from '@element-plus/icons-vue'
const activeNames = ref(['1'])
const isTitleShow = ref(true)
const isSearchShow = ref(false)
const isActive = ref(true)
const isSearch = ref(false)
const iscollapse = ref(false)
//专题多选框
const education = ref([])
const treatment = ref([])
const road = ref([])
const silkRoad = ref([]) // 存储选中的值
const searchValue = ref('')
const selectedLabels = ref([])
const checkbox = ref('')
const emits = defineEmits(['data']);
const changeTitle = () => {
  isActive.value = !isActive.value
  isSearch.value = !isSearch.value
  isTitleShow.value = !isTitleShow.value
  isSearchShow.value = !isSearchShow.value
}
const sendData = () => {
  emits('data', searchValue,selectedLabels);
  
};
const getSelectedLabels = (index) => {
  switch (index) {
    case 1:
      {
         selectedLabels.value = education.value.map(value => {
           checkbox.value = education.value.find(item => item === value);
          return checkbox;
        });
      }
      break;
      case 2:
      {
         selectedLabels.value = treatment.value.map(value => {
           checkbox.value = treatment.value.find(item => item === value);
          return checkbox;
        });
      }
      break;
      case 3:
      {
         selectedLabels.value = road.value.map(value => {
           checkbox.value = road.value.find(item => item === value);
          return checkbox;
        });
      }
      break;
      case 4:
      {
         selectedLabels.value = silkRoad.value.map(value => {
           checkbox.value = silkRoad.value.find(item => item === value);
          return checkbox;
        });
      }
      break;
    default:
      break;
  }
  console.log(selectedLabels)
  // sendData()
};
const handleChange = (val) => {
  console.log(val);
}
</script>
<style scoped>
.title-choose{
  position: absolute;
  top: 73px;
  left: 19px;
  z-index:1005;
  width: 13.7%;
  height: 76%;
  background: #fff;
  overflow: hidden;
  border-radius: 3px;
  box-shadow: 2px 5px 5px rgba(0,0,0,.3);
  -webkit-transition: .3s;
  transition: .3s;
}
.left-tool{
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1005;
  width: 13.7%;
  height: 50px;
  background: #fff;
  overflow: hidden;
  border-radius: 3px;
  box-shadow: 2px 5px 5px rgba(0,0,0,.3);
  -webkit-transition: .3s;
  transition: .3s;
}
.header-title{
  float: left;
  margin: 12px 0px 0 20px;
  width: 125px;
  height: 24px;
  color: #333;
}

.right-menu{
  float: right;
  margin: 11px 9px;
  width: 26px;
  height: 24px;
  /* border: none; */
  cursor: pointer;
}
.title-left{
  float: left;
  width: 20%;
  height:100%;
  border-right: 1px solid #eee;
}
.title-right{
  float: right;
  width: 80%;
  height:100%;
}
.specialNav{
  position: absolute;
  top: 20px;
  left: 20px;
  width: 20px;
  height: 20px;
  background: url(../../../assets/images/subject.png) 0px 0 no-repeat;
}
.specialSearch{
  position: absolute;
  top: 70px;
  left: 20px;
  width: 20px;
  height: 20px;
  background: url(../../../assets/images/search.png) 0px 0 no-repeat;
}
.static{
  display: block;
  width: 58px;
  height: 55px;
  cursor: pointer;
}
.active{
  background: #337fe5;
  
}
.search-input{
  width: 66%;
  position: absolute;
  top: 32px;
  left: 70px;
  margin:5px 0 0 0;
}
.search-button{
  position: absolute;
  right: 0;
  top: 37px;
}
.title-style{
  float: left;
  color: #333;
  font-weight: 400;
  font-size: 17px;
}
.demo-collapse{
  margin:5px 0 0 0;
  overflow-y: scroll;
  height: 100%;
  color: #333;
}
.checkbox-group-vertical .el-checkbox {
  display: block;
  margin:10px 0 ;   
}
.el-collapse-item__content{
  margin-bottom: 0;
}

</style>