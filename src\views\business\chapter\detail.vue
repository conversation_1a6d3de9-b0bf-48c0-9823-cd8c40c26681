<template>
  <el-dialog v-model="open" :title="title" width="800px" append-to-body>
    <el-form
      ref="chapterRef"
      class="mr-4 common-form"
      :model="form"
      :rules="rules"
      label-width="10em"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="所属课程" prop="courseId">
            <cu-select
              v-model="form.courseId"
              placeholder="请选择所属课程"
              :query-info="`business/course/list?pageNum=1&pageSize=100`"
              :replace-fields="{ label: 'courName', value: 'courId' }"
              @change="changeCour"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="父章节" prop="chapterPid">
            <el-tree-select
              v-model="form.chapterPid"
              :data="chapterOptions"
              :props="{ value: 'chapterId', label: 'chapterName', children: 'children' }"
              value-key="chapterId"
              placeholder="请选择父章节编号"
              check-strictly
              default-expand-all
              :current-node-key="selectedKey"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="章节编码" prop="chapterCode">
            <el-input v-model="form.chapterCode" placeholder="请输入章节编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="章节名称" prop="chapterName">
            <el-input v-model="form.chapterName" placeholder="请输入章节名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="显示顺序" prop="orderNum">
            <el-input-number
              v-model="form.orderNum"
              :min="0"
              :precision="0"
              :max="999"
              placeholder="请输入显示顺序"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="详情" prop="remark">
            <el-input
              v-model="form.remark"
              maxlength="500"
              placeholder="请输入备注"
              show-word-limit
              rows="5"
              type="textarea"
            />
            <!-- <el-input v-model="form.remark" type="textarea"  rows="20"  :maxlength="500"  placeholder="请输入备注" /> -->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
  import { listChapter, getChapter, addChapter, updateChapter } from '@/api/business/chapter'
  export default {
    name: 'ComponentName',
    components: {},
    emits: ['act-success'],
    data() {
      return {
        chapterOptions: [],
        form: {},
        open: false,
        title: '',
        selectedKey: '',
        rules: {
          courseId: [{ required: true, message: '请选择所属课程' }],
          chapterCode: [{ required: true, message: '请输入章节编码' }],
          chapterName: [{ required: true, message: '请输入章节名称' }]
        }
      }
    },
    methods: {
      changeCour({ value }) {
        this.getTreeselect(value)
      },
      /** 查询课程章节信息下拉树结构 */
      async getTreeselect(courseId) {
        await listChapter({ courseId }).then(response => {
          this.chapterOptions = []
          const data = { chapterId: '0', chapterName: '顶级节点', children: [] }
          data.children = this.handleTree(response.data, 'chapterId', 'chapterPid')
          this.chapterOptions.push(data)
        })
      },
      async show({ chapterPid = '0', chapterId, courseId = '' }) {
        this.open = true
        this.form = {
          chapterPid,
          courseId
        }
        courseId && (this.form.courseId = courseId)
        await this.getTreeselect(courseId)
        this.chapterPid = chapterPid
        this.title = !chapterId ? '新增课程章节信息' : '修改课程章节信息'
        chapterId && this.getInfo(chapterId)
      },
      getInfo(chapterId) {
        this.loading = true
        getChapter(chapterId)
          .then(response => {
            this.form = response.data
            this.selectedKey = this.form.chapterPid
          })
          .finally(() => {
            this.loading = false
          })
      },
      // 取消按钮
      cancel(t = false) {
        this.open = false
        this.form = {}
        t && this.$emit('act-success')
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs['chapterRef'].validate(valid => {
          if (valid) {
            let act = !!this.form.chapterId ? updateChapter : addChapter
            act(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.cancel(true)
            })
          }
        })
      }
    }
  }
</script>
