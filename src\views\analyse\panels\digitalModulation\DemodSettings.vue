<template>
  <data-form :model="model" :fields="fields" />
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import { AM_UNIT, RATE_UNIT } from '@/constant'
  import { rateUnits, levelUnits } from '@/constant/units'
  import ModelData from '@/common/classes/modelData'
  import { FILE_TYPES } from '@/constant/file'

  const filterList = [
    { label: '升余弦', value: 0 },
    { label: '根升余弦', value: 1 },
    { label: '高斯', value: 3 },
    { label: '矩形滤波器', value: 4 }
  ]

  const fields = [
    {
      target: 'centerFreqIn',
      name: '中心频率',
      options: rateUnits,
      appendSelect: true,
      unitType: RATE_UNIT.key
    },
    {
      target: 'freqStep',
      name: '频率步进',
      options: rateUnits,
      appendSelect: true,
      unitType: RATE_UNIT.key
    },
    {
      target: 'inputPower',
      name: '输入功率',
      options: levelUnits,
      appendSelect: true,
      unitType: AM_UNIT.key
    },
    {
      target: 'bitRate',
      name: '码速率',
      options: rateUnits,
      appendSelect: true,
      unitType: RATE_UNIT.key
    },
    { target: 'resultLen', name: '结果长度', appendText: 'syms' },
    {
      target: 'debugMode',
      name: '调制',
      options: FILE_TYPES,
      isSelect: true,
      appendBtnText: '调制编辑'
    },
    { target: 'filter', name: '滤波器', options: filterList, isSelect: true },
    { target: 'filterAlpha', name: '滤波器滚降系数' },
    { target: 'autoIfBw', name: '自动中频带宽', isCheckbox: true },
    {
      target: 'intermediateFrequencyBandwidth',
      name: '中频带宽',
      options: rateUnits,
      appendSelect: true,
      unitType: RATE_UNIT.key
    },
    { target: 'iqOvertrun', name: 'I/Q翻转', isCheckbox: true },
    { target: 'corvage', name: '平均', isCheckbox: true },
    { target: 'corvageNum', name: '平均次数' },
    { target: 'ptsSym', name: 'Pts/Sym' },
    { target: 'iqOffset', name: 'I/Q 偏移', isCheckbox: true },
    { target: 'amFading', name: '幅度衰落', isCheckbox: true }
  ]

  const model = reactive(new ModelData(fields, 'dm'))
</script>
