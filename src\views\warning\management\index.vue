<template>
  <div v-loading="loading" class="app-container">
    <div class="ptitle">告警设置</div>
    <div>
      <div>告警方式</div>
      <el-checkbox-group v-model="warnModeValue">
        <el-checkbox v-for="dict in warn_mng_mode" :key="dict.value" :label="dict.value">
          {{ dict.label }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div>
      <div>告警类型</div>
      <div class="flex flex-col gap-y-2">
        <div class="flex">
          <el-checkbox v-model="typeBlack" :label="warn_mng_type[0]?.label"></el-checkbox>
        </div>
        <div class="flex gap-x-8">
          <el-checkbox v-model="typeSignalTemp" :label="warn_mng_type[1]?.label"></el-checkbox>
          <div class="flex items-center justify-center w-[320px] gap-x-3">
            <el-input v-model="tempName" placeholder="请选择模板" disabled> </el-input>
            <el-button @click="openTemp">加载模板</el-button>
          </div>
        </div>
        <div class="flex gap-x-8">
          <el-checkbox v-model="typeSignalPower" :label="warn_mng_type[2]?.label"></el-checkbox>
          <div class="flex items-center justify-center w-[320px]">
            <div class="w-28">告警功率</div>
            <el-input v-model="signalPowerData" type="number" placeholder="请输入告警功率">
              <template #append>dBuv</template>
            </el-input>
          </div>
        </div>
      </div>
    </div>
    <div class="btnGroup">
      <el-button type="primary" @click="handleConfirm">确定</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>

  <el-dialog v-model="open" title="信号模板" width="900px" class="min-h-[320px]" append-to-body>
    <vxe-table
      ref="xTable"
      border
      round
      :radio-config="{ labelField: 'listId', highlight: true }"
      :data="signalList"
      @radio-change="handleRadioChange"
    >
      <vxe-column type="radio" title="序号" width="55" align="center" />
      <vxe-column field="name" title="模板名称" align="center" />
      <vxe-column field="centerFreq" title="中心频率（MHz）" align="center">
        <template #default="{ row }">
          {{ (row.centerFreq / 1000000).toFixed(2) }}
        </template>
      </vxe-column>
      <vxe-column field="signalBandwidth" title="信号带宽（KHz）" align="center">
        <template #default="{ row }">
          {{ (row.signalBandwidth / 1000).toFixed(2) }}
        </template>
      </vxe-column>
      <vxe-column field="step" title="步长" align="center" />
      <vxe-column field="fileName" title="模板文件" align="center" />
    </vxe-table>

    <pagination
      v-show="tempTotal > 0"
      v-model:page="tempQueryParams.pageNum"
      v-model:limit="tempQueryParams.pageSize"
      :total="tempTotal"
      @pagination="getTempList"
    />

    <template #footer>
      <div class="flex items-center justify-center">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
  import { getMngList, addOrUpdateMng } from '@/api/warning/management'
  import { getSignalTempList } from '@/api/warning/signalTemp'
  const { proxy } = getCurrentInstance()
  /**告警方式枚举 */
  const { warn_mng_mode } = proxy.useDict('warn_mng_mode')
  /**告警类型枚举 */
  const { warn_mng_type } = proxy.useDict('warn_mng_type')

  /**告警方式 */
  const warnModeValue = ref([])

  /**告警类型 */
  const typeBlack = ref(false) //黑名单警告
  const typeSignalPower = ref(false) //信号功率警告
  const typeSignalTemp = ref(false) // 信号模板警告

  const signalPowerData = ref(null) //告警功率

  const loading = ref(true)

  const mngData = ref([])

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 1,
    name: ''
  })

  const signalList = ref([]) // 信号模板列表
  const open = ref(false) // 模板弹窗
  const dialogLoading = ref(true) // 模板列表加载状态
  const selectedRow = ref(null) // 用来存储被选中的行
  const tempQueryParams = ref({
    pageNum: 1,
    pageSize: 10
  })
  const tempTotal = ref(0) // 模板列表总条数

  const tempName = ref('') // 模板名称

  /** 查询信号模板列表 */
  const getList = async () => {
    loading.value = true
    await getMngList(queryParams).then(response => {
      mngData.value = response.data.list?.[0] || {}
      if (mngData.value) {
        warnModeValue.value = []
        if (mngData.value.modeLight === 1) {
          warnModeValue.value.push('0')
        }
        if (mngData.value.modeVoice === 1) {
          warnModeValue.value.push('1')
        }
        mngData.value.typeBlack === 1 ? (typeBlack.value = true) : (typeBlack.value = false)
        mngData.value.typeSignalPower === 1
          ? (typeSignalPower.value = true)
          : (typeSignalPower.value = false)
        mngData.value.typeSignalTemp === 1
          ? (typeSignalTemp.value = true)
          : (typeSignalTemp.value = false)
        signalPowerData.value = mngData.value.signalPowerData
        tempName.value = mngData.value.signalTempName
      }
      loading.value = false
    })
  }

  const handleConfirm = () => {
    if (typeSignalTemp.value && !selectedRow.value) {
      proxy.$message.error('请选择模板')
      return
    }
    if (typeSignalPower.value && !signalPowerData.value) {
      proxy.$message.error('请输入告警功率')
      return
    }
    const warnMode = Object.values(warnModeValue.value)
    const submitData = {
      modeLight: warnMode.includes('0') ? '1' : '0',
      modeVoice: warnMode.includes('1') ? '1' : '0',
      typeBlack: typeBlack.value ? '1' : '0',
      typeSignalPower: typeSignalPower.value ? '1' : '0',
      typeSignalTemp: typeSignalTemp.value ? '1' : '0',
      signalTempId: selectedRow.value?.id || null,
      signalPowerData: signalPowerData.value || null
    }
    if (mngData.value?.id) {
      submitData.id = mngData.value.id
    }

    addOrUpdateMng(submitData).then(response => {
      proxy.$modal.msgSuccess('修改成功')
      open.value = false
      getList()
    })
  }
  const handleCancel = () => {
    console.log('取消')
  }

  const xTable = ref(null)
  /** 信号模板弹窗打开 */
  const openTemp = () => {
    open.value = true
    // 确保数据加载后选中默认行
    nextTick(() => {
      setDefaultSelection()
    })
  }

  const handleRadioChange = ({ row }) => {
    selectedRow.value = row
  }
  /** 查询信号模板列表 */
  const getTempList = async () => {
    dialogLoading.value = true
    await getSignalTempList(tempQueryParams.value).then(response => {
      signalList.value = response.data.list
      tempTotal.value = response.data.total || 0
      dialogLoading.value = false
    })
  }

  // 设置默认选中行的函数
  const setDefaultSelection = () => {
    if (signalList.value && signalList.value.length > 0) {
      const defaultRow = signalList.value.find(item => item.id === mngData.value.signalTempId) // 假设你根据某个 ID 来确定默认选中
      selectedRow.value = defaultRow
      if (defaultRow) {
        xTable.value.setRadioRow(defaultRow, true)
      }
    }
  }

  const submitForm = () => {
    tempName.value = selectedRow.value.name
    open.value = false
  }
  /** 取消按钮 */
  const cancel = () => {
    open.value = false
    selectedRow.value = null
  }

  onMounted(() => {
    getTempList()
    getList()
  })
</script>
<style lang="less" scoped>
  .btnGroup {
    margin-top: 100px;
    text-align: center;
  }
</style>
