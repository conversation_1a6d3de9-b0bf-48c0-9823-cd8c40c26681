<template>
  <el-tooltip content="选择类型" placement="top" effect="light">
    <el-dropdown trigger="click" class="ml-4">
      <x-icon icon="menu" size="20" source="el" />
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item in typeItems" :key="item.value" @click="renderView(item)">
            {{ item.title }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </el-tooltip>
  <el-tooltip content="模块" placement="top" effect="light">
    <el-dropdown trigger="click" class="ml-4">
      <x-icon icon="fold" size="20" source="el" />
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item in viewItems" :key="item.value" @click="renderView(item)">
            <div style="width: 24px">
              <x-icon v-show="isInView(item.value)" icon="check" source="el" />
            </div>
            {{ item.title }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </el-tooltip>
</template>

<script setup>
  import useChartsStore from '@/store/modules/charts'
  import { queryConstellationPlots } from '@/api/charts'

  const chartsStore = useChartsStore()
  const chartData = ref([]) // 星座图和符号表数据
  const map = { comp: {}, data: {} }
  const typeItems = [
    { title: '基础分析', value: 0 },
    { title: 'PSK/OAM 分析', value: 1 },
    { title: 'FSK 分析', value: 2 }
  ]
  const viewItems = [
    { title: '星座图点', value: 'ConstellationPlot' },
    // { title: '误差向量幅度', value: 1 },
    { title: '频谱绘制', value: 'Spectrum' },
    { title: '眼图', value: 'Eye' },
    { title: '误差向量幅度 vs 时间', value: 'EvmVsTime' },
    { title: '幅度误差 vs 时间', value: 'AmLinearVsTime' },
    // { title: 'FSK 误差', value: 6 },
    // { title: 'FSK 测量', value: 7 },
    { title: '解调比特流', value: 'SymbolTable' },
    { title: '幅度调制 vs 时间', value: 'AmVsTime' },
    { title: '互补累计分析函数', value: 'Ccdf' },
    // { title: 'Eq Impluse', value: 10 },
    // { title: 'Eq Mag Response', value: 11 },
    // { title: 'Eq Phase Response', value: 12 }
  ]
  const body = chartsStore.getFileInfo()
  const init = async () => {
    const { data } = await queryConstellationPlots(body)
    chartData.value = data
  }
  const renderView = item => {
    if (chartsStore.dmViews.find(v => v.name === item.value)) {
      return
    }
    if (!map.comp[item.value]) {
      switch (item.value) {
        case 'ConstellationPlot':
          map.comp[item.value] = defineAsyncComponent(() =>
            import('@/views/analyse/modules/ConstellationPlot')
          )
          map.data[item.value] = chartData.value.map.demoData
          break
        case 'Spectrum':
          map.comp[item.value] = defineAsyncComponent(() =>
            import('@/views/analyse/modules/Spectrum')
          )
          break
        case 'Eye':
          map.comp[item.value] = defineAsyncComponent(() => import('@/views/analyse/modules/Eye'))
          map.data[item.value] = chartData.value.finalEyePlotFuncEntity
          break
        case 'EvmVsTime':
          map.comp[item.value] = defineAsyncComponent(() =>
            import('@/views/analyse/modules/EvmVsTime')
          )
          break
        case 'AmLinearVsTime':
          map.comp[item.value] = defineAsyncComponent(() =>
            import('@/views/analyse/modules/AmLinearVsTime')
          )
          break
        case 'AmVsTime':
          map.comp[item.value] = defineAsyncComponent(() =>
            import('@/views/analyse/modules/AmVsTime')
          )
          break
        case 'Ccdf':
          map.comp[item.value] = defineAsyncComponent(() => import('@/views/analyse/modules/Ccdf'))
          break
        case 'SymbolTable':
          map.comp[item.value] = defineAsyncComponent(() => import('@/views/analyse/modules/SymbolTable'))
          map.data[item.value] = chartData.value.map.ints
          break
        default:
      }
      if (!map.comp[item.value]) {
        console.log('目前尚未该图表')
        return
      }
    }
    chartsStore.dmViews.push({
      name: item.value,
      comp: map.comp[item.value],
      binding: {
        data: map.data[item.value]
      }
    })
  }
  const isInView = (val) => {
    const item = chartsStore.dmViews.find(v => v.name === val)
    return !!item
  }

  onMounted(async () => {
    await init()
    renderView({ value: 'ConstellationPlot' }, chartData.value.demoData)
    renderView({ value: 'Eye' })
    renderView({ value: 'SymbolTable' }, chartData.value.ints)
    renderView({ value: 'Spectrum' })
  })
  onBeforeUnmount(() => {
    chartsStore.dmViews = []
  })
</script>
