import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';

async function executePollingFunctions(pollingFunctions, offset) {
  let promises;
  if (Array.isArray(pollingFunctions)) {
    promises = pollingFunctions.map(fn => fn(offset));
  } else {
    promises = [pollingFunctions(offset)];
  }
  return Promise.all(promises); // 确保所有函数都执行完毕后再返回
}

export const usePlaybackStore = defineStore('playback', {
  state: () => ({
    isPlaying: false,
    startOffset: 0,
    maxOffset: 0,
    playBackTime: 64,
    interval: null,
    isRequestInProgress: false,  // 请求进行中状态
    isFetching: false,
    isFinished: false,  // 播放是否结束
    isInfinite: false,  // 是否为无限流
  }),

  actions: {
    /** 执行轮询函数的封装 */
    async handlePollingFunctions(pollingFunctions, offset) {
      if (this.isFetching || this.isRequestInProgress) return;  // 防止重复请求
      this.isFetching = true;
      this.isRequestInProgress = true; // 标记请求进行中
      try {
        // 只在播放状态下处理请求
        if (this.isPlaying) {
          await executePollingFunctions(pollingFunctions, offset);
        }
      } catch (error) {
        console.error(error);
        this.stopPlay('请求失败'); // 请求失败时停止播放
      } finally {
        this.isRequestInProgress = false; // 请求完成后，重置请求锁
        this.isFetching = false;
      }
    },

    /** 切换播放状态 */
    togglePlay(pollingFunctions) {
      this.isPlaying = !this.isPlaying;
      if (this.isPlaying) {
        ElMessage.success('开始播放');
        if (this.startOffset >= this.maxOffset) {
          this.resetPlayback(); // 若已经播放结束，重置播放状态
        }
        this.play(pollingFunctions); // 开始播放时传入偏移量
      } else {
        ElMessage.warning('暂停播放');
        this.clearTimer(); // 暂停时清除定时器
      }
    },

    /** 播放逻辑 */
    async play(pollingFunctions) {
      if (this.isFinished) {
        this.resetPlayback(); // 播放结束后重新开始
      }

      this.isPlaying = true;

      // 确保上一个轮询请求完成前不发起新的请求
      this.interval = setInterval(async () => {
        if (this.isRequestInProgress) {
          return; // 如果请求还在进行中，跳过本轮
        }
        // 特殊处理：只有一包数据的情况，startOffset 为 0 且 maxOffset 为 1，立即完成播放
        if (this.startOffset === 0 && this.maxOffset >= 1) {
          if (this.isInfinite) {
            this.resetPlayback(); // 若为无限流，重置播放状态
          } else {
            this.finishPlayback(); // 立即结束播放
            return;
          }
        }
        // 执行轮询函数
        await this.handlePollingFunctions(pollingFunctions, this.startOffset);
      }, this.playBackTime); // 使用 playBackTime 作为轮询间隔
    },

    /** 停止播放 */
    async stopPlay(tips = '停止播放') {
      if (this.isFinished) return; // 如果播放已经结束，直接返回
      if (tips) ElMessage.error(tips);
      this.clearTimer();
      this.isPlaying = false;
      this.resetPlayback(); // 重置播放状态
    },

    /** 快退操作 */
    async fastBack(pollingFunction) {
      this.stopPlay('快退');
      this.play(pollingFunction);
    },

    /** 快进操作 */
    async forwardByStep(pollingFunction) {
      this.setStartOffset(Math.min(this.startOffset + 2, this.maxOffset));  // 快进 2 个偏移量，确保不超出最大偏移
      this.restartPlayback(pollingFunction);
    },

    /** 快退操作 */
    async backByStep(pollingFunction) {
      this.setStartOffset(Math.max(this.startOffset - 2, 0));  // 快退 2 个偏移量，确保不小于 0
      this.restartPlayback(pollingFunction);
    },

    /** 刷新播放 */
    async refreshPlay(pollingFunction) {
      this.stopPlay('');
      this.resetPlayback(); // 重置播放状态
      this.play(pollingFunction);
    },

    /** 清除定时器 */
    async clearTimer() {
      if (this.interval) {
        clearInterval(this.interval);
        this.interval = null;
      }
    },

    /** 播放结束处理 */
    finishPlayback() {
      this.clearTimer();
      this.isPlaying = false;
      this.isFinished = true;
      ElMessage.info('播放完成');
    },

    /** 重置播放状态 */
    resetPlayback() {
      this.setStartOffset(0);
      this.setMaxOffset(0);
      this.isFinished = false;
    },

    /** 快进或快退后重新开始播放 */
    async restartPlayback(pollingFunction) {
      this.clearTimer();
      this.isPlaying = false;
      this.isFinished = false;
      this.play(pollingFunction);
    },

    /** 设置起始偏移量 */
    setStartOffset(value) {
      this.startOffset = value;
    },

    /** 设置最大偏移量 */
    setMaxOffset(value) {
      this.maxOffset = value;
    }
  }
});

