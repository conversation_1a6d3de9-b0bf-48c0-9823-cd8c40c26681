export default {
  chart: {
    backgroundColor: '#020e1f',
    type: 'line',
    height: 360,
    spacingTop: 10,
    spacingBottom: 20,
    spacingLeft: 10,
    spacingRight: 20,
    events: {}
  },
  accessibility: {
    enabled: false
  },
  exporting: {
    enabled: false
  },
  reflow: true,
  credits: {
    enabled: false
  },
  xAxis: {
    labels: {
      style: {
        color: '#f3f3f3'
      }
    },
    gridLineWidth: 1,
    gridLineColor: '#f3f3f3',
    gridLineDashStyle: 'dash',
    lineColor: '#f3f3f3',
    zoomEnabled: false,
    min: 0
  },
  yAxis: {
    title: {
      enabled: false
    },
    gridLineColor: '#f3f3f3',
    gridLineDashStyle: 'dash',
    lineColor: '#f3f3f3',
    labels: {
      style: {
        color: '#f3f3f3'
      }
    },
    unit: 'mV'
  },
  title: {
    text: 'I/Q 图形',
    margin: 8,
    style: {
      color: '#f3f3f3',
      fontSize: 16,
      fontWeight: 700
    }
  },
  boost: {
    useGPUTranslations: true
  },
  legend: {
    enabled: false
  },
  series: [
    {
      color: '#00ff66',
      marker: {
        enabled: false
      },
      animation: false,
      enableMouseTracking: false,
      type: 'line',
      data: [],
      lineWidth: 0.5
    },
    {
      color: 'red',
      marker: {
        enabled: false
      },
      animation: false,
      enableMouseTracking: false,
      type: 'line',
      data: [],
      lineWidth: 0.5
    }
  ]
}
