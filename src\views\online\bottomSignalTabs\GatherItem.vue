<template>
  <div class="gather-item">
    <!-- <el-form class="left">
      <el-form-item label="检波器">
        <el-select v-model="form.detector">
          <el-option
            v-for="item in checkWaveItems"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文件名前缀">
        <el-input v-model="form.fileNamePrefix" />
      </el-form-item>
      <el-form-item v-if="type !== 'channel'" :label="pickTypeLabel">
        <el-input v-model="form.extractValue">
          <template #suffix>
            {{ unit }}
          </template>
        </el-input>
      </el-form-item>
      <el-form-item v-if="type === 'channel'" label="中心频率">
        <el-input v-model="channelForm.centerFrequency">
          <template #append>
            <el-select v-model="channelForm.cfUnit">
              <el-option v-for="item in rateUnitItems" :key="item" :value="item">{{
                item
              }}</el-option>
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item v-if="type === 'channel'" label="信号带宽">
        <el-input v-model="channelForm.intermediateFrequencyBandwidth">
          <template #append>
            <el-select v-model="channelForm.bdUnit">
              <el-option v-for="item in rateUnitItems" :key="item" :value="item">{{
                item
              }}</el-option>
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item v-if="type === 'channel'" label="输出功率">
        <el-select v-model="channelForm.outputPower">
          <el-option
            v-for="item in powerItems"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form> -->
    <ul class="right">
      <li v-for="item in gatherStatus" :key="item.label" class="info-list">
        <span class="label">{{ item.label }}</span>
        <span class="value">{{ item.value }}</span>
      </li>
      <li class="mt-14 text-center">
        <el-button class="ctl-btn mr-4" :class="{ close: ws }" @click="startGather">开始</el-button>
        <el-button class="ctl-btn" :class="{ close: !ws }" @click="stopGather">结束</el-button>
      </li>
    </ul>
  </div>
</template>

<script setup>
  import useScanStore from '@/store/modules/scanMonitor'
  import { linkWs } from '@/api/upperComputer/scan'
  import { IQ_GATHER_CODE } from '@/constant/funCodes'
  import { numToPlot, plotToNum } from '@/utils/utils'
  import { getConfigKey } from '@/api/system/config'
  import { ElMessage } from 'element-plus'

  const scanStore = useScanStore()
  const sysType = ref('416')
  const spectrum = scanStore.spectrumCopy
  const checkWaveItems = [
    { label: '平均', value: 0 },
    { label: '最大保持', value: 1 }
  ]
  const rateUnitItems = ['Hz', 'KHz', 'MHz', 'GHz']
  const powerItems = [
    {
      value: 0,
      label: 'dBm(信道功率)'
    },
    {
      value: 1,
      label: 'dBm/Hz(功率谱密度)'
    }
  ]
  const props = defineProps({
    type: {
      type: String,
      default: 'time'
    }
  })

  const ws = ref(null)
  const form = ref({
    detector: 0,
    fileNamePrefix: 'SWP_REC_',
    extractValue: 1
  })
  const channelForm = reactive({
    centerFrequency: spectrum.centerFreq,
    intermediateFrequencyBandwidth: spectrum.bandwidth,
    outputPower: 0,
    cfUnit: 'Hz',
    bdUnit: 'Hz'
  })
  const gatherStatus = reactive({
    schedule: { label: '进度', value: '已保存' },
    recordNum: { label: '记录次数', value: '0次' },
    size: { label: '文件大小', value: '0B' }
  })
  const unit = computed(() => (props.type === 'time' ? '秒' : '次'))
  const pickTypeLabel = computed(() => (props.type === 'time' ? '抽取时间' : '抽取次数'))

  const codeFindInfo = inject('codeFindInfo')
  const selectForm = inject('selectForm')

  const judgeSysType = async () => {
    await getConfigKey('system.type').then(async res => {
      sysType.value = res.msg
    })
  }

  const startGather = async () => {
    // const cf = plotToNum(channelForm.centerFrequency + channelForm.cfUnit)
    // const bd = plotToNum(channelForm.intermediateFrequencyBandwidth + channelForm.bdUnit)
    const data = {
      polarization: spectrum.taskParams.polarization,
      centerFrequency: plotToNum(spectrum.centerFreq + selectForm.value.centerFreq),
      intermediateFrequencyBandwidth: plotToNum(spectrum.bandwidth + selectForm.value.bandwidth),
      samplingTime: form.value.extractValue * 1000,
      dataType: form.value.detector
    }
    // 如果是是海德设备并且带宽超过100MHz则提示错误
    if (sysType.value === 'htra' && data.intermediateFrequencyBandwidth > 100 * 1000 * 1000) {
      ElMessage.error('IQ采集带宽不能超过100MHz')
      return
    }
    ws.value = await linkWs({
      host: codeFindInfo.value.ip,
      port: codeFindInfo.value.port,
      taskFunCode: IQ_GATHER_CODE,
      sendTime: 10,
      fileNameFirst: form.value.fileNamePrefix,
      path: 0,
      CollectNumber: form.value.extractValue,
      deviceCode: codeFindInfo.value.value,
      data: encodeURIComponent(JSON.stringify(data))
    })
    ws.value.onmessage = event => {
      // console.log('cccc', event.data)
      if (!event.data) {
        return
      }
      try {
        const { data } = JSON.parse(event.data)
        gatherStatus.schedule.value = '保存中'
        gatherStatus.recordNum.value = data.result.collectNumber + '次'
        const sizeStr = numToPlot(data.result.fileLarge)
        gatherStatus.size.value = sizeStr.replace('Hz', 'B')
      } catch (err) {
        console.log(err)
      }
    }
    ws.value.onclose = event => {
      gatherStatus.schedule.value = '已保存'
    }
  }
  const stopGather = () => {
    ws.value && ws.value.close()
    ws.value = null
  }
  const handleChannelForm = () => {
    const [cf, cfUnit] = numToPlot(channelForm.centerFrequency, '-').split('-')
    const [bd, bdUnit] = numToPlot(channelForm.intermediateFrequencyBandwidth, '-').split('-')
    channelForm.centerFrequency = cf
    channelForm.intermediateFrequencyBandwidth = bd
    channelForm.cfUnit = cfUnit
    channelForm.bdUnit = bdUnit
  }

  onMounted(async () => {
    if (props.type === 'channel') {
      handleChannelForm()
    }
    await judgeSysType()
  })
</script>
