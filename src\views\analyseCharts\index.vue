<template>
  <div class="scan-charts">
    <div class="scan-toolbar">
      <div class="pd6 br1">
        <el-button v-if="viewType === 'scan'" @click="switchType">
          {{ model.type ? '离散' : '全景' }}
        </el-button>
        <el-button v-else-if="viewType === 'monitor'" @click="switchType">
          {{ model.type ? '雷达监测' : '连续波' }}
        </el-button>
        <el-button v-else>{{ model.type ? '雷达信号' : '连续波' }}</el-button>
      </div>
      <el-checkbox-group v-model="checkList" class="pd6 br1">
        <el-checkbox
          v-for="item in lineTypes"
          :key="item.value"
          v-model="item.value"
          :label="item.label"
          border
        />
      </el-checkbox-group>
      <div class="pd6 br1">
        <el-button @click="showResetZoom">全频段</el-button>
      </div>
      <div class="pd6 bl1 inline-flex" v-if="!props.model.type">
        <el-select
          v-model="selectedFreStds"
          :teleported="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          effect="dark"
          value-key="label"
        >
          <el-option
            v-for="item in frequencyStandards"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
        <el-checkbox v-model="isActive" label="激活" border />
        <el-checkbox v-model="isTopFollow" label="峰值跟踪" border />
      </div>
      <div v-if="viewType !== 'signal'" class="pd6 bl1">
        <el-button @click="switchAssit">{{ assistChartName }}</el-button>
      </div>
      <div class="pd6 br1">
        <el-button @click="switchToWarning">告警设置</el-button>
      </div>
      <div class="flex pd6 br1">
        <el-button :disabled="props.model.status === 1" @click="deviceSelf">设备自检</el-button>
      </div>
      <!-- 设备选择 -->
      <el-select
        v-if="viewType === 'scan'"
        v-model="deviceCode"
        :teleported="false"
        effect="dark"
        value-key="label"
        class="pd6 bl1 w-[120px] equip"
      >
        <el-option
          v-for="item in deviceEnum"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </el-select>
    </div>
    <div class="main" style="display: flex">
      <!-- 频谱扫描echarts -->
      <div class="scanCharts" style="flex: 1">
        <div v-if="viewType === 'radar'" class="radar-chart">
          <slot
            name="radar"
            :show-lines="checkList"
            :selected-fremarkers="selectedFreStds"
            :is-active="isActive"
            :is-top-follow="isTopFollow"
          />
        </div>
        <div v-else class="spectrum">
          <!-- 信号检测echarts -->
          <Spectrum
            ref="spectrumRef"
            :key="usekey"
            :usekey="usekey"
            :show-lines="checkList"
            :selected-fremarkers="selectedFreStds"
            :is-active="isActive"
            :is-top-follow="isTopFollow"
            :data-list="dataList"
            :model="model"
          />
        </div>

        <!-- 瀑布图 -->
        <WaterFallPlot
          v-if="assistChartType === 0"
          :is-online="true"
          :data="dataList.current"
          :height="50"
          :legend-width="60"
          :container-height="isRadarAnalyse ? 200 : 300"
        />
        <!-- 占用度 -->
        <Occupancy v-else :data="dataList.occupancy" :model="model" />
      </div>
      <slot ref="analyseRef" />
    </div>

    <device-self-test
      ref="deviceSelfTest"
      v-model:open="open"
      :host="host"
      :port="port"
      :deviceCode="deviceCode"
    />
  </div>
</template>

<script setup name="AnalyseChart">
  import useScanStore from '@/store/modules/scanMonitor'
  import Spectrum from '@/components/SpectrumSignal/Spectrum.vue'
  import WaterFallPlot from '@/views/analyse/modules/WaterFallPlot.vue'
  import Occupancy from './Occupancy.vue'
  import DeviceSelfTest from './components/deviceSelfTest.vue'
  import { lineTypes } from '@/constant'
  import { ElMessage } from 'element-plus'
  import router from '@/router'

  const props = defineProps({
    viewType: {
      type: String,
      default: 'scan' // scan 扫描  monitor 监测
    },
    dataList: {
      type: Object,
      default: () => ({})
    },
    model: {
      type: Object,
      default: () => ({})
    },
    usekey: {
      type: String,
      default: 'default'
    },
    deviceEnum: {
      type: Array,
      default: () => []
    }
  })

  const deviceSelfTest = ref(null)
  const open = ref(false)
  const host = ref('')
  const port = ref(0)

  const { dataList } = toRefs(props)
  const frequencyStandards = [
    { label: '频标1', value: 1 },
    { label: '频标2', value: 2 },
    { label: '频标3', value: 3 },
    { label: '频标4', value: 4 },
    { label: '频标5', value: 5 }
  ]

  const scanStore = useScanStore()
  const spectrum = scanStore.spectrumCopy

  const deviceCode = defineModel('deviceCode')
  let isRestoring = false // 标志位
  watch(deviceCode, (newValue, oldValue) => {
    // 如果正在恢复旧值，直接返回，避免死循环
    if (isRestoring) {
      isRestoring = false
      return
    }
    if (spectrum.status === 1) {
      ElMessage.warning('任务执行过程中，请先停止当前任务')
      // 设置标志位，并恢复旧值
      isRestoring = true
      deviceCode.value = oldValue // 恢复旧值
    } else {
      // 在此处处理允许更改的逻辑
      host.value = props.deviceEnum.find(item => item.value === newValue).ip
      port.value = props.deviceEnum.find(item => item.value === newValue).port
      console.log('Device code changed to:', newValue)
    }
  })

  watch(
    () => props.model.type,
    newValue => {
      if (newValue) {
        assistChartType.value = 1
      }
    }
  )

  // const checkList = ref(['实时', '平均', '最大', '最小', '门限'])
  const checkList = defineModel('checkList')
  const spectrumRef = ref(null)
  const selectedFreStds = ref([])
  const isActive = ref(true)
  const isTopFollow = ref(false)
  const assistChartType = ref(0)
  const assistChartName = computed(() => (assistChartType.value === 0 ? '瀑布图' : '占用度'))
  const isRadarAnalyse = computed(() => {
    // return props.model.hideTab && props.model.type // 这个代表是雷达分析
    return false
  })
  const switchType = () => {
    props.model.type = Number(!props.model.type)
    if (!props.model.type) {
      assistChartType.value = 0
    }
  }
  const switchAssit = () => {
    if (props.model.type) return
    assistChartType.value = Number(!assistChartType.value)
  }
  const showResetZoom = () => {
    spectrumRef.value.instance.zoomOut()
  }

  const switchToWarning = () => {
    router.push({ path: '/warn/management' })
  }

  const deviceSelf = () => {
    open.value = true
    deviceSelfTest.value.selfTest()
  }
</script>

<style scoped>
  .scan-charts {
    padding: 12px 8px;
  }

  .scan-toolbar {
    display: flex;
    border-top: 1px solid var(--toolbar-color);
    border-bottom: 1px solid var(--toolbar-color);
  }

  .pd6 {
    padding: 6px;
  }

  .br1 {
    border-right: 1px solid var(--toolbar-color);
  }

  .bl1 {
    border-left: 1px solid var(--toolbar-color);
  }
  :deep(.el-input__inner) {
    color: #ffffff;
  }
</style>
