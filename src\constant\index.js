export const EYE_DOT = {
  dotsNumLine: 33, // 眼图一条线几个点
  baseDotsNum: 3, // 几个点来自于后台/作为基准点
  midDotsNum: 15 // 几个点作为中间点
}

export const BINARY2_16 = {
  0: '0000',
  1: '0001',
  2: '0010',
  3: '0011',
  4: '0100',
  5: '0101',
  6: '0110',
  7: '0111',
  8: '1000',
  9: '1001',
  A: '1010',
  B: '1011',
  C: '1100',
  D: '1101',
  E: '1110',
  F: '1111'
}

// 0 代表频率 1 代表时间 2 代表幅度 dBm
export const RATE_UNIT = { key: 0, default: 'MHz' }
export const TIME_UNIT = { key: 1, default: 'us' }
export const AM_UNIT = { key: 2, default: 'dBm' }

export const lineTypes = [
  { label: '实时', value: 'current' },
  { label: '平均', value: 'average' },
  { label: '最大', value: 'max' },
  { label: '最小', value: 'min' },
  { label: '门限', value: 'limit' }
]
