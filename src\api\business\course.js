import request from '@/utils/request'

// 查询课程信息列表
export function listCourse (query) {
  return request({
    url: '/business/course/list',
    method: 'get',
    params: query
  })
}

// 查询课程信息详细
export function getCourse (courId) {
  return request({
    url: '/business/course/' + courId,
    method: 'get'
  })
}

// 新增课程信息
export function addCourse (data) {
  return request({
    url: '/business/course',
    method: 'post',
    data: data
  })
}

// 修改课程信息
export function updateCourse (data) {
  return request({
    url: '/business/course',
    method: 'put',
    data: data
  })
}

// 删除课程信息
export function delCourse (courId) {
  return request({
    url: '/business/course/' + courId,
    method: 'delete'
  })
}

// 复制课程
export function copyCourse (data) {
  return request({
    url: '/business/course/copySrCourse',
    method: 'post',
    data
  })
}

// 教材全量接口
export function searchBook (boId) {
  return request({
    url: '/business/book/' + boId,
    method: 'get'
  })
}
