<template>
  <el-dialog
    v-model="tempOpen"
    title="信号文件库"
    width="1000px"
    class="min-h-[320px]"
    append-to-body
  >
    <vxe-table
      ref="xTable"
      border
      round
      :radio-config="{ labelField: 'listId', highlight: true }"
      :data="list"
      :loading="loading"
      :keep-source="true"
      class="mytable-scrollbar"
      @radio-change="handleRadioChange"
    >
      <vxe-column type="radio" title="序号" width="120" fixed="left" />
      <vxe-column field="fileName" title="文件" width="240" type="html" fixed="left" />
      <vxe-column field="uploadTime" title="上传时间" width="180"> </vxe-column>
      <vxe-column field="fileType" title="文件类型" width="150">
        <template #default="{ row }">
          <span>{{ formatFiletype(row.fileType) }}</span>
        </template>
      </vxe-column>
      <vxe-column field="dataOrganization" title="IQ数据组织方式" width="190">
        <template #default="{ row }">
          <span>{{ formatDataorganization(row.dataOrganization) }}</span>
        </template>
      </vxe-column>
      <vxe-column field="dataType" title="IQ数据类型" width="170">
        <template #default="{ row }">
          <span>{{ formatIQDatatype(row.dataType) }}</span>
        </template></vxe-column
      >
      <vxe-column field="debugMode" title="调制方式" width="140">
        <template #default="{ row }">
          <span>{{ formatDebugmode(row.debugMode) }}</span>
        </template></vxe-column
      >
      <vxe-column field="startOffset" title="IQ数据起始字节" width="160"> </vxe-column>
      <vxe-column field="cutoffLength" title="文件末尾截断字节数" width="200"> </vxe-column>
      <vxe-column field="centerFreq" title="中心频率" width="140"> </vxe-column>
      <vxe-column field="intermediateFrequencyBandwidth" title="中频带宽" width="140"> </vxe-column>
      <vxe-column field="samplingRate" title="采样率" width="120"> </vxe-column>
      <vxe-column field="bitRate" title="码速率" width="120"> </vxe-column>
    </vxe-table>
    <!-- 分页 -->
    <p>
      <vxe-pager
        v-model:current-page="curPage"
        v-model:page-size="size"
        class="vxe-page"
        perfect
        :total="total"
        :page-sizes="[10, 20, 50, 100, 200, 500]"
        :layouts="[
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'Sizes',
          'FullJump',
          'Total'
        ]"
      />
    </p>
    <template #footer>
      <div class="flex items-center justify-center">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
  import * as mtApi from '@/api/singalManage'
  import { onMounted, ref } from 'vue'
  import useDictStore from '@/store/modules/dict'
  import useList from '@/api/tool/filemanage/tableFunciton'
  import { allSignalListGet } from '@/api/singalManage'
  const filterOption = ref({})
  const fileId = ref(0)
  const xTable = ref(null)
  const { list, loading, curPage, size, total, loadData } = useList(
    allSignalListGet,
    filterOption,
    fileId,
    xTable
  )
  const tempOpen = defineModel()
  const debugmodeMessage = ref([])
  const filetypeList = ref([])
  const dataorganizationList = ref([])
  const datatypeList = ref([])
  const debugmodeList = ref([])

  const selectedRow = ref(null) // 用于保存选中行的数据

  const handleRadioChange = ({ row }) => {
    selectedRow.value = row
  }
  onMounted(() => {
    getDictionaryData()
  })
  const cancel = () => {
    tempOpen.value = false
  }

  //获取字典数据
  const getDictionaryData = () => {
    const store = useDictStore()
    //获取文件类型
    mtApi.signalDataGet({ dictType: 'filetype' }).then(res => {
      const filetype = store.dict.filter(item => item.dictType == 'filetype')
      filetype.forEach(item => {
        filetypeList.value.push({ label: item.dictLabel, value: item.dictValue })
      })
      filetypeList.value.sort((a, b) => {
        const valueA = parseInt(a.value)
        const valueB = parseInt(b.value)
        if (valueA < valueB) {
          return -1
        } else if (valueA > valueB) {
          return 1
        } else {
          return 0
        }
      })
      const $table = xTable.value
      const filetypecolumn = $table?.getColumnByField('fileType')
      if (filetypecolumn) {
        // 修改筛选列表，并默认设置为选中状态
        $table.setFilter(filetypecolumn, filetypeList.value)
        // 修改条件之后，需要手动调用 updateData 处理表格数据
        $table.updateData()
      }
    })
    //获取IQ数据组织方式
    mtApi.signalDataGet({ dictType: 'data_organization' }).then(res => {
      res.data.forEach((item, index) => {
        dataorganizationList.value.push({ label: item, value: String(index) })
      })
      const $table = xTable.value
      const dataorganizationcolumn = $table?.getColumnByField('dataOrganization')
      if (dataorganizationcolumn) {
        $table.setFilter(dataorganizationcolumn, dataorganizationList.value)
        $table.updateData()
      }
    })
    //获取IQ数据类型
    mtApi.signalDataGet({ dictType: 'data_type' }).then(res => {
      res.data.forEach((item, index) => {
        datatypeList.value.push({ label: item, value: String(index) })
      })
      const $table = xTable.value
      const datatypecolumn = $table?.getColumnByField('dataType')
      if (datatypecolumn) {
        $table.setFilter(datatypecolumn, datatypeList.value)
        $table.updateData()
      }
    })
    //获取调制方式
    mtApi.signalDataGet({ dictType: 'debug_mode' }).then(res => {
      const debugmode = store.dict.filter(item => item.dictType == 'debug_mode')
      debugmode.forEach(item => {
        debugmodeList.value.push({ label: item.dictLabel, value: item.dictValue })
      })
      debugmodeList.value.sort((a, b) => {
        const valueA = parseInt(a.value)
        const valueB = parseInt(b.value)
        if (valueA < valueB) {
          return -1
        } else if (valueA > valueB) {
          return 1
        } else {
          return 0
        }
      })
      debugmodeMessage.value = debugmodeList.value
      const $table = xTable.value
      const debugmodecolumn = $table?.getColumnByField('debugMode')
      if (debugmodecolumn) {
        $table.setFilter(debugmodecolumn, debugmodeList.value)
        $table.updateData()
      }
    })
  }
  // IQ数据组织方式
  const formatDataorganization = value => {
    let txtArr = dataorganizationList.value.filter(item => {
      return value == item.value || value == item.label
    })
    return txtArr.length > 0 ? txtArr[0].label : ''
  }
  // 文件类型
  const formatFiletype = value => {
    let txtArr = filetypeList.value.filter(item => {
      return value == item.value || value == item.label
    })
    return txtArr.length > 0 ? txtArr[0].label : ''
  }
  // IQ数据类型
  const formatIQDatatype = value => {
    let txtArr = datatypeList.value.filter(item => {
      return value == item.value || value == item.label
    })
    return txtArr.length > 0 ? txtArr[0].label : ''
  }
  // 调制方式
  const formatDebugmode = value => {
    let txtArr = debugmodeList.value.filter(item => {
      return value == item.value || value == item.label
    })
    return txtArr.length > 0 ? txtArr[0].label : ''
  }
  const emit = defineEmits(['selectFile'])
  const submitForm = () => {
    emit('selectFile', selectedRow.value)
    tempOpen.value = false
  }
</script>
<style lang="less" scoped>
  .time-button {
    margin: 0 20px;
  }
  .interval-p {
    margin: 0 16px 0 0;
  }
  .search-button {
    margin: 0 0 0 20px;
  }
  .upload-button {
    margin: 0 0 0 20px;
  }
  .delete-all-button {
    margin: 0 0 0 20px;
  }

  .nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .home {
    background-color: none;
    padding: 0;
  }

  .exp-list {
    margin-bottom: 10px;

    .project-desc {
      height: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: pre-line;
      margin-bottom: 10px;
      font-size: 14px;
    }

    li {
      font-size: 12px;
      display: flex;
      color: #aaa;
      text-align: right;

      span {
        float: left;
        margin-right: 2em;
        color: #888;
        text-align: right;
      }

      & > div {
        flex: 1;
      }
    }
  }

  .uploadtime-style {
    display: flex;
  }
  .time-span {
    line-height: 42px;
    margin-right: 5px;
    margin-left: 10px;
  }
  /* 增加筛选颜色 */
  .keyword-lighten {
    color: #000;
    background-color: #ffff00;
  }
  .input-name {
    width: 100%;
    height: 34px;
    border-radius: 4px;
    outline: 0;
    margin: 0;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 0.6em;
    color: var(--chart-text-color);
    border: 1px solid #dcdfe6;
    background-color: var(--background-color);
    box-shadow: none;
  }
  .select-style {
    background-color: var(--background-color);
    border: 1px solid var(--chart-text-color);
    border-radius: 4px;
    padding: 8px;
    color: var(--chart-text-color);
    width: 100%;
    cursor: pointer;
  }
  :deep(.vxe-table--filter-wrapper .vxe-table--filter-body > li:hover) {
    background-color: var(--scrollbar-color) !important;
    border: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-table--filter-wrapper .vxe-table--filter-header > li:hover) {
    background-color: var(--scrollbar-color) !important;
    border: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-table--filter-wrapper) {
    background-color: var(--background-color) !important;
    border: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-select-option--wrapper) {
    background-color: var(--background-color) !important;
    border: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }

  :deep(.vxe-table--body) {
    width: 100% !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-table--header) {
    width: 100% !important;
  }
  :deep(.vxe-pager--goto) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-pager--num-btn) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-pager--next-btn) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-pager--jump-next) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-pager--jump-prev) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-pager--prev-btn) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-input--inner) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  :deep(.vxe-table--header-wrapper) {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  .vxe-toolbar {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  .vxe-page {
    background-color: var(--background-color) !important;
    color: var(--chart-text-color) !important;
  }
  .mytable-scrollbar {
    background-color: var(--background-color);
    color: var(--chart-text-color) !important;
    border: 1px solid var(--background-color) !important;
  }
  .mytable-scrollbar ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  /*滚动条的轨道*/
  .mytable-scrollbar ::-webkit-scrollbar-track-piece {
    background-color: var(--background-color);
  }
  /*滚动条里面的小方块，能向上向下移动*/
  .mytable-scrollbar ::-webkit-scrollbar-thumb {
    background-color: var(--chart-text-color);
    border-radius: 5px;
  }
  .mytable-scrollbar ::-webkit-scrollbar-thumb:hover {
    background-color: var(--chart-text-color);
  }
  .mytable-scrollbar ::-webkit-scrollbar-thumb:active {
    background-color: var(--chart-text-color);
  }
  /*边角，即两个滚动条的交汇处*/
  .mytable-scrollbar ::-webkit-scrollbar-corner {
    background-color: var(--background-color);
  }
</style>
