<template>
  <div id="fmContainer" class="w-full" />
</template>

<script setup name="FmPlot">
  import Highcharts from '@/plugins/highcharts'
  import config from './config'
  import mockFn from './mock'

  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    }
  })

  let instance = null

  const updateData = data => {
    if (!instance) {
      return
    }
    instance.series[0].setData(data)
  }
  // function hexStringToByteArray(hexString) {
  //   if (hexString.length % 2 !== 0) {
  //     throw new Error('Invalid hex string')
  //   }

  //   let byteArray = new Uint8Array(hexString.length / 2)
  //   for (let i = 0; i < hexString.length; i += 2) {
  //     byteArray[i / 2] = parseInt(hexString.substr(i, 2), 16)
  //   }

  //   return byteArray
  // }

  // const hexString =
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
  // const byteArray = hexStringToByteArray(hexString)
  onMounted(() => {
    instance = new Highcharts.Chart('fmContainer', config)
    // const data = mockFn.getData(1000)
    // updateData(byteArray)
  })

  watch(
    () => props.data,
    val => {
      if (val.length > 0) {
        updateData(val)
      }
    }
  )
</script>
