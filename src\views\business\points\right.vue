<template>
  <el-col :span="20" class="app-container pl-4">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      style="height: 60%"
      @row-click="rowClick"
      @selectionChange="rows => (selection = rows)"
    />
    <table-form
      v-bind="tableFormConfigQa"
      ref="refsTableFormQa"
      style="height: 40%"
      @selectionChange="rows => {}"
    />
    <DetailInfo ref="refsMt" :chapter-id="chapterId" @actSuccess="getList()" />
    <FileListPreview ref="refsFile" />
    <QaAdd ref="refsQa" @actSuccess="qasearch()" />
    <PointDetail ref="refsPointDetail" />
    <mtDetailInfo ref="refsMtQa" @actSuccess="search()" />
    <c-import ref="refsImport" />
  </el-col>
</template>
<script>
  import * as mtApi from '@/api/business/points'
  import * as mtApiQa from '@/api/business/qa'
  import DetailInfo from './detail.vue'
  import QaAdd from './qa.vue'
  import mtDetailInfo from '../qa/tmpInfo.vue'
  import PointDetail from '../points/detail.vue'
  export default {
    name: 'PointsList',
    components: { DetailInfo, QaAdd, PointDetail, mtDetailInfo },
    props: ['chapterId','judge'],
    data() {
      return {
        selection: [],
        disabled:false,
        tableFormConfig: {
          title: '知识点',
          searchConfig: {
            formConfig: [{ title: '知识点', name: 'pointName' }],
            loadData: p => {
              p.chapterId = this.chapterId
              return this.chapterId ? mtApi.listPoints(p) : Promise.resolve({ rows: [] })
            }
          },
          actions: [
            {
              label: '新增',
              type: 'primary',
              icon: 'el_Plus',
              disabled: () => !this.judge,
              permission: ['business:points:add'],
              click: () => {            
                  this.$refs.refsMt.show()                                                   
              }
            },
            {
              label: '导出',
              icon: 'el_upload',
              type:'warning',
              isPlain: true,
              disabled: () => !this.chapterId,
              permission: ['business:points:export'],
              click: row =>
                this.download(
                  'business/points/export',
                  { chapterId: this.chapterId },
                  `知识点_${new Date().getTime()}.xlsx`
                )
            },
            {
              type: 'danger',
              label: '删除',
              permission: ['business:points:remove'],
              disabled: () => !this.selection.length,
              isPlain: true,
              icon: 'el_Delete',
              click: row => this.deleteAll()
            },
            {
             
              label: '导入',  
              type: 'info',       
              icon: 'el_download',
              disabled: () => !this.judge,
              click: row => {
              
                  this.importFile()
               
              
              }
            }
          ],
          tableConfig: {
            isChecked: true,
            cols: [
              { label: '知识点', prop: 'pointName', minWidth: 200 },
              {
                label: '操作',
                type: 'action',
                width: '160',
                actions: [
                  {
                    title: '编辑',
                    icon: 'el_Edit',
                    permission: ['business:points:edit'],
                    click: row => this.$refs.refsMt.show(row.pointId)
                  },
                  {
                    title: '提问',
                    icon: 'el_ChatLineRound',
                    permission: ['business:qa:add'],
                    click: row => this.$refs.refsQa.show('', row.pointId)
                  },
                  {
                    title: '教学资料',
                    icon: 'el_Memo',
                    click: row =>
                      mtApi.getPoints(row.pointId).then(res => {
                        this.$refs.refsFile.show(res.data.srFileList)
                      })
                  },
                  {
                    title: '删除',
                    permission: ['business:points:remove'],
                    icon: 'el_Delete',
                    click: row => this.deleteAll(row.pointId)
                  }
                ]
              }
            ]
          }
        },
        tableFormConfigQa: {
          title: '知识点答疑',
          searchConfig: {
            hideBtn: true,
            formConfig: [],
            loadData: p => {
              return mtApiQa.listQa(p)
            }
          },
          actions: [],
          tableConfig: {
            isChecked: true,
            cols: [
              {
                label: '知识点',
                prop: 'srPoints.pointName',
                renderFun: (h, { row }) => {
                  return h(
                    'a',
                    {
                      onClick: () => {
                        this.$refs.refsPointDetail.show(row.pointId, 'view')
                      }
                    },
                    row.srPoints.pointName
                  )
                }
              },
              { label: '问题', prop: 'qaTitle', minWidth: 200 },
              // {
              //   label: '问题内容',
              //   prop: 'qaContent',
              //   renderFun: (h, { row }) => {
              //     return h('span', { 'v-html': row.qaContent }, row.qaContent)
              //   },
              //   width: 300
              // },
              { label: '提问人', prop: 'qaRequsetName' },
              { label: '解答人', prop: 'qaAnswerName' },
              // { label: '解答内容', prop: 'qaAnswerContent', width: 300 },
              { label: '状态', prop: 'status', type: 'dict', dictName: 'qa_status' },
              {
                label: '操作',
                type: 'action',
                width: '100',
                actions: [
                  {
                    title: '答疑',
                    label: '答疑',
                    // icon: 'el_Warning',
                    permission: ['business:qa:answer'],
                    click: row => this.$refs.refsMtQa.show(row.qaId)
                  },
                  {
                    title: '删除',
                    // icon: 'el_Delete',
                    label: '删除',
                    permission: ['business:qa:remove'],
                    click: row => this.deleteAllQa(row.qaId)
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList(data = null) {
        this.$refs.refsTableForm.loadData(data)
      },
      search(data = null) {
        this.$refs.refsTableFormQa.loadData(data)
      },
      qasearch(data = null) {
        this.$refs.refsTableFormQa.loadData(data)
      },
      rowClick(arr) {
        console.log('123', arr)
        this.$refs.refsTableFormQa.loadData({ pageNum: 1, pageSize: 20, pointId: arr.pointId })
      },
      //批量删除模板
      deleteAll(id = null) {
        if (!this.selection.length && !id) return this.$modal.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选知识点吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.pointId)
          mtApi.delPoints(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.getList()
          })
        })
      },
      deleteAllQa(id = null) {
        if (!this.selection.length && !id) return this.$modal.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选知识点答疑吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.objId)
          mtApiQa.delQa(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.$refs.refsTableFormQa.loadData()
          })
        })
      },
      importFile(){
        this.$refs.refsImport.show({
         tmpUrl: '/business/points/excelTemplate',
         tmpName:'知识点管理',
         uploadUrl: '/business/points/importData?' + 'chapterId=' + this.chapterId,
          ok: res => {
            this.getList()
          }
        })
      }
    }
  }
</script>
