<template>
  <!-- 地图 -->
  <div id="map" class="w-full h-full"></div>
  <!-- 地图坐标数据 -->
  <div id="overlay-box"></div>
</template>

<script setup>
  import 'ol/ol.css'
  import TileLayer from 'ol/layer/Tile'
  import VectorLayer from 'ol/layer/Vector'
  import VectorSource from 'ol/source/Vector'
  import XYZ from 'ol/source/XYZ'
  import { Map, View, Feature } from 'ol'
  import { Stroke, Style, Icon } from 'ol/style'
  import { LineString } from 'ol/geom'
  import { fromLonLat } from 'ol/proj'
  import { Point } from 'ol/geom'
  import Overlay from 'ol/Overlay'
  import iconSrc from '@/assets/images/pos.jpg' // 使用相对路径引入图片
  import Drones from '@/assets/images/drones.png'
  import gcj02Mecator from './gcj02Mecator'
  import { round } from 'lodash'
  import { getConfigKey } from '@/api/system/config'

  /**
   * 对坐标进行四舍五入
   * @param coords 坐标数组，格式为 [x, y]
   * @param decimals 保留的小数位数
   * @returns 保留指定位数小数的坐标数组，如果输入的不是数组或数组长度不为2，则返回原坐标
   */
  const roundCoordinates = (coords, decimals) => {
    if (Array.isArray(coords) && coords.length === 2) {
      return [round(coords[0], decimals), round(coords[1], decimals)]
    }
    return coords
  }

  const map = ref(null) // 地图实例
  let pointLayer = null // 点图层
  let lineLayer = null // 线图层
  const markedCombinations = new Set() // 用于记录已经标记过的频率和带宽组合
  const textOverlays = [] // 用于存储文本覆盖物

  const props = defineProps({
    // 地图固定坐标数据（点数据）
    fixedData: {
      type: Array,
      default: () => []
    },
    // 动态坐标数据（线数据）
    dynamicData: {
      type: Object,
      default: () => {}
    }
  })

  const offline = ref(false) // 是否使用离线地图

  onMounted(async () => {
    // 判断是否使用离线地图
    await judgeMapType()
    // 初始化地图
    nextTick().then(() => {
      initMap()
    })
  })

  const initMap = async () => {
    const mapLayerSource = offline.value
      ? new XYZ({
          projection: gcj02Mecator,
          // tileUrlFunction: function (tileCoord) {
          //   var z = tileCoord[0]
          //   var x = tileCoord[1]
          //   var y = Math.pow(2, z - 1) + tileCoord[2] // 注意：有些瓦片服务使用的是从底部开始的行索引，所以需要对 y 进行转换
          // return `${offLinePath.value}/${z}/${x}/${y}.png`
          url: 'http://127.0.0.1:32768/roadmap/' + '/{z}/{x}/{y}.png'
          // }
        })
      : new XYZ({
          projection: gcj02Mecator,
          url: 'http://wprd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&style=7&x={x}&y={y}&z={z}'
        })

    map.value = new Map({
      target: 'map',
      layers: [
        new TileLayer({
          source: mapLayerSource,
          zIndex: 0
        })
      ],
      view: new View({
        projection: 'EPSG:3857',
        center: fromLonLat([119.421003, 32.393159]),
        zoom: 12
      })
    })

    // 初始化点图层·
    pointLayer = new VectorLayer({
      source: new VectorSource()
    })
    map.value.addLayer(pointLayer)

    // 初始化线图层
    lineLayer = new VectorLayer({
      source: new VectorSource()
    })
    map.value.addLayer(lineLayer)

    // 如果在mounted时数据已经存在，初始化地图要素
    if (props.fixedData.length > 0) {
      props.fixedData.forEach(item => {
        addPoints(item)
      })
    }

    if (Object.keys(props.dynamicData).length > 0) {
      Object.values(props.dynamicData).forEach(group => {
        drawLine(group.positions, group.frequency, group.bandwidth)
      })
    }
  }

  /**
   * 判断地图类型
   * 调用 getConfigKey('map.online.offline') 来获取地图的在线/离线配置，并解析其返回结果。
   * 解析后的结果将赋值给 offline.value。
   * @returns 返回一个Promise，无直接返回值，但会异步更新 offline.value 的值
   */
  const judgeMapType = async () => {
    await getConfigKey('map.online.offline').then(async res => {
      offline.value = JSON.parse(res.msg)
    })
  }
  /**
   * 根据经纬度坐标添加监测站要素
   */
  const addPoints = pointInfo => {
    const roundedCoordinates = roundCoordinates(pointInfo.value, 6)
    const feature = new Feature({
      geometry: new Point(fromLonLat(roundedCoordinates))
    })
    // 设置要素的图标
    feature.setStyle(
      new Style({
        // 设置图片效果
        image: new Icon({
          src: iconSrc, // 使用引入的图片路径
          scale: 0.1
        })
      })
    )
    // 要素添加到点图层上
    pointLayer.getSource().addFeature(feature)
    // 设置文字信息
    addText(pointInfo)
    // 检查是否是主机
    if (pointInfo.name === '主机') {
      // 将视图中心设置为第一个点的位置
      map.value.getView().setCenter(fromLonLat(roundedCoordinates))
    }
  }

  /**
   * 添加文本到地图指定位置
   * @param coordinate 坐标点数组，包含经纬度信息
   */
  const addText = pointInfo => {
    const overlayBox = document.getElementById('overlay-box')
    const oSpan = document.createElement('span')
    oSpan.contentEditable = true
    oSpan.id = pointInfo.value[0]
    let pText = document.createTextNode(pointInfo.name)
    oSpan.style = 'color: #d62f20; font-size: 16px; font-weight: bold'
    oSpan.appendChild(pText)
    overlayBox.appendChild(oSpan)
    let textInfo = new Overlay({
      position: fromLonLat(roundCoordinates(pointInfo.value, 6)),
      element: oSpan,
      offset: [-15, -50]
    })
    map.value.addOverlay(textInfo)
    textOverlays.push(textInfo)
  }

  /**
   * 绘制线段并标注频率和带宽
   * @param coordinatesArr 点坐标数组
   * @param frequency 中心频率
   * @param bandwidth 带宽
   */
  const drawLine = (coordinatesArr, frequency, bandwidth) => {
    // console.log(coordinatesArr, '传入的定位经纬度')
    // console.log(frequency, '传入的频率')
    // console.log(bandwidth, '传入的带宽')
    if (!Array.isArray(coordinatesArr) || coordinatesArr.length === 0) {
      // console.error('无效的坐标数组:', coordinatesArr)
      return
    }

    const roundedCoordinates = coordinatesArr.map(coord => roundCoordinates(coord, 6))
    const coordinates = roundedCoordinates.map(coord => fromLonLat(coord))
    // console.log('转换后的坐标:', coordinates)
    const lineString = new LineString(coordinates)
    const feature = new Feature({
      geometry: lineString
    })
    // 调整样式，使线条更明显
    const style = new Style({
      stroke: new Stroke({
        color: 'red', // 更醒目的颜色
        width: 4 // 更宽的线条
      })
    })
    feature.setStyle(style)
    // 检查图层源是否可用并添加特征
    const lineSource = lineLayer.getSource()
    if (lineSource) {
      lineSource.addFeature(feature)
      console.log('特征已添加到线图层')
    } else {
      console.error('线图层源不可用')
    }

    // 标注频率和带宽
    const combinationKey = `${frequency}-${bandwidth}`
    if (!markedCombinations.has(combinationKey)) {
      const midPointIndex = Math.floor(coordinates.length / 2)
      const midPoint = coordinates[midPointIndex]
      const overlayElement = document.createElement('div')
      overlayElement.className = 'label'
      overlayElement.innerHTML = `频率: ${hzToMhz(frequency)} MHz`
      overlayElement.style.color = '#d62f20'
      overlayElement.style.fontSize = '12px'
      overlayElement.style.fontWeight = 'bold'
      const overlay = new Overlay({
        position: midPoint,
        element: overlayElement,
        offset: [0, -15]
      })
      map.value.addOverlay(overlay)
      // console.log('标注已添加:', combinationKey, overlay)
      // 添加到标记集合中
      markedCombinations.add(combinationKey)
    }

    // 在最新的点添加无人机图标
    const latestPoint = coordinates[coordinates.length - 1]
    const droneFeature = new Feature({
      geometry: new Point(latestPoint)
    })
    droneFeature.setStyle(
      new Style({
        image: new Icon({
          src: Drones,
          scale: 0.1
        })
      })
    )
    pointLayer.getSource().addFeature(droneFeature)
  }

  /**
   * 将 Hz 单位转换为 MHz，并精确到小数点后三位
   * @param {number} hz - 以 Hz 为单位的数值
   * @return {number} - 以 MHz 为单位的数值，精确到小数点后三位
   */
  const hzToMhz = hz => {
    const mhz = hz / 1_000_000
    return round(mhz, 3)
  }

  watch(
    () => props.fixedData,
    newData => {
      if (map.value) {
        // 清空点图层上的所有要素
        pointLayer.getSource().clear()
        // 清空之前的文本覆盖物
        textOverlays.forEach(overlay => {
          map.value.removeOverlay(overlay)
          if (overlay.getElement() && overlay.getElement().parentNode) {
            overlay.getElement().parentNode.removeChild(overlay.getElement())
          }
        })
        textOverlays.length = 0

        // 清空线图层上的所有要素
        // lineLayer.getSource().clear()
        // markedCombinations.clear() // 清空已标记的频率和带宽组合

        if (newData.length > 0) {
          newData.forEach(item => {
            addPoints(item)
          })
        }
      }
    },
    { immediate: true, deep: true }
  )

  watch(
    () => props.dynamicData,
    newData => {
      if (map.value && Object.keys(newData).length > 0) {
        // 清空线图层上的所有要素
        lineLayer.getSource().clear()
        // markedCombinations.clear() // 清空已标记的频率和带宽组合

        Object.values(newData).forEach(group => {
          drawLine(group.positions, group.frequency, group.bandwidth)
        })
      }
    },
    { immediate: true, deep: true }
  )
</script>

<style scoped>
  #map {
    height: 100%;
    width: 100%;
  }
  .label {
    background-color: white;
    padding: 2px;
    border: 1px solid #d62f20;
    border-radius: 3px;
  }
</style>
