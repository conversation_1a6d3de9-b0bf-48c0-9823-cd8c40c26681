<template>
  <el-table class="ml-2 max-h-[270px] h-[270px]" border :height="270" :data="tableData">
    <el-table-column label="序号" type="index" width="60" align="center" />
    <el-table-column label="中心频率" prop="signalCenterFrequency" width="200" align="center">
      <template #default="{ row }">
        {{ numToPlot(row.signalCenterFrequency) }}
      </template>
    </el-table-column>
    <el-table-column label="带宽" prop="signalBandwidth" align="center">
      <template #default="{ row }">
        {{ numToPlot(row.signalBandwidth) }}
      </template>
    </el-table-column>
    <el-table-column label="实时(dBm)" prop="signalFieldStrength" align="center" />
    <el-table-column label="最大(dBm)" prop="max" align="center" />
    <el-table-column label="最小(dBm)" prop="min" align="center" />
    <el-table-column label="首次发现时间" prop="firstTime" align="center">
      <template #default="{ row }">
        {{ convertTimestampToDateTimeString(row.firstTime) }}
      </template>
    </el-table-column>
    <el-table-column label="最新更新时间" prop="lastUpdateTime" align="center">
      <template #default="{ row }">
        {{ convertTimestampToDateTimeString(row.lastUpdateTime) }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="120" align="center">
      <template #default="{ row }">
        <el-button size="small" @click="analyze(row)">分析</el-button>
        <el-button size="small" @click="position(row)">定位</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
  import { numToPlot, convertTimestampToDateTimeString } from '@/utils/utils'

  const props = defineProps({
    tableData: {
      type: Array,
      default: () => []
    }
  })
  const emit = defineEmits(['analyze', 'position'])

  /**
   * 分析信号信息
   * @param row 信号信息对象
   */
  const analyze = row => {
    emit('analyze', row)
  }

  /**
   * 根据传入的行数据，跳转到信号定位TDOA页面
   * @param row 行数据
   */
  const position = row => {
    emit('position', row)
  }
</script>
