import dayjs from 'dayjs'
import * as mtApi from '@/api/radarsingalManage'
import { signalFileLoad } from '@/api/singalManage'
let stateDate = ''
//数据展示单位转化
export function displayUnitConversion(data) {
  if (/,|，/.test(data)) {
    data = data.replace(/,|，/g, '')
  }
  if (/[a-zA-Z]/.test(data)) {
    data = data.replace(/hz|Hz/g, '')
    if (data.toLowerCase().includes('k')) {
      return (parseFloat(data) * 1000).toString()
    } else if (data.toLowerCase().includes('m')) {
      return (parseFloat(data) * 1000000).toString()
    } else if (data.toLowerCase().includes('g')) {
      return (parseFloat(data) * 1000000000).toString()
    } else {
      return parseFloat(data).toString()
    }
  } else {
    if (data < 1000) {
      return data + 'Hz'
    } else if (data / 1000 >= 0 && data / 1000 < 1000) {
      return data / 1000 + 'KHz'
    } else if (data / 1000000 > 0 && data / 1000000 < 1000) {
      return data / 1000000 + 'MHz'
    } else if (data / 1000000000 > 0) {
      return data / 1000000000 + 'GHz'
    }
  }
}

// 数据去掉单位处理
export function removeUnit(data) {
  if (/,|，/.test(data)) {
    data = data.replace(/,|，/g, '')
  }
  if (/[a-zA-Z]/.test(data)) {
    if (data.toLowerCase().includes('k')) {
      return Number(parseFloat(data) * 1000)
    } else if (data.toLowerCase().includes('m')) {
      return Number(parseFloat(data) * 1000000)
    } else if (data.toLowerCase().includes('g')) {
      return Number(parseFloat(data) * 1000000000)
    } else {
      return Number(parseFloat(data))
    }
  } else {
    return Number(data)
  }
}

//下载文件
export function downFile(data) {
  const formattedDate = data.uploadTime.includes('-')
    ? dayjs(data.uploadTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss')
    : data.uploadTime
  signalFileLoad({ fileName: data.fileName, uploadTime: formattedDate }).then(res => {
    //1 生成文件的 blob 对象 // 指定转换成blob的类型 => 使用 Blob 创建一个指向类型化数组的URL
    const blobData = new Blob([res], { type: 'blob' })
    if (blobData instanceof Blob) {
      // 2 手动生成文件的 url
      const url = window.URL.createObjectURL(blobData)
      // 3 创建 a 标签，模拟点击事件
      const link = document.createElement('a')
      link.href = url
      // 3.1 重命名下载文件名称
      link.download = data.fileName
      document.body.appendChild(link)
      const evt = document.createEvent('MouseEvents')
      evt.initEvent('click', false, false)
      link.dispatchEvent(evt)
      document.body.removeChild(link)
    }
  })
}

//下载文件
export function downRadarFile(data) {
  const formattedDate = dayjs(data.uploadTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss')
  mtApi.radarSignalFileLoad({ fileName: data.fileName, uploadTime: formattedDate }).then(res => {
    //1 生成文件的 blob 对象 // 指定转换成blob的类型 => 使用 Blob 创建一个指向类型化数组的URL
    const blobData = new Blob([res], { type: 'blob' })
    if (blobData instanceof Blob) {
      // 2 手动生成文件的 url
      const url = window.URL.createObjectURL(blobData)
      // 3 创建 a 标签，模拟点击事件
      const link = document.createElement('a')
      link.href = url
      // 3.1 重命名下载文件名称
      link.download = data.fileName
      document.body.appendChild(link)
      const evt = document.createEvent('MouseEvents')
      evt.initEvent('click', false, false)
      link.dispatchEvent(evt)
      document.body.removeChild(link)
    }
  })
}

//编辑的值转化单位
export function showData(data) {
  data = displayUnitConversion(data)
}

//数据过滤
export function fliterData({ option, cellValue }) {
  return parseFloat(cellValue) === parseFloat(option.data)
}

//时间筛选
export function customDateFilterMethod({ option, row }) {
  if (option.data) {
    if (option.label === '起:') {
      stateDate = dayjs(option.data + ' 00:00:00').valueOf()
    }
    if (option.label === '止:') {
      let date = dayjs(row.uploadTime).valueOf()
      if (date > stateDate && date < dayjs(option.data + ' 23:59:59').valueOf()) {
        return true
      } else {
        return false
      }
    }
  }
}
