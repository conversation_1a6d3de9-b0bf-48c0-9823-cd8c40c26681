<template>
  <data-form :fields="fields" :model="model" />
  <el-button style="width: 80%;margin: 0 10%;" @click="FilterFn">滤波</el-button>
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData';
  import useScanFormStore from '@/store/modules/form/radarScanForm'
  import useChartsStore from '@/store/modules/charts'
  import { getFilter } from '@/api/charts'
  import { cloneDeep } from 'lodash'

  const fields = ref([
    {
      target: 'filterorder',
      name: '滤波器阶数',
      int: true,
      change(val) {
        scanFormStore.setFilterorder()
        // scanFormStore.setCoefficientDown()
      }
    },
    {
      target: 'coefficientDown',
      name: '滚降系数',
      float: false,
      change(val) {
        // scanFormStore.setFilterorder()
        scanFormStore.setCoefficientDown(val)
      }
    },
  ])
  const model = reactive(new ModelData(fields.value, 'scan'))
  const scanFormStore = useScanFormStore()
  const chartsStore = useChartsStore()
  let body = scanFormStore.getFileParams()
  const uniqueId = `${Date.now()}${Math.floor(Math.random() * 1000)}`;
  body.doubleMapKey = uniqueId
  const FilterFn = () => {
    if (!body.fileName) {
      body = scanFormStore.getFileParams()
      body.doubleMapKey = uniqueId
    }
    body.n_FilterOrder = scanFormStore.filterorder
    body.wc = scanFormStore.coefficientDown
    getFilter(body).then(res=>{
      chartsStore.setRadarFilting(res.data)
    })
  }
  watch(() => scanFormStore, () => {
    model.update()
  }, { deep: true })
</script>
