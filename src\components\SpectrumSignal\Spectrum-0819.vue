<template>
  <div class="spectrum-container">
    <!-- 图表容器 -->
    <div :id="usekey" />
    <div id="marker">
      <!-- 自定义SVG标记 -->
      <svg version="1.1" width="24" height="322">
        <path
          fill="#aeadad"
          d="M 0 322 L 24 322  L 12 298 Z M 12 298 L 12 0"
          stroke="#aeadad"
          stroke-width="1"
        />
        <text x="7" y="319" fill="rgba(1, 23, 13, 1)">1</text>
      </svg>
    </div>
    <!-- 显示标记信息 -->
    <div v-if="markers.length > 0" class="markers-info">
      <div>
        <span style="width: 36px">Mark</span>
        <span style="width: 75px">频率({{ speUnit }})</span>
        <span>幅度(dBm)</span>
      </div>
      <div v-for="item in markers" :key="item.index">
        <span style="width: 36px">{{ item.index }}</span>
        <span>{{ item.point ? indexToPlot(item.point.x, model) : '--' }}</span>
        <span>{{ item.point ? round(item.point.y, 3) : '--' }}</span>
      </div>
    </div>
    <!-- 显示当前活动标记的值 -->
    <div v-if="instance && instance.activeMarker" class="marker-values">
      <span v-for="item in currentValues" :key="item.label" style="white-space: nowrap">
        {{ item.label + ':' + item.value }}
      </span>
    </div>
  </div>
</template>

<script setup>
  import Highcharts from 'highcharts'
  import timeline from 'highcharts/modules/timeline'
  import exporting from 'highcharts/modules/exporting'
  import boost from 'highcharts/modules/boost'
  import exportData from 'highcharts/modules/export-data'
  import brokenAxis from 'highcharts/modules/broken-axis'
  import highchartsMore from 'highcharts/highcharts-more'

  timeline(Highcharts)
  exporting(Highcharts)
  boost(Highcharts)
  exportData(Highcharts)
  brokenAxis(Highcharts)
  highchartsMore(Highcharts)

  import SpectrumMarker from '@/common/classes/spectrumMark'
  import TopMarker from '@/common/classes/topMarker'
  import { cloneDeep, round } from 'lodash'
  import { numToPlot, indexToPlot, plotToNum } from '@/utils/utils'
  import { chartConfig } from './chartConfig'
  import { chartConfigColumn } from './chartConfigColumn'
  import { setInstance } from '@/utils/instance'

  const props = defineProps({
    showLines: Array,
    selectedFremarkers: Array,
    isActive: Boolean,
    isTopFollow: Boolean,
    model: Object,
    dataList: Object,
    usekey: String
  })

  const { dataList } = toRefs(props)
  const instance = ref(null)

  // 计算属性，根据 centerFreq 返回频率单位
  const speUnit = computed(() => {
    let centerFreq = null
    if (typeof props.model.centerFreq === 'number') {
      centerFreq = plotToNum(props.model.centerFreq + props.model.unit)
    } else {
      centerFreq = props.model.centerFreq
    }
    if (centerFreq < 1000) {
      return numToPlot(centerFreq).slice(-2)
    }
    return numToPlot(centerFreq).slice(-3)
  })

  // 计算属性，返回当前标记点的频率和幅度等信息
  const currentValues = computed(() => {
    if (!instance.value?.activeMarker) {
      return []
    }
    const x = instance.value.activeMarker.point.x
    return [
      { label: '频率', value: indexToPlot(x, props.model) },
      { label: '幅度值', value: getRoundValue(dataList.value.current[x], 3) },
      { label: '门限值', value: getRoundValue(dataList.value.limit[0], 3) },
      { label: '最大值', value: getRoundValue(dataList.value.max[x], 3) },
      { label: '平均值', value: getRoundValue(dataList.value.average[x], 3) },
      { label: '最小值', value: getRoundValue(dataList.value.min[x], 3) }
    ]
  })

  // 计算属性，根据是否激活和选中的频标，返回需要显示的标记对象
  const markers = computed(() => {
    if (
      !props.isActive ||
      !instance.value ||
      Object.values(dataList.value).every(value => Array.isArray(value) && value.length === 0)
    ) {
      return []
    }
    const chartMarks = instance.value.specturmMarkers || []
    const target = []
    props.selectedFremarkers.forEach(item => {
      let marker = chartMarks.find(mk => mk.index === item)
      if (marker) {
        marker.show()
      } else {
        marker = new SpectrumMarker(
          instance.value,
          item,
          document.getElementById('marker'),
          props.model
        )
      }
      target.push(marker)
    })
    props.isTopFollow && target.push(instance.value.topMarker)
    return target
  })

  // 获取四舍五入后的值
  const getRoundValue = (value, slot) => {
    const num = round(value, slot)
    if (isNaN(num)) {
      return '--'
    }
    return num
  }

  // 获取X轴标签格式化函数
  const getXLabelFormat = (showNum, spectrum) => {
    const centerFreq =
      typeof spectrum.centerFreq === 'number'
        ? spectrum.centerFreq
        : plotToNum(spectrum.centerFreq + spectrum.centerFreqUnit)
    const bandwidth =
      typeof spectrum.bandwidth === 'number'
        ? spectrum.bandwidth
        : plotToNum(spectrum.bandwidth + spectrum.bandwidthUnit)
    const startFreq = centerFreq - bandwidth / 2
    return function () {
      const val = startFreq + (this.value / (showNum - 1)) * bandwidth
      return val > 1e6 ? numToPlot(val.toFixed(6), '', 6) : numToPlot(val.toFixed(2), '', 2)
    }
  }

  // 获取 X 轴配置
  const getXAisConfig = (showNum, spectrum) => {
    const axis = cloneDeep(chartConfig.xAxis)
    axis.max = showNum - 1
    axis.min = 0
    axis.tickAmount = 11
    axis.tickInterval = (showNum - 1) / 10
    axis.labels.formatter = getXLabelFormat(showNum, spectrum)
    return axis
  }

  /**
   * 获取分散的 X 轴配置
   * @param spectrum 频谱数据
   * @returns 返回分散的 X 轴配置
   */
  const getDispersedXConfig = spectrum => {
    const axis = cloneDeep(chartConfigColumn.xAxis)
    // 初始化最小值和最大值
    let minFreq = Infinity
    let maxFreq = -Infinity
    spectrum.discrete.forEach(item => {
      const startFreq = item.frequency - item.analysisBandwidth / 2
      const endFreq = item.frequency + item.analysisBandwidth / 2
      if (startFreq < minFreq) {
        minFreq = startFreq
      }
      if (endFreq > maxFreq) {
        maxFreq = endFreq
      }
    })
    // 将最小值减去10 MHz，确保不低于0
    minFreq = Math.max(0, minFreq - 10e6)
    // 将最大值加上10 MHz
    maxFreq += 10e6
    // 设置X轴的最小值和最大值
    axis.min = minFreq
    axis.max = maxFreq
    // 禁用自动调整
    axis.startOnTick = false
    axis.endOnTick = false
    // 设置X轴的刻度间隔
    axis.tickInterval = (maxFreq - minFreq) / 11
    // 格式化X轴标签
    axis.labels.formatter = function () {
      return numToPlot(this.value)
    }
    return axis
  }

  const initHighChart = () => {
    const chartConfigRes = props.model.type ? chartConfigColumn : chartConfig
    instance.value = new Highcharts.Chart(props.usekey, {
      ...chartConfigRes
    })
    setInstance(markRaw(instance.value))
    if (props.model.type) {
      const xAxisConfig = getDispersedXConfig(props.model)
      updateChartConfig({
        xAxis: xAxisConfig
      })

      dataList.value['current'] = [
        { x: 100000000, y: -70 },
        { x: 200000000, y: -120 },
        { x: 300000000, y: -140 }
      ]
      dataList.value['average'] = [
        { x: 100000000, y: -80 },
        { x: 200000000, y: -100 },
        { x: 300000000, y: -120 }
      ]
      dataList.value['max'] = [
        { x: 100000000, y: -60 },
        { x: 200000000, y: -90 },
        { x: 300000000, y: -100 }
      ]
      dataList.value['min'] = [
        { x: 100000000, y: -100 },
        { x: 200000000, y: -140 },
        { x: 300000000, y: -150 }
      ]
      dataList.value['limit'] = [
        { x: 100000000, y: -90 },
        { x: 200000000, y: -80 },
        { x: 300000000, y: -80 }
      ]

      updateSeriesData()
    }
  }

  // 更新图表配置
  const updateChartConfig = config => {
    try {
      instance.value.update(config, false, true)
      instance.value.redraw()
    } catch (err) {
      console.log(err)
    }
  }

  // 更新视图线条
  const updateViewLines = val => {
    instance.value.series.forEach(item => {
      if (val.includes(item.name)) {
        item.setVisible(true)
      } else {
        item.setVisible(false)
      }
    })
  }

  const seriesEnum = reactive({
    current: '实时',
    average: '平均',
    max: '最大',
    min: '最小',
    limit: '门限'
  })

  // mark-解决了showLines更新不刷新的问题，但是性能缺陷严重
  const updateSeriesData = () => {
    if (!instance.value?.series) {
      return
    }
    if (props.model.type === 1) {
      // 获取所有的 x 轴点
      const xPoints = dataList.value.current.map(point => point.x)
      // 初始化一个数组来存储每个 x 轴点的排序数据
      const seriesData = xPoints.map(x => {
        const points = [
          {
            y: dataList.value.current.find(p => p.x === x)?.y || -Infinity,
            series: 'current',
            color: '#ffff00'
          },
          {
            y: dataList.value.average.find(p => p.x === x)?.y || -Infinity,
            series: 'average',
            color: '#f56c6c'
          },
          {
            y: dataList.value.max.find(p => p.x === x)?.y || -Infinity,
            series: 'max',
            color: '#67c23a'
          },
          {
            y: dataList.value.min.find(p => p.x === x)?.y || -Infinity,
            series: 'min',
            color: '#409eff'
          },
          {
            y: dataList.value.limit.find(p => p.x === x)?.y || -Infinity,
            series: 'limit',
            color: '#ffffff'
          }
        ]
        // 按 y 值大小排序
        return points.sort((a, b) => b.y - a.y)
      })
      // 清空已有的系列
      while (instance.value.series.length > 0) {
        instance.value.series[0].remove(false)
      }
      // 根据 showLines 来过滤需要更新的系列
      const filteredSeriesData = seriesData.map(points =>
        points.filter(point => props.showLines.includes(seriesEnum[point.series]))
      )
      // 动态添加系列
      filteredSeriesData.forEach((dataSet, index) => {
        dataSet.forEach((dataPoint, idx) => {
          const seriesName = dataPoint.series
          instance.value.addSeries(
            {
              id: seriesName,
              name: seriesEnum[seriesName],
              data: [
                {
                  x: xPoints[index],
                  y: dataPoint.y,
                  color: dataPoint.color
                }
              ],
              pointPlacement: 'on',
              threshold: props.model.refLevel - 100, // 设置柱子的起始位置为 Y 轴的最小值
              zIndex: idx // 通过 zIndex 控制渲染顺序
            },
            false
          )
        })
      })

      instance.value.redraw()
    } else {
      requestAnimationFrame(() => {
        // 连续状态：优化处理
        instance.value.series.forEach(series => {
          const shouldShow = props.showLines.includes(seriesEnum[series.options.id])
          if (shouldShow) {
            series.setData(dataList.value[series.options.id], false)
            series.update({ visible: true }, false)
          } else {
            series.update({ visible: false }, false)
          }
        })
        // 更新 TopMarker
        if (instance.value.topMarker) {
          instance.value.topMarker.update()
        }
        // 更新图表
        instance.value.redraw()
      })
    }
  }

  defineExpose({
    instance
  })

  watch(
    () => props.model.type,
    () => {
      if (instance.value) {
        instance.value.destroy() // 销毁现有图表实例
        instance.value = null
        initHighChart() // 重新初始化图表
      }
    }
  )
  // 更新视图线条
  watch(
    () => props.showLines,
    val => {
      if (props.model.type === 0) {
        updateViewLines(val) // 连续状态下更新可见性
      } else {
        updateSeriesData() // 离散状态下更新数据
      }
    }
  )

  // 更新图表系列数据
  watch(
    () => props.isTopFollow,
    val => {
      if (!instance.value.topMarker) {
        new TopMarker(instance.value)
      }
      instance.value.topMarker.setVisible(val)
    }
  )

  // 监听 selectedFremarkers 的变化，移除取消选中的标记
  watch(
    () => props.selectedFremarkers,
    (newVal, oldVal) => {
      const removedMarkers = oldVal.filter(item => !newVal.includes(item))
      const currentMarkers = instance.value.specturmMarkers || []
      removedMarkers.forEach(item => {
        const marker = currentMarkers.find(mk => mk.index === item)
        if (marker) {
          marker.remove()
        }
      })
    }
  )

  watch(
    () => dataList.value.current,
    (val, oldVal) => {
      if (val.length !== oldVal.length) {
        const xAxisConfig =
          props.model.type && props.model.discrete
            ? getDispersedXConfig(props.model)
            : getXAisConfig(val.length, props.model)
        updateChartConfig({
          xAxis: xAxisConfig
        })
        props.model.resultLen = val.length
      }
      requestAnimationFrame(() => {
        updateSeriesData()
      })
    }
  )

  watch(
    () => props.model.refLevel,
    val => {
      const yAxis = props.model.type
        ? cloneDeep(chartConfigColumn.yAxis)
        : cloneDeep(chartConfig.yAxis)
      yAxis.max = val * 1
      yAxis.min = val - 100
      requestAnimationFrame(() => {
        updateChartConfig({ yAxis })
      })
    }
  )

  watch(
    () => props.model.startFreq,
    () => {
      if (dataList.value.current.length !== 0) {
        const xAxisConfig = getXAisConfig(dataList.value.current.length, props.model)
        requestAnimationFrame(() => {
          updateChartConfig({
            xAxis: xAxisConfig
          })
        })
      }
    }
  )

  onMounted(() => {
    initHighChart()
  })

  onActivated(() => {
    instance.value?.redraw()
  })

  onDeactivated(() => {})
</script>

<style scoped lang="scss">
  .spectrum-container {
    position: relative;
    height: 400px;
  }

  #spectrum {
    height: 100%;
  }

  #marker {
    position: absolute;
    bottom: 48px;
    cursor: pointer;
    display: none;
    opacity: 0.6;
  }

  .markers-info {
    font-size: 12px;
    color: var(--scan-text-color);
    position: absolute;
    right: 0;
    top: 12px;

    span {
      padding: 0 6px;
      display: inline-block;
    }
  }

  .marker-values {
    font-size: 12px;
    color: var(--scan-text-color);
    position: absolute;
    left: 60px;
    top: 12px;
    width: 300px;

    span {
      padding: 0 6px;
      display: inline-block;

      &:nth-child(4),
      &:nth-child(1) {
        width: 106px;
      }
    }
  }
</style>
