import request from '@/utils/request'

// 查询开课列表
export function listStarts(query) {
  return request({
    url: '/business/starts/list',
    method: 'get',
    params: query
  })
}

// 查询开课详细
export function getStarts(stId) {
  return request({
    url: '/business/starts/' + stId,
    method: 'get'
  })
}

// 新增开课
export function addStarts(data) {
  return request({
    url: '/business/starts',
    method: 'post',
    data: data
  })
}

// 修改开课
export function updateStarts(data) {
  return request({
    url: '/business/starts',
    method: 'put',
    data: data
  })
}

// 删除开课
export function delStarts(stId) {
  return request({
    url: '/business/starts/' + stId,
    method: 'delete'
  })
}
