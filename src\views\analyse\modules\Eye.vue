<template>
  <div class="eye-container">
    <chart-header title="眼图" name="iEye" />
    <el-row :gutter="20">
      <el-col :span="12">
        <div id="iEye" />
      </el-col>
      <el-col :span="12">
        <div id="qEye" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  // 眼图
  import useChartsStore from '@/store/modules/charts'
  import ChartHeader from '@/views/analyse/header/ChartHeader'
  import useDmFormStore from '@/store/modules/form/dmForm'
  import useChartOptions from '@/common/hooks/chartOptions'
  import Highcharts from '@/plugins/highcharts'
  import Interval from '@/common/classes/interval'
  import ci from '@/common/chartInstances'
  import { cloneDeep, round } from 'lodash'

  defineComponent([ChartHeader])

  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    }
  })

  let iContainer = null
  let qContainer = null
  let interval = null
  const chartsStore = useChartsStore()
  const iData = ref([])
  const qData = ref([])
  const { settings } = chartsStore
  // 一组几个点
  const groupNum = ref(0)
  const form = useDmFormStore()
  const { themeStyle, chartOptions } = useChartOptions()
  const isStatic = chartsStore.static.enabled
  const generateEyeData = (data, eyestep = 25) => {
    const result = []
    for (let i = 0; i < data.length; i += eyestep) {
      let n = 0,
        temp = []
      while (n < eyestep) {
        temp.push([n, data[i + n]])
        n++
      }
      result.push(temp)
    }
    return result
  }
  const genearteChartsOptions = data => {
    const currentOptions = cloneDeep(chartOptions.value)
    currentOptions.yAxis.min = -1.6
    currentOptions.yAxis.max = 1.6
    currentOptions.xAxis.min = 0
    const max = Math.ceil(props.data.eyestep) - 1
    currentOptions.xAxis.max = max
    currentOptions.xAxis.tickInterval = max / 10
    currentOptions.xAxis.labels.formatter = function () {
      if (this.value === 0) {
        return -1
      }
      if (this.value === max / 2) {
        return 0
      }
      if (this.value === max) {
        return 1
      }
    }
    currentOptions.xAxis.labels.renderFormatter = function () {
      return round(-1 + (this.value / max) * 2, 2)
    }
    currentOptions.yAxis.tickAmount = 11
    currentOptions.yAxis.tickInterval = 0.32
    currentOptions.yAxis.labels.formatter = function () {
      return round(this.value, 1)
    }
    const t = isStatic ? data.slice(0, chartsStore.static.dmLimit) : data.slice(0, form.resultLen)
    currentOptions.series = t.map(item => ({
      type: 'spline',
      marker: {
        enabled: false
      },
      enableMouseTracking: false,
      animation: false,
      lineWidth: 0.5,
      color: themeStyle.value.lineColor,
      data: item
    }))
    return currentOptions
  }
  const initCharts = () => {
    iContainer = new Highcharts.Chart('iEye', genearteChartsOptions(iData.value))
    qContainer = new Highcharts.Chart('qEye', genearteChartsOptions(qData.value))
    ci.set(iContainer, 'iEye')
    ci.set(qContainer, 'qEye')
  }
  const generateViewData = (data, start) => {
    const showNum = form.resultLen
    const len = data.length
    let temp = []
    if (start + showNum >= len) {
      temp = data.slice(-showNum)
    } else {
      temp = data.slice(start, start + showNum)
    }
    return temp
  }

  // 更新图表
  const updateChart = (start, data, instance) => {
    if (!instance) {
      return
    }
    const curViewData = generateViewData(data, start)
    instance.series?.forEach((s, i) => {
      s.setData(curViewData[i], true, false)
    })
  }
  const upadteDataChart = (start = 0) => {
    updateChart(start, iData.value, iContainer)
    updateChart(start, qData.value, qContainer)
  }
  const init = async () => {
    const eyestep = Math.ceil(props.data.eyestep)
    iData.value = generateEyeData(props.data.pdIBase, eyestep)
    qData.value = generateEyeData(props.data.pdQBase, eyestep)
    initCharts()
    if (!isStatic) {
      interval = Interval.create(
        upadteDataChart,
        form.resultLen,
        form.resultLen,
        iData.value.length
      )
    }
  }

  watchEffect(() => {
    const { modulation } = form
    groupNum.value = Math.pow(2, modulation)
  })

  watch(
    () => themeStyle.value,
    () => {
      iContainer.update(genearteChartsOptions(iData.value), true)
      qContainer.update(genearteChartsOptions(qData.value), true)
    }
  )

  onMounted(() => {
    init()
  })

  onUnmounted(() => {
    interval?.destroy()
  })
</script>

<style scoped>
  .eye-container {
    background-color: var(--background-color);
  }
</style>
