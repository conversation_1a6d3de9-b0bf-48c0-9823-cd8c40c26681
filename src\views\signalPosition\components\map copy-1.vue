<template>
  <!-- 地图 -->
  <div id="map" class="w-full h-full"></div>
  <!-- 地图坐标数据 -->
  <div id="overlay-box"></div>
</template>

<script setup>
  import 'ol/ol.css'
  import TileLayer from 'ol/layer/Tile'
  import VectorLayer from 'ol/layer/Vector'
  import VectorSource from 'ol/source/Vector'
  import XYZ from 'ol/source/XYZ'
  import { Map, View, Feature } from 'ol'
  import { Stroke, Style, Icon } from 'ol/style'
  import { LineString } from 'ol/geom'
  import { fromLonLat } from 'ol/proj'
  import { Point } from 'ol/geom'
  import Overlay from 'ol/Overlay'
  import iconSrc from '@/assets/images/pos.jpg' // 使用相对路径引入图片
  import gcj02Mecator from './gcj02Mecator'

  const map = ref(null)
  let pointLayer = null
  let lineLayer = null
  const markedCombinations = new Set() // 用于记录已经标记过的频率和带宽组合

  const props = defineProps({
    // 地图固定坐标数据（点数据）
    fixedData: {
      type: Array,
      default: () => []
    },
    dynamicData: {
      type: Object,
      default: () => {}
    }
  })

  onMounted(() => {
    nextTick().then(() => {
      initMap()
    })
  })

  const initMap = () => {
    map.value = new Map({
      target: 'map',
      layers: [
        new TileLayer({
          source: new XYZ({
            projection: gcj02Mecator,
            url: 'http://wprd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&style=7&x={x}&y={y}&z={z}'
          }),
          zIndex: 0
        })
      ],
      view: new View({
        projection: 'EPSG:3857',
        center: fromLonLat([119.421003, 32.393159]),
        zoom: 12
      })
    })

    // 初始化点图层
    pointLayer = new VectorLayer({
      source: new VectorSource()
    })
    map.value.addLayer(pointLayer)

    // 初始化线图层
    lineLayer = new VectorLayer({
      source: new VectorSource()
    })
    map.value.addLayer(lineLayer)

    // 如果在mounted时数据已经存在，初始化地图要素
    if (props.fixedData.length > 0) {
      props.fixedData.forEach(item => {
        addPoints(item)
      })
    }

    if (Object.keys(props.dynamicData).length > 0) {
      Object.values(props.dynamicData).forEach(group => {
        drawLine(group.positions, group.frequency, group.bandwidth)
      })
    }
  }

  /**
   * 根据经纬度坐标添加监测站要素
   */
  const addPoints = pointInfo => {
    // 清空点图层上的所有要素
    if (pointLayer) {
      pointLayer.getSource().clear()
    }

    const feature = new Feature({
      geometry: new Point(fromLonLat(pointInfo.value))
    })

    // 设置要素的图标
    feature.setStyle(
      new Style({
        // 设置图片效果
        image: new Icon({
          src: iconSrc, // 使用引入的图片路径
          scale: 0.1
        })
      })
    )

    // 要素添加到点图层上
    pointLayer.getSource().addFeature(feature)
    // 设置文字信息
    addText(pointInfo)

    // 检查是否是主机
    if (pointInfo.name === '主机') {
      // 将视图中心设置为第一个点的位置
      map.value.getView().setCenter(fromLonLat(pointInfo.value))
    }
  }

  /**
   * 添加文本到地图指定位置
   * @param coordinate 坐标点数组，包含经纬度信息
   */
  const addText = pointInfo => {
    const overlayBox = document.getElementById('overlay-box') //获取一个div
    overlayBox.innerHTML = '' // 清空之前的文本

    const oSpan = document.createElement('span') //创建一个span
    oSpan.contentEditable = true //设置文字是否可编辑
    oSpan.id = pointInfo.value[0] //创建一个id
    let pText = document.createTextNode(pointInfo.name) //创建span的文本信息
    // 设置颜色和字体大小
    oSpan.style = 'color: #d62f20; font-size: 16px;font-weight:bold'
    oSpan.appendChild(pText) //将文本信息添加到span
    overlayBox.appendChild(oSpan) //将span添加到div中
    let textInfo = new Overlay({
      position: fromLonLat(pointInfo.value), //设置位置
      element: document.getElementById(pointInfo.value[0]),
      offset: [-15, -50] //设置偏移
    })
    map.value.addOverlay(textInfo)
  }

  /**
   * 绘制线段并标注频率和带宽
   * @param coordinatesArr 点坐标数组
   * @param frequency 中心频率
   * @param bandwidth 带宽
   */
  const drawLine = (coordinatesArr, frequency, bandwidth) => {
    if (!Array.isArray(coordinatesArr) || coordinatesArr.length === 0) {
      console.error('Invalid coordinates array:', coordinatesArr)
      return
    }

    const coordinates = coordinatesArr.map(coord => fromLonLat(coord))
    console.log('Converted coordinates:', coordinates)

    const lineString = new LineString(coordinates)
    const feature = new Feature({
      geometry: lineString
    })

    const style = new Style({
      stroke: new Stroke({
        color: '#d62f20',
        width: 2
      })
    })

    feature.setStyle(style)

    // 将要素添加到线图层上
    lineLayer.getSource().addFeature(feature)

    // 标注频率和带宽
    const combinationKey = `${frequency}-${bandwidth}`
    if (!markedCombinations.has(combinationKey)) {
      const midPointIndex = Math.floor(coordinates.length / 2)
      const midPoint = coordinates[midPointIndex]

      const overlayElement = document.createElement('div')
      overlayElement.className = 'label'
      overlayElement.innerHTML = `Freq: ${frequency} MHz<br>BW: ${bandwidth} MHz`
      overlayElement.style.color = '#d62f20'
      overlayElement.style.fontSize = '12px'
      overlayElement.style.fontWeight = 'bold'

      const overlay = new Overlay({
        position: midPoint,
        element: overlayElement,
        offset: [0, -15]
      })
      map.value.addOverlay(overlay)

      // 添加到标记集合中
      markedCombinations.add(combinationKey)
    }
  }

  watch(
    () => props.fixedData,
    newData => {
      if (map.value && newData.length > 0) {
        newData.forEach(item => {
          addPoints(item)
        })
      }
    },
    { immediate: true, deep: true }
  )

  watch(
    () => props.dynamicData,
    newData => {
      if (map.value && Object.keys(newData).length > 0) {
        Object.values(newData).forEach(group => {
          drawLine(group.positions, group.frequency, group.bandwidth)
        })
      }
    },
    { immediate: true, deep: true }
  )
</script>

<style scoped>
  #wrapper {
    height: 100vh;
    width: 100vw;
  }
  #map {
    height: 100%;
    width: 100%;
  }
  .label {
    background-color: white;
    padding: 2px;
    border: 1px solid #d62f20;
    border-radius: 3px;
  }
</style>
