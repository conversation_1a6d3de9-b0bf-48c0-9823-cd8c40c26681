<template>
  <el-col :span="4">
    <div class="ptitle">课程章节</div>
    <el-tree
      ref="deptTreeRef"
      node-key="id"
      :current-node-key="selectedKey"
      :props="prop"
      :data="deptOptions"
      highlight-current
      :expand-on-click-nod="false"
      @node-click="handleNodeClick"
    />
  </el-col>
</template>
<script>
  import { knowTreeData } from '@/api/business/points'
  import { listChapter } from '@/api/business/chapter'
  export default {
    name: 'CourseChapterLeftTree',
    prop: ['chapterId'],
    emits: ['change','judge'],
    data() {
      return {
        selectedKey: '',
        changeId:'',
        deptOptions: [],
        prop: {
          label: 'chapterName',
          children: 'children',
          isLeaf: 'leaf',
          class: (data, node) => {
            let classes = 'node-' + node.level
            if (node.level != 1) {
              classes += ' child '
            }
            if (node.level != 1 && data.chapterId == this.selectedKey) {
              classes += ' selected'
            }
            return classes
          }
        }
      }
    },
    created() {
      this.getCourChapterTree()
    },
    methods: {
      getCourChapterTree() {
        knowTreeData({ pageSize: 100, pageNum: 1 }).then(res => {
          let children = res.map(v => {
            return {
              chapterName: v.courName,
              id: v.courId,
              level: 0,
              loaded: false,
              children: [{}]
            }
          })
          this.deptOptions = children
          this.selectedKey = this.deptOptions[0]?.id
          this.$emit('change', this.selectedKey)
 
        })
      },
      handleNodeClick(data, node) {
        console.log('data', data )
        console.log('node', node )
        if (node.level == 1 && !data.loaded) {
          listChapter({ courseId: data.id }).then(res => {
            let list = this.handleTree(res.data, 'chapterId', 'chapterPid')
            data.children = list.length ? list : []
            data.loaded = true
          })  
        }
        if(data.children){
         this.$emit('judge', 0)
        }else{
           this.$emit('judge', 1)
        }
        this.selectedKey = data.chapterId || data.id
        this.$emit('change', data.chapterId || data.id)
     
        //
      },
      
    }
  }
</script>
<style lang="less" scoped>
  :deep(.el-tree-node.selected > .el-tree-node__content) {
    background-color: var(--el-color-primary) !important;
    color: #fff;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
</style>
