<template>
  <div id="map">
    <div class="fixed-icons-container">
      <el-tooltip
        class="box-item"
        content="底图"
        placement="left"
      >
        <el-icon :size="30"><PictureRounded class="icon-one" @click="changeMap" /></el-icon>
      </el-tooltip>
      <div class="line-one" />
      <el-tooltip
        class="box-item"
        content="统计表"
        placement="left"
      >
        <el-icon :size="30">
          <Calendar class="icon-one" @click="showStatisticsTable" />
        </el-icon>
      </el-tooltip>
      <div class="line-one" />
      <el-tooltip
        class="box-item"
        content="统计图"
        placement="left"
      >
        <el-icon :size="30">
          <Histogram class="icon-one" @click="showChart" />
        </el-icon>
      </el-tooltip>
      <div class="line-one" />
      <el-tooltip
        class="box-item"
        content="时间轴"
        placement="left"
      >
        <el-icon :size="30">
          <Timer class="icon-one" />
        </el-icon>
      </el-tooltip>
    </div>
    <!-- 绘制测量工具 -->
    <drawtool :mapvalue="mapvalue" />
    <!-- 统计表 表格 -->
    <vxe-modal
      :model-value="ismodaltable"
      :title="title"
      width="15%"
      height="300px"
      :position="{top: 160, left: 2100}"
      :mask-closable="true"
      @close="closeModal"
    >
      <vxe-table
        :data="tableData"
      >
        <vxe-column field="name" title="Name" />
        <vxe-column field="sex" title="Sex" />
        <vxe-column field="age" title="Age" />
      </vxe-table>
    </vxe-modal> 
    <!-- 统计图  -->
    <vxe-modal
      :model-value="ismodalchart"
      :title="titlechart"
      width="15%"
      height="300px"
      :position="{top: 560, left: 2100}"
      :mask-closable="true"
      @close="closechartModal"
    >
      <echarts />
    </vxe-modal>
    <!-- 底图选择 -->
    <div v-show="ismodal" class="modal-class">
      <h4 class="title-header">选择底图</h4>
      <span class="close-button" @click="closeDrawer">x</span>
      <hr>
      <div>
        <a href="javascript:void(0)" class="map-change" @click="changeLayer(0)"><img src="@/assets/images/firstimg.jpg">地图</a>
        <a href="javascript:void(1)" class="map-change" @click="changeLayer(1)"><img src="@/assets/images/secondimg.jpg">影像</a>
        <a href="javascript:void(2)" class="map-change" @click="changeLayer(2)"><img src="@/assets/images/thirdimg.jpg">地形</a>
        <a href="javascript:void(2)" class="map-change" @click="getjsondata"><img src="@/assets/images/administrativeRegion.jpg">行政区域</a>
      </div>
    </div>
    <!-- 专题图层 -->
    <!-- <titlelayer @data="handleData" /> -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue' 
import  drawtool from '@/common/tools/drawtool'
// import  titlelayer from '@/common/tools/titlelayer'
import  echarts from './echarts.vue'
// 地图实例方法、视图方法  
import { Map, View } from 'ol'      
//经纬度进行转化
// import { transform } from 'ol/proj';
import GeoJSON from 'ol/format/GeoJSON';
import * as olProj from "ol/proj";
import { Vector as VectorSource } from 'ol/source';
import { Tile as TileLayer, Vector as VectorLayer } from 'ol/layer';
// 地图样式
import 'ol/ol.css'                 
import mapType from "../maptype";
import china from "@/common/jsondata/china.json";
import { Circle as CircleStyle, Fill, Stroke, Style } from 'ol/style';
const title = ref('数据')
const titlechart = ref('统计图')
const ismodal = ref(false)
const ismodaltable = ref(false)
const ismodalchart = ref(false)
// 存放地图实例
const map = ref(null) 
// 存放地图实例(传给子组件)
const mapvalue = ref(null)
const allMapLayer = ref([])
let tileLayer = null
let lineLayer =null
const mapList = ref(null)
const locaMap = ref("0") || mapChange()
const message = ref('');
const tableData = ref([
  { id: 10001, name: 'Test1', sex: 'Man', age: 28, },
  { id: 10002, name: 'Test2', sex: 'Women', age: 22,},
  { id: 10003, name: 'Test3', sex: 'Man', age: 32,},
])
// 创建矢量数据源和矢量图层
const vectorSource = new VectorSource();
  const vectorLayer = new VectorLayer({
    source: vectorSource,
    style: new Style({
      stroke: new Stroke({
        color: 'red',
        width: 1
      })
    })
  });
onMounted(() => {
  mapList.value = mapType
  tileLayer = new TileLayer({
    source: mapType.find((e) => e.id === locaMap.value).value,
    id:mapType[locaMap.value].name
  });
  initMap()
})
const initMap  = () => {
  map.value = new Map({
    target: "map",
    layers: [tileLayer],
    view: new View({
      center: olProj.transform([118.945951, 32.465262], 'EPSG:4326', 'EPSG:3857'),
      minZoom:3,                          
      zoom: 7,
      maxZoom:13
    }),
    vectorLayer,
    controls: [],
  });
  mapvalue.value = map.value
}
//子组件传的值
const handleData = (data,data2) => {
  message.value = data;
};
const clearLayer = data => {
  allMapLayer.value = map.value.getLayers().getArray()
    allMapLayer.value.map((item,index)=> { 
      map.value.removeLayer(map.value.getLayers().item(index));
    })
}
//添加行政区域
const getjsondata = () => {
  let features = (new GeoJSON({ featureProjection: 'EPSG:3857' })).readFeatures(china)
  let vectorSource = new VectorSource({ features: features });
  if (lineLayer) {
    clearLayer("行政图")
  }
  lineLayer = new VectorLayer({
    zIndex: 99,
    source: vectorSource,
    id:"行政图"
  });
  // 把图层添加到地图
  map.value.addLayer(lineLayer)  
}
const changeMap = () => {
  ismodal.value = !ismodal.value
}
const closeDrawer = () =>{
  ismodal.value = !ismodal.value
}
const showStatisticsTable = () => {
  ismodaltable.value = !ismodaltable.value
}
const closeModal = () => {
  ismodaltable.value = !ismodaltable.value
}
const showChart = () => {
  ismodalchart.value = !ismodalchart.value
}
const closechartModal = () => {
  ismodalchart.value = !ismodalchart.value
}
//改变图层
const changeLayer = (data) => {
  if(tileLayer){
    clearLayer(mapType[data].name)
  }
  tileLayer = new TileLayer({
    source: mapType.find((e) => e.id == data).value,
    id:mapType[data].name
  });
  map.value.addLayer(tileLayer)
  if(data != 0){
    changeoverlay()
  }
}
//添加的混合图
const changeoverlay = () => {
  allMapLayer.value.map((item,index)=> { 
    if(mapType[3].name === item.values_.id){
      map.value.removeLayer(map.value.getLayers().item(index));
    }
  })
  tileLayer = new TileLayer({
    source: mapType.find((e) => e.id === "3").value,
    id:mapType[3].name
  });
  map.value.addLayer(tileLayer)
}
</script>
<style scoped>
#map {
  height: 100%;
  position: 'relative'
}
.fixed-icons-container {
  width: 38px;
  height:124px;
  position: absolute;
  right: 20px;
  top: 42%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index:999;
  box-shadow: 1px 1px 1px 1px rgba(0,0,0,.3);
  background: white;
}
.modal-class{
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.3);
  background: #fff;
  padding: 15px;
  padding-bottom: 4px;
  z-index: 999;
  cursor: auto;
  position: absolute;
  width: 12% !important;
  right: 2.6%;
  height: 27.5%;
  top: 42%;
  overflow-y: scroll;
}
.icon-one{
  width: 38px !important;
  height: 38px !important;
  font-size: 12px;
  line-height: 38px;
  text-align: center;
  cursor: pointer;
  margin: 0 5px;
  color:#333;
}
.line-one{
  width: 20px;
  height: 1px;
  border-bottom: 1px solid #cccccc;
  margin: 0px 9px;
}
.close-button{
  cursor: pointer;
  font-size: 17px;
  position: absolute;
  right: 7px;
  top: 0;
}
.title-header{
  font-size:14px;
  color:#333;
  text-align: left;
  padding-bottom: 5px;
}
.map-change{
  width: 80px;
  float: left;
  text-align: center;
  line-height: 20px;
}
.map-change > img{
  width: 74px;
  height: 54px;
}
.modal-table{
  left:2100px;
  top:160px;
}
:deep().el-collapse-item__header{
  color: #333;
}
:deep().vxe-table--body-wrapper{
    overflow-x: scroll !important;
}
:deep().vxe-modal--wrapper {
  width: 0 !important;
}
:deep().vxe-modal--wrapper.is--mask:before, .vxe-modal--wrapper.lock--view:before{
  width: 0 !important;
}
</style>
<style scoped>
  :deep().ol-tooltip {
    position: relative;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: white;
    padding: 4px 8px;
    opacity: 0.7;
    white-space: nowrap;
    font-size: 12px;
    cursor: default;
    user-select: none;
  }

  :deep().ol-tooltip-measure {
    opacity: 1;
    font-weight: bold;
  }

  :deep().ol-tooltip-static {
    background-color: #ffcc33;
    color: black;
    border: 1px solid white;
  }

  :deep().ol-tooltip-measure:before,
  :deep().ol-tooltip-static:before {
    border-top: 6px solid rgba(0, 0, 0, 0.5);
    border-right: 6px solid transparent;
    border-left: 6px solid transparent;
    content: "";
    position: absolute;
    bottom: -6px;
    margin-left: -7px;
    left: 50%;
  }

  :deep().ol-tooltip-static:before {
    border-top-color: #ffcc33;
  }
</style>




