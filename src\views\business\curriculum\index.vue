<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="8em"
    >
      <el-form-item label="课程名称" prop="curriculumName">
        <el-input
          v-model="queryParams.curriculumName"
          placeholder="请输入课程名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:curriculum:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:curriculum:edit']"
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:curriculum:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:curriculum:export']"
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar v-model:show-search="showSearch" @queryTable="getList" />
    </el-row>
    <div class="fit-table">
      <el-table
        v-loading="loading"
        border
        stripe
        :data="curriculumList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="课程编码" align="center" prop="curriculumCode" />
        <el-table-column label="课程名称" align="center" prop="curriculumName" />
        <el-table-column label="状态" align="center" prop="status" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="scope">
            <el-button
              v-hasPermi="['business:curriculum:edit']"
              icon="Edit"
              link
              @click="handleUpdate(scope.row)"
            >
              修改
            </el-button>
            <el-button
              v-hasPermi="['business:curriculum:remove']"
              icon="Delete"
              link
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改课程信息对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课程编码" prop="curriculumCode">
          <el-input v-model="form.curriculumCode" placeholder="请输入课程编码" />
        </el-form-item>
        <el-form-item label="课程名称" prop="curriculumName">
          <el-input v-model="form.curriculumName" placeholder="请输入课程名称" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import {
    listCurriculum,
    getCurriculum,
    delCurriculum,
    addCurriculum,
    updateCurriculum
  } from '@/api/business/curriculum'

  export default {
    name: 'Curriculum',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 课程信息表格数据
        curriculumList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          curriculumCode: null,
          curriculumName: null,
          status: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {}
      }
    },
    created() {
      this.getList()
    },
    methods: {
      /** 查询课程信息列表 */
      getList() {
        this.loading = true
        listCurriculum(this.queryParams).then(response => {
          this.curriculumList = response.rows
          this.total = response.total
          this.loading = false
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          curriculumId: null,
          curriculumCode: null,
          curriculumName: null,
          createBy: null,
          createTime: null,
          updateBy: null,
          updateTime: null,
          status: '0',
          delFlag: null,
          remark: null
        }
        this.resetForm('form')
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryForm')
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.curriculumId)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset()
        this.open = true
        this.title = '添加课程信息'
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset()
        const curriculumId = row.curriculumId || this.ids
        getCurriculum(curriculumId).then(response => {
          this.form = response.data
          this.open = true
          this.title = '修改课程信息'
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.curriculumId != null) {
              updateCurriculum(this.form).then(response => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
            } else {
              addCurriculum(this.form).then(response => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const curriculumIds = row.curriculumId || this.ids
        this.$modal
          .confirm('是否确认删除课程信息编号为"' + curriculumIds + '"的数据项？')
          .then(function () {
            return delCurriculum(curriculumIds)
          })
          .then(() => {
            this.getList()
            this.$modal.msgSuccess('删除成功')
          })
          .catch(() => {})
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download(
          'business/curriculum/export',
          {
            ...this.queryParams
          },
          `curriculum_${new Date().getTime()}.xlsx`
        )
      }
    }
  }
</script>
