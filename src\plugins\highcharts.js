import Highcharts from 'highcharts'
import timeline from 'highcharts/modules/timeline'
import exporting from 'highcharts/modules/exporting'
import boost from 'highcharts/modules/boost'
import exportData from 'highcharts/modules/export-data'
import brokenAxis from 'highcharts/modules/broken-axis'
import highchartsMore from 'highcharts/highcharts-more'

timeline(Highcharts)
exporting(Highcharts)
boost(Highcharts)
exportData(Highcharts)
brokenAxis(Highcharts)
highchartsMore(Highcharts)

const orignSetData = Highcharts.Series.prototype.setData

Highcharts.Series.prototype.setData = function (...args) {
  orignSetData.apply(this, args)
  if (this.chart.customMarker) {
    this.chart.customMarker.update()
  }
}

const originUpdate = Highcharts.Chart.prototype.update

Highcharts.Chart.prototype.update = function (...args) {
  originUpdate.apply(this, args)
  if (this.customMarker) {
    this.customMarker.update()
  }
}

const redraw = Highcharts.Chart.prototype.redraw
Highcharts.Chart.prototype.redraw = function (...args) {
  redraw.apply(this, args)
  if (this.customMarker) {
    this.customMarker.update()
  }
  if (this.redrawCb) {
    this.redrawCb()
  }
}

export default Highcharts
