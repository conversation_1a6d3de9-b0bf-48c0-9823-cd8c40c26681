<template>
  <!-- 添加或修改虚拟资源信息对话框 -->
  <el-dialog v-model="dia" v-loading="loading" :title="title" width="800px" append-to-body>
    <el-form
      ref="resourcesRef"
      v-loading="loading"
      class="mr-4"
      :model="form"
      label-width="9em"
      :rules="rules"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="UUID" prop="uuid">
            <el-input
              v-model="form.uuid"
              :disabled="title == '修改虚拟资源信息'"
              placeholder="请输入UUID"
            />
          </el-form-item>
          <el-form-item label="虚拟机编号" prop="id">
            <el-input v-model="form.id" placeholder="请输入虚拟机编号" />
          </el-form-item>
          <el-form-item label="虚拟机IP" prop="ip">
            <el-input v-model="form.ip" placeholder="请输入虚拟机IP" />
          </el-form-item>
          <el-form-item label="虚拟机名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入虚拟机名称" />
          </el-form-item>
          <!-- <el-form-item label="登录名" prop="loginName" v-if="form.uuid">
            <el-input v-model="form.loginName" placeholder="请输入登录名" />
          </el-form-item>
          <el-form-item label="登录密码" prop="loginPass" v-if="form.uuid">
            <el-input v-model="form.loginPass" placeholder="请输入登录密码" />
          </el-form-item> -->
          <el-form-item label="主机编号" prop="hostId">
            <el-input v-model="form.hostId" placeholder="请输入主机编号" />
          </el-form-item>
          <el-form-item label="域名" prop="domainName">
            <el-input v-model="form.domainName" placeholder="请输入域名" />
          </el-form-item>
          <el-form-item label="磁盘大小" prop="diskSize">
            <el-input v-model="form.diskSize" placeholder="请输入磁盘大小" />
          </el-form-item>
          <el-form-item label="cpu频率" prop="cpuRate">
            <el-input v-model="form.cpuRate" placeholder="请输入cpu频率" />
          </el-form-item>
          <el-form-item label="集群Id" prop="clusterid">
            <el-input v-model="form.clusterid" placeholder="请输入集群Id" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="集群名称" prop="clustername">
            <el-input v-model="form.clustername" placeholder="请输入集群名称" />
          </el-form-item>
          <el-form-item label="主机名称" prop="hostName">
            <el-input v-model="form.hostName" placeholder="请输入主机名称" />
          </el-form-item>
          <el-form-item label="内存" prop="memory">
            <el-input v-model="form.memory" placeholder="请输入内存" />
          </el-form-item>
          <el-form-item label="内存频率" prop="memoryRate">
            <el-input v-model="form.memoryRate" placeholder="请输入内存频率" />
          </el-form-item>
          <el-form-item label="操作系统" prop="osType">
            <el-input v-model="form.osType" placeholder="请输入操作系统" />
          </el-form-item>
          <el-form-item label="操作系统版本" prop="osVersion">
            <el-input v-model="form.osVersion" placeholder="请输入操作系统版本" />
          </el-form-item>
          <el-form-item label="系统盘大小" prop="sysDiskSize">
            <el-input v-model="form.sysDiskSize" placeholder="请输入系统盘大小" />
          </el-form-item>
          <el-form-item label="cup核数" prop="vcpu">
            <el-input v-model="form.vcpu" placeholder="请输入cup核数" />
          </el-form-item>
          <!-- <el-form-item label="是否加密" prop="isEncrypt">
            <dict-select v-model="form.isEncrypt" dict-name="sys_yes_no" />
            <el-input v-model="form.isEncrypt" placeholder="请输入是否加密" />
          </el-form-item> -->
          <!-- <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" rows="3" placeholder="请输入备注" />
          </el-form-item> -->
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel(false)">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
  import { getResources, addResources, updateResources } from '@/api/business/resources'
  export default {
    name: 'ResourceDetail',
    emits: ['update:modelValue', 'actSuccess'],
    data() {
      return {
        form: {},
        title: '',
        rules: {
          uuid: [{ required: true, message: '请输入UUID', trigger: 'blur' }],
          ip: [{ required: true, message: '请输入虚拟机IP', trigger: 'blur' }],
          id: [{ required: true, message: '请输入虚拟机编号', trigger: 'blur' }]
        },
        dia: false,
        loading: false
      }
    },
    methods: {
      show(id) {
        this.init(id)
      },
      // 取消按钮
      cancel(t = false) {
        this.dia = false
        t && this.$emit('actSuccess')
        this.form = {}
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs['resourcesRef'].validate(valid => {
          if (valid) {
            this.loading = true
            if (this.form.resId != null) {
              updateResources(this.form)
                .then(response => {
                  this.$modal.msgSuccess('修改成功')
                  this.cancel(true)
                })
                .finally(() => {
                  this.loading = false
                })
            } else {
              addResources(this.form)
                .then(response => {
                  this.$modal.msgSuccess('新增成功')
                  this.cancel(true)
                })
                .finally(() => {
                  this.loading = false
                })
            }
          }
        })
      },
      init(id = '') {
        this.dia = true
        this.form = {}
        this.title = !!id ? '修改虚拟资源信息' : '添加虚拟资源信息'
        id &&
          (this.loading = true) &&
          getResources(id)
            .then(response => {
              this.form = response.data
            })
            .finally(() => {
              this.loading = false
            })
      }
    }
  }
</script>
