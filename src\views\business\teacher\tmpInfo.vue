<template>
  <el-dialog
    :key="dia"
    v-model="dia"
    width="960px"
    append-to-body
    :title="id ? '教师信息' : '新增教师'"
  >
    <c-form
      v-bind="formConfig"
      :key="form.teaId + '' + dia"
      ref="refsForm"
      v-model="form"
      v-loading="loading"
      class="mr-4"
    />
    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button type="primary" @click="sub">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
  import pattern from '@/utils/pattern'
  import { getTeacher, addTeacher, updateTeacher } from '@/api/business/teacher'
  export default {
    emits: ['actSuccess', 'close'],
    data() {
      return {
        dia: false,
        loading: false,
        id: '',
        form: {},

        formConfig: {
          splice: 2,
          layout: 'block',
          autoEmit: false,
          hideBtn: true,
          formInfo: [
            {
              title: '教师编号',
              name: 'teaCode',
              rules: { required: true, message: '请输入教师编号' }
            },
            {
              title: '教师姓名',
              name: 'teaName',
              rules: { required: true, message: '请输入姓名' }
            },
            {
              title: '教师性别',
              name: 'teaSex',
              // required: true,
              type: 'dict',
              dictName: 'sys_user_sex',
              // rules: { required: true, message: '请选择性别' }
            },
            {
              title: '手机号码',
              name: 'teaPhone',
              maxLenght: 11,
              maxLen: 11,
              rules: [{ pattern: pattern.TEL, message: '请输入11位手机号码' }]
            },
            {
              title: '身份证',
              name: 'teaIdent',
              maxLen: 18,
              rules: [
                {
                  pattern: pattern.IDENT,
                  message: '请输入正确的身份证号码'
                }
              ]
            },
            { title: '籍贯', name: 'teaNative',  rules: [{ pattern: pattern.STRING, message: '请输入正确籍贯' }] },
            {
              title: '出生日期',
              name: 'teaBirth',
              type: 'date'
            },
            {
              title: '用户邮箱',
              name: 'teaEmail',
              rules: [
                {
                  pattern: pattern.EMAIL,
                  message: '请输入正确的邮箱'
                }
              ]
            },
            { title: '专业', name: 'teaMajor', type: 'dict', dictName: 'stu_major' },
            {
              title: '政治面貌',
              name: 'teaPolitical',
              type: 'dict',
              dictName: 'teaching_political'
            },

            {
              title: '学历',
              name: 'teaEducation',
              type: 'dict',
              dictName: 'teaching_education'
            },
            {
              title: '入职时间',
              name: 'teaIndate',
              type: 'date',
              // rules: { required: true, message: '请选择入职时间' }
            },
            {
              title: '家庭住址',
              name: 'remark',
              type: 'addr',
              addrlist: ['entProvince', 'entCounty', 'entCity']
            },
            {
              title: '详细地址',
              name: 'teaHomeaddr',
              type: 'textarea',
              maxLen: 200,
              autoSize: 1
            },
            { title: '照片', name: 'teaAvatar', type: 'image-upload', limit: 1 }
          ]
        }
      }
    },

    methods: {
      show(id) {
        this.id = id
        this.dia = true
        this.form = {}
        id && this.getInfo(id)
      },
      getInfo(id) {
        this.loading = true
        getTeacher(id)
          .then(res => {
            this.form = res.data
          })
          .finally(() => {
            this.loading = false
          })
      },
      sub() {
        this.$refs.refsForm.subForm(form => {
          let act = this.form.teaId ? updateTeacher : addTeacher
          // console.log('arr', this.form)

          act(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
        })
      }
    }
  }
</script>
<style></style>
