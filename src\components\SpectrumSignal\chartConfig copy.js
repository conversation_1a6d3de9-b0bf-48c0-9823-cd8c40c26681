import useScanStore from '@/store/modules/scanMonitor'
import { cloneDeep, isUndefined } from 'lodash'

const { spectrum } = useScanStore()
export const chartConfig = {
  chart: {
    backgroundColor: '#020e1f',
    type: 'line',
    height: 400,
    spacingTop: 30,
    spacingBottom: 0,
    spacingLeft: 0,
    spacingRight: 10,
    zoomType: 'xy',
    events: {
      redraw(ev) {
        this.specturmMarkers &&
          this.specturmMarkers.forEach(marker => {
            marker.update()
          })
        this.topMarker && this.topMarker.update()
      }
    }
  },
  exporting: { enabled: false },
  reflow: true,
  credits: { enabled: false },
  accessibility: {
    enabled: false
  },
  xAxis: {
    title: { text: '频率', style: { color: '#aeadad' } },
    labels: {
      style: { color: '#aeadad' }
    },
    events: {
      setExtremes(ev) {
        const { max, min, target } = ev
        const axisMax = ev.target.dataMax
        const axisMin = ev.target.dataMin
        if (min < axisMax / 2 && max > axisMax / 2) {
          this.chart.zoomInMid = true
        }
        const dataMin = isUndefined(min) ? axisMin : min
        const dataMax = isUndefined(max) ? axisMax : max
        const tickInterval = (dataMax - dataMin) / 10
        target.update({ max: dataMax, min: dataMin, tickInterval })
        if (isUndefined(max)) {
          if (this.chart.zoomInMid) {
            this.chart.zoomInMid = false
            setTimeout(() => {
              this.setExtremes(0, dataMax)
            })
          }
        }
      }
    },
    tickAmount: 11,
    lineColor: '#aeadad',
    lineWidth: 0
  },
  yAxis: {
    title: { text: '幅度(dBμV/m)', style: { color: '#aeadad' } },
    // min: spectrum.refLevel - 100,
    // max: spectrum.refLevel,
    gridLineColor: 'rgba(1, 84, 120, 0.8)',
    gridLineDashStyle: 'solid',
    gridLineWidth: 0.5,
    lineColor: 'rgba(1, 84, 120, 1)',
    tickAmount: 11,
    labels: { style: { color: '#aeadad' } }
  },
  title: { enabled: false, text: '' },
  boost: { useGPUTranslations: true },
  legend: {
    enabled: false
    // itemStyle: { color: '#fff' },
    // itemHoverStyle: { color: '#f3f3f3' },
    // navigation: { enabled: false },
    // padding: 0,
    // margin: 2
  },
  series: [
    {
      id: 'current',
      name: '实时',
      color: '#ffff00',
      marker: { enabled: false },
      animation: false,
      enableMouseTracking: false,
      type: 'line',
      data: [],
      lineWidth: 0.5,
      boostThreshold: 100000,
      gapSize: 1
    },
    {
      id: 'average',
      name: '平均',
      color: '#f56c6c',
      marker: { enabled: false },
      animation: false,
      enableMouseTracking: false,
      type: 'line',
      data: [],
      lineWidth: 0.5,
      gapSize: 1
    },
    {
      id: 'max',
      name: '最大',
      color: '#67c23a',
      marker: { enabled: false },
      animation: false,
      enableMouseTracking: false,
      type: 'line',
      data: [],
      lineWidth: 0.5,
      gapSize: 1
    },
    {
      id: 'min',
      name: '最小',
      color: '#409eff',
      marker: { enabled: false },
      animation: false,
      enableMouseTracking: false,
      type: 'line',
      data: [],
      lineWidth: 0.5,
      gapSize: 1
    },
    {
      id: 'limit',
      name: '门限',
      color: '#fff',
      marker: { enabled: false },
      animation: false,
      enableMouseTracking: false,
      type: 'line',
      data: [],
      lineWidth: 0.5,
      gapSize: 1
    }
  ],
  plotOptions: {
    series: {
      turboThreshold: 1000,//启用 Turbo模式，当数据量大于 1000 时启用
    }
  },
}
