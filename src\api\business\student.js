import request from '@/utils/request'

// 查询实验学生列表
export function listStudent(query) {
  return request({
    url: '/business/student/list',
    method: 'get',
    params: query
  })
}

// 查询实验学生详细
export function getStudent(sctId) {
  return request({
    url: '/business/student/' + sctId,
    method: 'get'
  })
}

// 新增实验学生
export function addStudent(data) {
  return request({
    url: '/business/student',
    method: 'post',
    data: data
  })
}

// 修改实验学生
export function updateStudent(data) {
  return request({
    url: '/business/student',
    method: 'put',
    data: data
  })
}

// 删除实验学生
export function delStudent(sctId) {
  return request({
    url: '/business/student/' + sctId,
    method: 'delete'
  })
}
