<template>
  <!-- 表格数据 -->
  <div class="fit-table">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="addRadarFn"
        >
          新增
        </el-button>
      </el-col>
  
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          @click="delRadarFn"
        >
          删除
        </el-button>
      </el-col>
    </el-row>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="radarList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" />
      <el-table-column label="通用参数" align="center">
        <el-table-column label="信号编号" prop="radarSignalNum" width="100" />
        <el-table-column label="信号频率(Hz)" prop="radarFrequency" width="120" />
        <el-table-column label="信号带宽(Hz)" prop="signalBandwidth" width="120" />
        <el-table-column label="信号场强(dBμV/m)" prop="signalStrength" width="150" />
        <el-table-column label="载噪比(dBm)" prop="cnr" width="120" />
      </el-table-column>
      <el-table-column label="雷达信号监测结果" align="center">
        <el-table-column label="截获次数" prop="interceptTimes" width="120" />
        <el-table-column label="初次截获日期" prop="initialInterceptionDate" width="120" />
        <el-table-column label="初次截获时间" prop="initialInterceptionTime" width="160" />
        <el-table-column label="最新截获日期" prop="latestInterceptionDate" width="120" />
        <el-table-column label="最新截获时间" prop="latestInterceptionTime" width="160" />
        <el-table-column label="批号" prop="batchNumber" />
        <el-table-column label="工作频率类型" prop="operatingFrequencyType" width="120" />
        <el-table-column label="工作频率数量" prop="operatingFrequencyNum" width="120" />
        <el-table-column label="工作频率值(Hz)" prop="operatingFrequencyValue" width="130" />
        <el-table-column label="频率中心值(Hz)" prop="frequencyCenterValue" width="130" />
        <el-table-column label="工作频率最大值(Hz)" prop="frequencyMaxValue" width="150" />
        <el-table-column label="工作频率最小值(Hz)" prop="frequencyMinValue" width="150" />
        <el-table-column label="方位(真北)" prop="positionNorth" width="120" />
        <el-table-column label="方位(车/机头)" prop="positionCar" width="120" />
        <el-table-column label="俯仰角(对水平面)" prop="pitchAngle" width="140" />
        <el-table-column label="俯仰角(车/机身)" prop="pitchAngleCar" width="140" />
        <el-table-column label="定位(经度,维度,高度)" prop="positioning" width="160" />
        <el-table-column label="定位(经度,维度,高度)" prop="positioning1" width="160" />
        <el-table-column label="定位(经度,维度,高度)" prop="positioning2" width="160" />
        <el-table-column label="幅度中心值" prop="amplitudeCenterValue" width="120" />
        <el-table-column label="幅度最大值" prop="amplitudeMaxValue" width="120" />
        <el-table-column label="幅度最小值" prop="amplitudeMinValue" width="120" />
        <el-table-column label="脉宽类型" prop="pulseWidthType" width="120" />
        <el-table-column label="脉宽数量" prop="pulseWidthNum" width="120" />
        <el-table-column label="中心脉宽值(ns)" prop="pulseWidthValue" width="140" />
        <el-table-column label="脉宽上限(ns)" prop="pulseWidthUpValue" width="120" />
        <el-table-column label="脉宽下限(ns)" prop="pulseWidthDownValue" width="120" />
        <el-table-column label="重周类型" prop="doubleCycleType" width="140" />
        <el-table-column label="重周数量" prop="doubleCycleNum" width="120" />
        <el-table-column label="重周(ns)" prop="doubleCycle" width="120" />
        <el-table-column label="重周中心值(ns)" prop="doubleCycleValue" width="130" />
        <el-table-column label="重周最大值(ns)" prop="doubleCycleMax" width="130" />
        <el-table-column label="重周最小值(ns)" prop="doubleCycleMin" width="130" />
        <el-table-column label="雷达型号" prop="radarModel" width="120" />
        <el-table-column label="脉内调制类型" prop="intrapulseModulationType" width="120" />
        <el-table-column label="调制编码个数" prop="modulationCodes" width="120" />
        <el-table-column label="编码序列" prop="codingSequence" width="120" />
        <el-table-column label="字码宽度" prop="codeWidth" width="120" />
        <el-table-column label="带宽" prop="bandwidth" width="120" />
        <el-table-column label="线性调频斜率" prop="linearFrequencyModulationSlope" width="120" />
      </el-table-column>
      <el-table-column label="测向结果" align="center">
        <el-table-column label="示向度" prop="directionality" />
        <el-table-column label="俯仰角" prop="pitchAngleLast" />
        <el-table-column label="测向质量" prop="directionFindingQuality" width="120" />
        <el-table-column label="信号场强" prop="signalStrengthLast" width="120" />
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="160"
        fixed="right"
      >
        <template #default="scope">
          <el-button text link @click="editRadarFn(scope.row)">
            修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <pagination
    v-show="total > 0"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :total="total"
    @pagination="getList"
  />

  <el-dialog
    v-model="addRadarDialog"
    width="900px"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="form"
      label-width="160px"
      :rules="rules"
    >
      <span class="titleStyle">通用参数</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="信号编号" prop="radarSignalNum">
            <el-input v-model="form.radarSignalNum" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号频率(Hz)" prop="radarFrequency">
            <el-input v-model="form.radarFrequency" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号带宽(Hz)" prop="signalBandwidth">
            <el-input v-model="form.signalBandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强(dBμV/m)" prop="signalStrength">
            <el-input v-model="form.signalStrength" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="载噪比(dBm)" prop="cnr">
            <el-input v-model="form.cnr" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">雷达信号监测结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="截获次数" prop="interceptTimes">
            <el-input v-model="form.interceptTimes" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="初次截获日期" prop="initialInterceptionDate">
            <el-input v-model="form.initialInterceptionDate" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="初次截获时间" prop="initialInterceptionTime">
            <el-input v-model="form.initialInterceptionTime" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最新截获日期" prop="latestInterceptionDate">
            <el-input v-model="form.latestInterceptionDate" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最新截获时间" prop="latestInterceptionTime">
            <el-input v-model="form.latestInterceptionTime" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="批号" prop="batchNumber">
            <el-input v-model="form.batchNumber" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作频率类型">
            <el-select v-model="form.operatingFrequencyType" style="width:100%">
              <el-option
                v-for="item in operatingFrequencyTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作频率数量" prop="operatingFrequencyNum">
            <el-input v-model="form.operatingFrequencyNum" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作频率值(Hz)" prop="operatingFrequencyValue">
            <el-input v-model="form.operatingFrequencyValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="频率中心值(Hz)" prop="frequencyCenterValue">
            <el-input v-model="form.frequencyCenterValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作频率最大值(Hz)" prop="frequencyMaxValue">
            <el-input v-model="form.frequencyMaxValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作频率最小值(Hz)" prop="frequencyMinValue">
            <el-input v-model="form.frequencyMinValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="方位(真北)" prop="positionNorth">
            <el-input v-model="form.positionNorth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="方位(车/机头)" prop="positionCar">
            <el-input v-model="form.positionCar" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="俯仰角(对水平面)" prop="pitchAngle">
            <el-input v-model="form.pitchAngle" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="俯仰角(车/机身)" prop="pitchAngleCar">
            <el-input v-model="form.pitchAngleCar" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="定位(经度,维度,高度)" prop="positioning">
            <el-input v-model="form.positioning" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="定位(经度,维度,高度)" prop="positioning1">
            <el-input v-model="form.positioning1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="定位(经度,维度,高度)" prop="positioning2">
            <el-input v-model="form.positioning2" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="幅度中心值" prop="amplitudeCenterValue">
            <el-input v-model="form.amplitudeCenterValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="幅度最大值" prop="amplitudeMaxValue">
            <el-input v-model="form.amplitudeMaxValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="幅度最小值" prop="amplitudeMinValue">
            <el-input v-model="form.amplitudeMinValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉宽类型">
            <el-select v-model="form.pulseWidthType" style="width:100%">
              <el-option
                v-for="item in pulseWidthTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉宽数量" prop="pulseWidthNum">
            <el-input v-model="form.pulseWidthNum" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="中心脉宽值(ns)" prop="pulseWidthValue">
            <el-input v-model="form.pulseWidthValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉宽上限(ns)" prop="pulseWidthUpValue">
            <el-input v-model="form.pulseWidthUpValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉宽下限(ns)" prop="pulseWidthDownValue">
            <el-input v-model="form.pulseWidthDownValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周类型">
            <el-select v-model="form.doubleCycleType" style="width:100%">
              <el-option
                v-for="item in doubleCycleTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周数量" prop="doubleCycleNum">
            <el-input v-model="form.doubleCycleNum" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周(ns)" prop="doubleCycle">
            <el-input v-model="form.doubleCycle" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周中心值(ns)" prop="doubleCycleValue">
            <el-input v-model="form.doubleCycleValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周最大值(ns)" prop="doubleCycleMax">
            <el-input v-model="form.doubleCycleMax" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周最小值(ns) " prop="doubleCycleMin">
            <el-input v-model="form.doubleCycleMin" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="雷达型号" prop="radarModel">
            <el-input v-model="form.radarModel" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉内调制类型">
            <el-select v-model="form.intrapulseModulationType" style="width:100%">
              <el-option
                v-for="item in intrapulseModulationTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调制编码个数" prop="modulationCodes">
            <el-input v-model="form.modulationCodes" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编码序列" prop="codingSequence">
            <el-input v-model="form.codingSequence" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字码宽度" prop="codeWidth">
            <el-input v-model="form.codeWidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="带宽" prop="bandwidth">
            <el-input v-model="form.bandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="线性调频斜率" prop="linearFrequencyModulationSlope">
            <el-input v-model="form.linearFrequencyModulationSlope" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">测向结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="示向度" prop="directionality">
            <el-input v-model="form.directionality" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="俯仰角" prop="pitchAngleLast">
            <el-input v-model="form.pitchAngleLast" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="测向质量" prop="directionFindingQuality">
            <el-input v-model="form.directionFindingQuality" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强" prop="signalStrengthLast">
            <el-input v-model="form.signalStrengthLast" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(formRef)">确 定</el-button>
        <el-button @click="closeModel">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog
    v-model="editRadarDialog"
    width="800px"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="editform"
      label-width="140px"
      :rules="rules"
    >
      <span class="titleStyle">通用参数</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="信号编号" prop="radarSignalNum">
            <el-input v-model="editform.radarSignalNum" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号频率(Hz)" prop="radarFrequency">
            <el-input v-model="editform.radarFrequency" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号带宽(Hz)" prop="signalBandwidth">
            <el-input v-model="editform.signalBandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强(dBμV/m)" prop="signalStrength">
            <el-input v-model="editform.signalStrength" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="载噪比(dBm)" prop="cnr">
            <el-input v-model="editform.cnr" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">雷达信号监测结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="截获次数" prop="interceptTimes">
            <el-input v-model="editform.interceptTimes" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="初次截获日期" prop="initialInterceptionDate">
            <el-input v-model="editform.initialInterceptionDate" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="初次截获时间" prop="initialInterceptionTime">
            <el-input v-model="editform.initialInterceptionTime" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最新截获日期" prop="latestInterceptionDate">
            <el-input v-model="editform.latestInterceptionDate" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最新截获时间" prop="latestInterceptionTime">
            <el-input v-model="editform.latestInterceptionTime" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="批号" prop="batchNumber">
            <el-input v-model="editform.batchNumber" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作频率类型">
            <el-select v-model="editform.operatingFrequencyType" style="width:100%">
              <el-option
                v-for="item in operatingFrequencyTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作频率数量" prop="operatingFrequencyNum">
            <el-input v-model="editform.operatingFrequencyNum" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作频率值(Hz)" prop="operatingFrequencyValue">
            <el-input v-model="editform.operatingFrequencyValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="频率中心值(Hz)" prop="frequencyCenterValue">
            <el-input v-model="editform.frequencyCenterValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作频率最大值(Hz)" prop="frequencyMaxValue">
            <el-input v-model="editform.frequencyMaxValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作频率最小值(Hz)" prop="frequencyMinValue">
            <el-input v-model="editform.frequencyMinValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="方位(真北)" prop="positionNorth">
            <el-input v-model="editform.positionNorth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="方位(车/机头)" prop="positionCar">
            <el-input v-model="editform.positionCar" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="俯仰角(对水平面)" prop="pitchAngle">
            <el-input v-model="editform.pitchAngle" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="俯仰角(车/机身)" prop="pitchAngleCar">
            <el-input v-model="editform.pitchAngleCar" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="定位(经度,维度,高度)" prop="positioning">
            <el-input v-model="editform.positioning" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="定位(经度,维度,高度)" prop="positioning1">
            <el-input v-model="editform.positioning1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="定位(经度,维度,高度)" prop="positioning2">
            <el-input v-model="editform.positioning2" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="幅度中心值" prop="amplitudeCenterValue">
            <el-input v-model="editform.amplitudeCenterValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="幅度最大值" prop="amplitudeMaxValue">
            <el-input v-model="editform.amplitudeMaxValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="幅度最小值" prop="amplitudeMinValue">
            <el-input v-model="editform.amplitudeMinValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉宽类型" prop="pulseWidthType">
            <el-select v-model="editform.pulseWidthType" style="width:100%">
              <el-option
                v-for="item in pulseWidthTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉宽数量" prop="pulseWidthNum">
            <el-input v-model="editform.pulseWidthNum" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="中心脉宽值(ns)" prop="pulseWidthValue">
            <el-input v-model="editform.pulseWidthValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉宽上限(ns)" prop="pulseWidthUpValue">
            <el-input v-model="editform.pulseWidthUpValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉宽下限(ns)" prop="pulseWidthDownValue">
            <el-input v-model="editform.pulseWidthDownValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周类型">
            <el-select v-model="editform.doubleCycleType" style="width:100%">
              <el-option
                v-for="item in doubleCycleTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周数量" prop="doubleCycleNum">
            <el-input v-model="editform.doubleCycleNum" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周(ns)" prop="doubleCycle">
            <el-input v-model="editform.doubleCycle" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周中心值(ns)" prop="doubleCycleValue">
            <el-input v-model="editform.doubleCycleValue" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周最大值(ns)" prop="doubleCycleMax">
            <el-input v-model="editform.doubleCycleMax" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重周最小值(ns) " prop="doubleCycleMin">
            <el-input v-model="editform.doubleCycleMin" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="雷达型号" prop="radarModel">
            <el-input v-model="editform.radarModel" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脉内调制类型">
            <el-select v-model="editform.intrapulseModulationType" style="width:100%">
              <el-option
                v-for="item in intrapulseModulationTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调制编码个数" prop="modulationCodes">
            <el-input v-model="editform.modulationCodes" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编码序列" prop="codingSequence">
            <el-input v-model="editform.codingSequence" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字码宽度" prop="codeWidth">
            <el-input v-model="editform.codeWidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="带宽" prop="bandwidth">
            <el-input v-model="editform.bandwidth" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="线性调频斜率" prop="linearFrequencyModulationSlope">
            <el-input v-model="editform.linearFrequencyModulationSlope" />
          </el-form-item>
        </el-col>
      </el-row>
      <span class="titleStyle">测向结果</span>
      <el-row>
        <el-col :span="12">
          <el-form-item label="示向度" prop="directionality">
            <el-input v-model="editform.directionality" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="俯仰角" prop="pitchAngleLast">
            <el-input v-model="editform.pitchAngleLast" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="测向质量" prop="directionFindingQuality">
            <el-input v-model="editform.directionFindingQuality" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信号场强" prop="signalStrengthLast">
            <el-input v-model="editform.signalStrengthLast" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="closeEditModel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { listRadar,addRadar,delRadar,updateRadar} from '@/api/system/radar'
const loading = ref(false)
const addDialog = ref(false)
const total = ref(0)
const radarList = ref([])
const addRadarDialog = ref(false)
const editRadarDialog = ref(false)
const deleteId = ref([])
const operatingFrequencyTypeList = ref([
  {label:'未知',value:0},
  {label:'频率固定',value:1},
  {label:'射频可选择',value:2},
  {label:'频率分集',value:3},
  {label:'频率编码',value:4},
  {label:'射频捷变',value:5},
  {label:'射频参差',value:6},
  {label:'连续波频率调制',value:7},
  {label:'连续波编码调制',value:8},
  {label:'连续波噪声调制',value:9},
  {label:'脉内频率编码',value:10},
  {label:'频率组变',value:11},
  {label:'频率分集',value:12},
  {label:'无效',value:255},
])
const pulseWidthTypeList = ref([
  {label:'未知',value:0},
  {label:'固定',value:1},
  {label:'脉宽分集',value:2},
  {label:'脉宽捷变',value:3},
  {label:'双脉冲',value:4},
  {label:'多脉冲',value:5},
  {label:'连续波',value:6},
  {label:'脉冲复合',value:7},
  {label:'脉宽可选择',value:8},
  {label:'脉宽编码',value:9},
  {label:'脉宽抖动',value:10},
  {label:'脉宽调制',value:11},
  {label:'脉冲组',value:12},
  {label:'无效',value:255},
])
const doubleCycleTypeList = ref([
  {label:'未知',value:0},
  {label:'脉冲重复周期固定',value:1},
  {label:'脉冲重复周期可选择',value:2},
  {label:'脉冲重复周期抖动',value:3},
  {label:'脉冲重复周期捷变',value:4},
  {label:'脉冲重复周期参差',value:5},
  {label:'脉冲重复周期编码',value:6},
  {label:'脉冲重复周期滑变',value:7},
  {label:'脉冲多普勒',value:8},
  {label:'脉冲重复周期驻留',value:9},
  {label:'脉冲重复周期转换',value:10},
  {label:'脉冲重复周期交替',value:11},
  {label:'脉冲重复周期复合',value:12},
  {label:'脉冲重复周期脉间参差',value:13},
  {label:'脉冲重复周期脉组参差',value:14},
  {label:'无效',value:255},
])
const intrapulseModulationTypeList = ref([
  {label:'未知',value:0},
  {label:'线性调频',value:1},
  {label:'非线性调频',value:2},
  {label:'二相编码',value:3},
  {label:'四相编码',value:4},
  {label:'脉内无调制',value:5},
  {label:'频率编码',value:6},
  {label:'单调非线性调频',value:7},
  {label:'多次线性调频',value:8},
  {label:'一般调频',value:9},
  {label:'脉内分集',value:10},
  {label:'脉冲压缩',value:11},
  {label:'无效',value:255},
])
const formRef = ref(null);

const { resetFields,validate } = toRefs(formRef);
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
})

const commonNumberRules = { pattern: /^[0-9]+$/, message: "请输入数字整数", trigger: "blur" }
const pointNumberRules = { pattern: /^\d{1,6}(\.\d{2})?$/, message: "请输入", trigger: "blur" }
const pointDigitNumberRules = { pattern: /^\d{1,6}(\.\d{1})?$/, message: "请输入", trigger: "blur" }
const dateRules = { 
  pattern: /^(19\d{2}|20\d{2})-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$/, 
  message: "请输入正确的日期格式", 
  trigger: "blur" 
}
const timeRules = {
  pattern:/^(19\d{2}|20\d{2})-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]) (0\d|1\d|2[0-3]):([0-5]\d):([0-5]\d)$/,
  message: "请输入正确的时间格式", 
  trigger: "blur" 
}
const stringRule = {pattern: /^[A-Za-z0-9]{0,15}$/, message: "请输入15个字符（不含中文）", trigger: "blur" }
const rules = reactive({
  radarSignalNum: [{ pattern: /^11\d{6}$/, message: "请输入11开头的8位整数", trigger: "blur" }],
  radarFrequency: [commonNumberRules],
  signalBandwidth: [commonNumberRules],
  signalStrength: [pointNumberRules],
  cnr: [pointNumberRules],
  interceptTimes: [commonNumberRules],
  initialInterceptionDate: [dateRules],
  initialInterceptionTime: [timeRules],
  latestInterceptionDate: [dateRules],
  latestInterceptionTime: [timeRules],
  batchNumber: [commonNumberRules],
  operatingFrequencyNum: [commonNumberRules],
  operatingFrequencyValue: [commonNumberRules],
  frequencyCenterValue: [commonNumberRules],
  frequencyMaxValue: [commonNumberRules],
  frequencyMinValue: [commonNumberRules],
  positionNorth: [pointNumberRules],
  positionCar: [pointNumberRules],
  pitchAngle: [pointNumberRules],
  pitchAngleCar: [pointNumberRules],
  amplitudeCenterValue: [pointDigitNumberRules],
  amplitudeMaxValue: [pointDigitNumberRules],
  amplitudeMinValue: [pointDigitNumberRules],
  pulseWidthNum: [commonNumberRules],
  pulseWidthValue: [commonNumberRules],
  pulseWidthUpValue: [commonNumberRules],
  pulseWidthDownValue: [commonNumberRules],
  doubleCycleNum: [commonNumberRules],
  doubleCycle:[{ pattern: /^[0-9,]+$/, message: "请重新输入", trigger: "blur" }],
  doubleCycleValue: [commonNumberRules],
  doubleCycleMax: [commonNumberRules],
  doubleCycleMin: [commonNumberRules],
  radarModel:[stringRule],
  modulationCodes:[commonNumberRules],
  codeWidth:[commonNumberRules],
  bandwidth:[commonNumberRules],
  linearFrequencyModulationSlope:[commonNumberRules],
  directionality:[pointDigitNumberRules],
  pitchAngleLast:[pointDigitNumberRules],
  directionFindingQuality:[{ pattern: /^(100|([1-9][0-9]?|0))$/, message: "请输入0到100的数字", trigger: "blur" }],
  signalStrengthLast:[pointNumberRules],
});
const form = ref({})
const editform = ref({})
const addRadarFn = () => {
  addRadarDialog.value = true
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      addRadar(form.value).then(res=>{
        addRadarDialog.value = false
        getList()
        formRef.value.resetFields();
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
const closeModel = () => {
  addRadarDialog.value = false
}
const delRadarFn = () => {
  if(deleteId.value.length !== 0){
    delRadar(deleteId.value).then(res=>{
      console.log(res)
      getList()
    })
  }else{
    alert('请选择删除的内容')
  }
}
const editRadarFn = (row) => {
  editform.value  = { ...row }
  editRadarDialog.value = true
}

const submitEditForm = () => {
  updateRadar(editform.value).then(res=>{
    getList()
    editRadarDialog.value = false
  })
} 
const closeEditModel = () => {
  editRadarDialog.value = false
}
/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  selection.map(item=>{
    deleteId.value.push(item.radarSignalId)
    deleteId.value = [...new Set(deleteId.value)];
  })
}
const getList = () => {
  listRadar().then(res=>{
    console.log(res)
    radarList.value = res.data
  })
}
const addfun = () => {
  addDialog.value = true
}
onMounted(() => {
  getList()
})

watchEffect(() => {})

computed(() => {})
</script>
<style>
.online-main{
  height: 100%;
  display: flex;
  flex-direction: column;
}
.titleStyle{
  margin-bottom: 11px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}
</style>