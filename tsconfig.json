{
  "compilerOptions": {
    "outDir": "./",
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "noImplicitAny": false,
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "allowJs": true,
    "paths": {
      "@/*": ["./src/*"]
    },
    "types": [
      "vite/client",
      "element-plus/global",
      "@types/node",
      "@vue/runtime-core",
      "@types/amap-js-api"
    ],
    "skipLibCheck": true /* Skip type checking all .d.ts files. */,
    "allowSyntheticDefaultImports": true /* 允许默认导入 */,
    "forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */
  },
  "include": ["src/**/*.ts", "src/**/*.vue", "src/types/**/*.d.ts"],
  "exclude": ["node_modules", "dist", "**/*.js"]
}
