<template>
  <div class="constellation-main">
    <!-- 播放控制栏 -->
    <!--todo-分析播放控制逻辑完善  -->
    <!-- <PlayBack :pollingFunction="pollingPlayBack" /> -->
    <div class="charts-body">
      <el-row v-if="loaded" class="chart-row" :gutter="16">
        <el-col
          v-for="(comp, index) in zeroSpanViews"
          :key="comp.name"
          :span="12"
          :style="generateStyle(index)"
        >
          <div class="chart-module">
            <component :is="comp.comp" />
          </div>
        </el-col>
      </el-row>
    </div>
    <div v-if="loaded && !chartsStore.static.enabled" class="charts-playbar">
      <Playbar :reload-charts="reloadCharts" />
    </div>
  </div>
</template>

<script setup name="ZeroSpan">
  // 零扫宽
  import useChartsStore from '@/store/modules/charts'
  import { defineComponent } from 'vue'
  import AnalyseHeader from '../header'
  import { defineAsyncComponent } from 'vue'
  // todo-分析播放控制逻辑完善
  // import PlayBack from '@/components/PlayBack' //播放组件
  // const pollingPlayBack = () => {}

  defineComponent([AnalyseHeader])
  const Playbar = defineAsyncComponent(() => import('../playbar/index.vue'))
  const chartsStore = useChartsStore()
  const { zeroSpanViews } = chartsStore
  const loaded = ref(false)

  const reloadCharts = () => {
    loaded.value = false
    nextTick(() => {
      loaded.value = true
    })
  }
  const generateStyle = index => {
    if (index > 1) {
      return { marginTop: '16px' }
    }
  }
  onMounted(() => {
    // 将本地缓存的内容写入store里面
    chartsStore.readStorage()
    loaded.value = true
  })

  onUnmounted(() => {
    chartsStore.writeStorage()
  })
</script>

<style lang="scss" scoped>
  .constellation-main {
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    .charts-body {
      box-sizing: border-box;
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
      .chart-row {
        .el-col {
          height: 50%;
          .chart-module {
            border: 1px solid var(--el-border-color);
          }
        }
      }
    }
    .charts-playbar {
      margin-top: 20px;
    }
  }
</style>
