<template>
  <data-form :model="model" :fields="fields" />
</template>

<script setup>

import DataForm from '@/components/DataForm'
import { levelUnits } from '@/constant/units'
import ModelData from '@/common/classes/modelData'

const messuseTypes = [
    { label: '立即触发', value: 0 },
    { label: '视频触发', value: 1 },
    { label: '外部触发', value: 2 },
  ]

const fields = [   
  { target: 'triggerType', name: "触发类型", options: messuseTypes, isSelect: true },
  { target: 'triggerLevel', name: "触发电平", options: levelUnits, appendSelect: true },
  { target: 'triggerTimeOut', name: "触发延时", appendText: 'syms' },
]

const model = reactive(new ModelData(fields, 'dm'))
</script>
