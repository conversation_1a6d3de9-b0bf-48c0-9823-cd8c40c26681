<template>
  <el-dialog v-model="dia" width="800px" append-to-body title="历史答疑">
    <div style="height:300px;overflow: auto;">
      <el-collapse v-for="v in form" :key="v" v-model="activeNames">
        <el-collapse-item :title="v.anBy + '&nbsp&nbsp' + v.anTime" name="1">    
          <div>{{ v.anContent || '暂无答疑' }}</div>
        </el-collapse-item>
      </el-collapse>
    </div> 
    <template #footer>
      <el-button @click="dia = false">取消</el-button>
    </template>
  </el-dialog>
</template>
<script>
  export default {
    name: 'History',
    emits: ['actSuccess'],
    data () {
      return {
        dia: false,
        loading: false,
        activeNames:['1'],
        form:{}      
      }
    },
    methods: {
      create () {},
      show (rows) {
        this.form = rows
        this.dia = true
      }

    }
  }
</script>
<style></style>
