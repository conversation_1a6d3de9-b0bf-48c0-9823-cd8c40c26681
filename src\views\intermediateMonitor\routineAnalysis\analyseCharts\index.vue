<template>
  <div class="scan-charts">
    <div class="scan-toolbar">
      <div class="pd6 br1">
        <el-button v-if="viewType === 'scan'" @click="switchType">
          {{ model.type ? '离散' : '全景' }}
        </el-button>
      </div>
      <el-checkbox-group v-model="checkList" class="pd6 br1">
        <el-checkbox
          v-for="item in lineTypes"
          :key="item.value"
          v-model="item.value"
          :label="item.label"
          border
        />
      </el-checkbox-group>
      <div class="pd6 br1">
        <el-button @click="showResetZoom">全频段</el-button>
      </div>
      <div class="pd6 bl1" style="display: inline-flex">
        <el-select
          v-model="selectedFreStds"
          :teleported="false"
          multiple
          collapse-tags
          collapse-tags-tooltip
          effect="dark"
          value-key="label"
        >
          <el-option
            v-for="item in frequencyStandards"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
        <el-checkbox v-model="isActive" label="激活" border />
        <el-checkbox v-model="isTopFollow" label="峰值跟踪" border />
      </div>
      <div v-if="viewType !== 'signal'" class="pd6 bl1">
        <el-button @click="switchAssit">{{ assistChartName }}</el-button>
      </div>

      <div class="pd6 br1">
        <el-button @click="switchToWarning">告警设置</el-button>
      </div>
      <div class="flex pd6 br1">
        <el-button :disabled="props.model.status === 1" @click="selfTest">设备自检</el-button>
      </div>
      <!-- 设备选择 -->
      <el-select
        v-if="viewType === 'scan'"
        v-model="deviceCode"
        :teleported="false"
        effect="dark"
        value-key="label"
        class="pd6 bl1 w-[120px] equip"
      >
        <el-option
          v-for="item in deviceEnum"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </el-select>
    </div>
    <div class="main" style="display: flex">
      <!-- 频谱扫描echarts -->
      <div class="scanCharts" style="flex: 1">
        <Spectrum
          ref="spectrumRef"
          :key="useKey"
          :usekey="useKey"
          :show-lines="checkList"
          :selected-fremarkers="selectedFreStds"
          :is-active="isActive"
          :is-top-follow="isTopFollow"
          :data-list="dataList"
          :model="model"
        />
        <!-- 瀑布图 -->
        <div v-show="assistChartType === 0" class="water-fall">
          <WaterFallPlot
            :is-online="true"
            :data="dataList.current"
            :height="50"
            :legend-width="60"
            :container-height="isRadarAnalyse ? 200 : 300"
          />
        </div>
        <!-- 占用度 -->
        <div v-show="assistChartType === 1" class="occupancy">
          <Occupancy :data="dataList.occupancy" :model="model" />
        </div>
      </div>
    </div>

    <el-dialog v-model="open" title="设备自检信息" width="400px" align-center append-to-body center>
      <div v-loading="wsLoading">
        <div class="m-2 text-lg">设备状态：{{ getDeviceStatusType(deviceStatus) }}</div>
        <div v-if="deviceStatus === 1" class="m-2 text-lg"> 状态信息：{{ message }} </div>
        <div class="m-2 text-lg">时统设备：{{ getTimeDeviceType(timeType) }}</div>
        <div class="m-2 text-lg">时统时间：{{ timeMeasurement }}</div>
        <div class="m-2 text-lg">经度：{{ longitude }} </div>
        <div class="m-2 text-lg">纬度：{{ latitude }}</div>
      </div>

      <template #footer>
        <div class="flex items-center justify-center">
          <el-button type="primary" @click="open = false">确 定</el-button>
          <el-button @click="open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import useScanStore from '@/store/modules/scanMonitor'
  import Spectrum from '@/components/SpectrumSignal/Spectrum.vue'
  import WaterFallPlot from '@/views/analyse/modules/WaterFallPlot.vue'
  import Occupancy from './Occupancy.vue'
  import { lineTypes } from '@/constant'
  import { ElMessage } from 'element-plus'
  import { linkWs } from '@/api/upperComputer/scan'
  import { DEVICE_SEARCH, DEVICE_STATUS_SEARCH } from '@/constant/funCodes'
  import router from '@/router'

  const props = defineProps({
    viewType: {
      type: String,
      default: 'scan' // scan 扫描  monitor 监测
    },
    dataList: {
      type: Object,
      default: () => ({})
    },
    model: {
      type: Object,
      default: () => ({})
    },
    useKey: {
      type: String,
      default: 'default'
    },
    deviceEnum: {
      type: Array,
      default: () => []
    }
  })

  const open = ref(false)
  const wsLoading = ref(false)
  const longitude = ref('--')
  const latitude = ref('--')
  const timeType = ref('--')
  const timeMeasurement = ref('--')
  const message = ref('--')
  const deviceStatus = ref('--')

  const getDeviceStatusType = type => {
    switch (type) {
      case 0:
        return '正常'
      case 1:
        return '异常'
      case 2:
        return '正在执行任务'
      default:
        return '--'
    }
  }

  const getTimeDeviceType = type => {
    switch (type) {
      case 1:
        return '北斗/GPS一体机'
      case 2:
        return '北斗'
      case 3:
        return 'GPS'
      case 4:
        return '其他'
      default:
        return '--'
    }
  }

  const frequencyStandards = [
    { label: '频标1', value: 1 },
    { label: '频标2', value: 2 },
    { label: '频标3', value: 3 },
    { label: '频标4', value: 4 },
    { label: '频标5', value: 5 }
  ]

  const scanStore = useScanStore()
  const spectrum = scanStore.routineAnalysis

  const deviceCode = defineModel('deviceCode')
  let isRestoring = false // 标志位
  watch(deviceCode, (newValue, oldValue) => {
    // 如果正在恢复旧值，直接返回，避免死循环
    if (isRestoring) {
      isRestoring = false
      return
    }
    if (spectrum.status === 1 && oldValue) {
      ElMessage.warning('任务执行过程中，请先停止当前任务')
      // 设置标志位，并恢复旧值
      isRestoring = true
      deviceCode.value = oldValue // 恢复旧值
    } else {
      // 在此处处理允许更改的逻辑
      console.log('Device code changed to:', newValue)
    }
  })

  // const checkList = ref(['实时', '平均', '最大', '最小', '门限'])
  const checkList = defineModel('checkList')
  const spectrumRef = ref(null)
  const selectedFreStds = ref([])
  const isActive = ref(true)
  const isTopFollow = ref(false)
  const assistChartType = ref(0)
  const assistChartName = computed(() => (assistChartType.value === 0 ? '瀑布图' : '占用度'))
  const isRadarAnalyse = computed(() => {
    return props.model.hideTab && props.model.type // 这个代表是雷达分析
  })
  const switchType = () => {
    props.model.type = Number(!props.model.type)
  }
  const switchAssit = () => {
    assistChartType.value = Number(!assistChartType.value)
  }
  const showResetZoom = () => {
    spectrumRef.value.instance.zoomOut()
  }

  const switchToWarning = () => {
    router.push({ path: '/warn/management' })
  }

  // 设备自检
  const selfTest = async () => {
    open.value = true
    wsLoading.value = true

    try {
      const paramResult = await paramSetting()
      if (paramResult) {
        const statusResult = await statusSetting()
        if (statusResult) {
          ElMessage.success('设备自检成功')
        }
      }
    } catch (error) {
      ElMessage.error('设备连接失败，请确保设备开机并连接正常后再试')
    } finally {
      wsLoading.value = false
    }
  }

  const handleWebSocket = (ws, resolve, reject, processResult) => {
    ws.onmessage = event => {
      try {
        const rsp = JSON.parse(event.data)
        console.log('收到数据:', rsp)
        if (rsp.data.msgHeader.taskCode === 1) {
          console.log('回执，跳过处理')
          return
        }

        if (rsp.code === 200) {
          console.log('处理设备数据:', rsp.data.result)
          processResult(rsp.data.result)
          resolve(true)
          ws.close()
        } else {
          console.log('rsp.code 不为200:', rsp.code)
          reject(new Error('设备连接失败，请确保设备开机并连接正常后再试'))
        }
      } catch (err) {
        console.log('处理数据出错:', err)
        reject(new Error('设备连接失败，请确保设备开机并连接正常后再试'))
      }
    }

    ws.onerror = err => {
      console.log('WebSocket连接错误:', err)
      reject(new Error('WebSocket连接错误'))
    }

    ws.onclose = () => {
      console.log('WebSocket连接已关闭')
    }
  }

  const paramSetting = () => {
    return new Promise(async (resolve, reject) => {
      let ws = null
      let data = {}
      try {
        ws = await linkWs({
          host: props.deviceEnum.find(item => item.value === deviceCode.value).ip,
          port: props.deviceEnum.find(item => item.value === deviceCode.value).port,
          taskFunCode: DEVICE_STATUS_SEARCH,
          sendTime: 10,
          deviceCode: deviceCode.value,
          data: encodeURIComponent(JSON.stringify(data))
        })

        handleWebSocket(ws, resolve, reject, result => {
          longitude.value = result.longitude || '--'
          latitude.value = result.latitude || '--'
          deviceStatus.value = result.deviceStatus.deviceStatus || '--'
          message.value = result.deviceStatus.message || '--'
          console.log(
            '开始处理数据:',
            longitude.value,
            latitude.value,
            deviceStatus.value,
            message.value
          )
        })
      } catch (err) {
        console.log('设备连接失败:', err)
        reject(new Error('设备连接失败，请确保设备开机并连接正常后再试'))
      }
    })
  }

  const statusSetting = () => {
    return new Promise(async (resolve, reject) => {
      let ws = null
      let data = {}
      try {
        ws = await linkWs({
          host: props.deviceEnum.find(item => item.value === deviceCode.value).ip,
          port: props.deviceEnum.find(item => item.value === deviceCode.value).port,
          taskFunCode: DEVICE_SEARCH,
          sendTime: 10,
          deviceCode: deviceCode.value,
          data: encodeURIComponent(JSON.stringify(data))
        })

        handleWebSocket(ws, resolve, reject, result => {
          timeType.value = result.deviceLocation.timeType || '--'
          timeMeasurement.value = result.deviceLocation.timeMeasurement || '--'
          console.log('开始处理数据:', timeType.value, timeMeasurement.value)
        })
      } catch (err) {
        console.log('设备连接失败:', err)
        reject(new Error('设备连接失败，请确保设备开机并连接正常后再试'))
      }
    })
  }
</script>

<style scoped>
  .scan-charts {
    padding: 12px 8px;
  }

  .scan-toolbar {
    display: flex;
    border-top: 1px solid var(--toolbar-color);
    border-bottom: 1px solid var(--toolbar-color);
  }

  .pd6 {
    padding: 6px;
  }

  .br1 {
    border-right: 1px solid var(--toolbar-color);
  }

  .bl1 {
    border-left: 1px solid var(--toolbar-color);
  }
  :deep(.el-input__inner) {
    color: #ffffff;
  }
</style>
