<template>
  <div class="online-container app-container">
    <el-row class="main">
      <el-col :span="18" class="chart-info">
        <ScanSection>
          <div class="charts">
            <AnalyseCharts
              view-type="signal"
              usekey="signal"
              :data-list="dataList"
              :model="signal"
              :device-enum="deviceList"
              :device-list="deviceDataList"
              v-model:deviceCode="deviceCode"
            >
              <cpe-chart :iq-data="iqData" :audio-waveform="audioWaveform" />
            </AnalyseCharts>
          </div>
          <div class="tabs">
            <BottomTable :param-list="paramList" :modulation-list="modulationList" />
          </div>
        </ScanSection>
      </el-col>
      <el-col :span="6" class="chart-form">
        <ScanSection class="charts">
          <div class="form">
            <RightSettings :model="signal" @start="startWebsocket" @stop="closeScan">
              <template #default>
                <FreBandSetting ref="freBandRef" />
              </template>
              <template #formItem="{ form }">
                <el-form-item label="设备参数">
                  <el-button :loading="paramsLoading" @click="paramSetting(form)">
                    设备参数设置
                  </el-button>
                </el-form-item>
              </template>
            </RightSettings>
          </div>
        </ScanSection>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="ScanSignalAnalyse">
  import ScanSection from '@/components/SpectrumSignal/ScanSection.vue'
  import AnalyseCharts from './analyseCharts/index.vue'
  import FreBandSetting from './rightSettings/FreBandSetting.vue'
  import RightSettings from '@/components/SettingsFormPro/index.vue'
  import BottomTable from './bottomTable'
  import CpeChart from './charts'
  import useScanStore from '@/store/modules/scanMonitor'
  import useLinkWsHook from '@/common/hooks/linkWs'
  import { SIGNAL_ORIENTATION_CODE, SIGNAL_ANALYSE_CODE, PARAMS_SETTING } from '@/constant/funCodes'
  import { signalParamTypes, demodulateTypes } from '@/constant/types'
  import { debounce } from 'lodash'
  import { computed, ref, watch, onMounted, onActivated, nextTick, provide } from 'vue'
  import { plotToNum } from '@/utils/utils'
  import { getEquipList } from '@/api/system/equipment'
  import { deviceParams } from '@/constant/types'
  import { linkWs } from '@/api/upperComputer/scan'
  import { ElMessage, ElLoading } from 'element-plus'

  const scanStore = useScanStore()
  const { signal } = useScanStore()
  const dataList = ref({
    current: [],
    average: [],
    max: [],
    min: [],
    limit: [],
    occupancy: []
  })
  const paramList = ref({
    frequency: null,
    bandwidth: null,
    dimensionCount: null,
    dimensions: [],
    elevationAngles: [],
    signalStrengths: []
  })

  const route = useRoute()
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10
  })
  const deviceCode = ref(null)
  const host = ref(null)
  const port = ref(null)
  const deviceList = ref([])
  const resData = ref({})
  const freBandRef = ref(null)
  const deviceData = ref({})
  const deviceDataList = ref([])

  const paramsLoading = ref(false)
  const paramSetting = async form => {
    let ws = null
    const data = {
      parameterCount: deviceParams.length,
      parameterCode: deviceParams.map(item => item.code),
      parameterValue: deviceParams.map(item => form[item.key])
    }
    paramsLoading.value = true
    host.value = codeFindInfo.value.ip
    port.value = codeFindInfo.value.port
    ws = await linkWs({
      host: host.value,
      port: port.value,
      taskFunCode: PARAMS_SETTING,
      sendTime: 10,
      deviceCode: deviceCode.value,
      data: encodeURIComponent(JSON.stringify(data))
    })
    ws.onmessage = event => {
      paramsLoading.value = false
      try {
        const rsp = JSON.parse(event.data)
        if (rsp.code === 200) {
          ElMessage.success('设备参数设置成功')
        } else {
          ElMessage.error('设备参数设置失败')
        }
      } catch (err) {
        console.log(err)
      }
      ws.close()
    }
  }

  const getList = async () => {
    deviceDataList.value = []
    deviceData.value = []
    const response = await getEquipList(queryParams.value)
    deviceDataList.value = response.data.list
    deviceData.value = response.data.list[0]
    deviceList.value = response.data.list.map(item => ({
      label: item.name,
      value: item.code,
      ip: item.ip,
      port: item.port
    }))
  }

  const init = async () => {
    await getList()
    await dealRouteInfo()
  }

  watch(deviceCode, newValue => {
    const idx = deviceDataList.value.findIndex(item => item.code === deviceCode.value)
    const resData = deviceDataList.value[idx]
    scanStore.setSpectrumData('signal', {
      centerFreq: resData.deviceScan.centerFreq
    })
    scanStore.updateTaskParams('signal', resData.devicePara)
    freBandRef.value.dataChangeFun()
  })

  const codeFindInfo = computed(() => {
    if (!deviceCode.value) {
      return {}
    }
    const resIdx = deviceList.value.findIndex(item => item.value === deviceCode.value)
    return resIdx !== -1 ? deviceList.value[resIdx] : resData.value || {}
  })

  const selectForm = ref({})
  watchEffect(() => {
    selectForm.value = freBandRef.value?.selectForm || {}
  })

  provide('selectForm', selectForm)

  const dealRouteInfo = async () => {
    if (route.query?.data) {
      resData.value = JSON.parse(route.query.data)
      deviceCode.value = resData.value.code
      scanStore.setSpectrumData('signal', {
        centerFreq: resData.value.centerFreq
      })
      await nextTick()
      signal.centerFreq = resData.value.centerFreq
      scanStore.updateTaskParams('signal', resData.value.taskParams)
      freBandRef.value.dataChangeFun()
    } else {
      scanStore.setSpectrumData('signal', {
        centerFreq: deviceData.value.deviceScan?.centerFreq
      })
      scanStore.updateTaskParams('signal', deviceData.value.devicePara)
      freBandRef.value.dataChangeFun()
      deviceCode.value = deviceList.value.length > 0 ? deviceList.value[0].value : null
      host.value = deviceList.value.length > 0 ? deviceList.value[0].ip : null
      port.value = deviceList.value.length > 0 ? deviceList.value[0].port : null
    }
  }

  onMounted(init)
  onActivated(init)

  const modulationList = ref([])
  const iqData = ref({ i: [], q: [] })
  const audioWaveform = ref([])
  const taskFunCode = computed(() =>
    signal.commandType === 1 ? SIGNAL_ORIENTATION_CODE : SIGNAL_ANALYSE_CODE
  )
  const { linkScan, closeScan, pageData } = useLinkWsHook(signal, taskFunCode)

  watch(
    () => pageData.value,
    data => {
      const domain =
        taskFunCode.value === SIGNAL_ORIENTATION_CODE
          ? data.singleSignal
          : data.signalParameterMeasurement
      if (domain) {
        updateChartData(domain)
        updateParamList(domain)
        updateIqData(domain)
        updateModulation(domain)
        updateAudioWave(domain)
      }
    }
  )

  const updateChartData = data => {
    if (data?.spectralDataList) {
      dataList.value.current = data.spectralDataList
    }
  }

  const updateParamList = data => {
    if (!data?.continuousWave) {
      return
    }
    paramList.value = {}
    const keys = Object.keys(signalParamTypes)
    Object.keys(data.continuousWave).forEach(key => {
      if (keys.includes(key)) {
        paramList.value[key] = {
          value: data.continuousWave[key],
          max: data.continuousWave[key + 'Max'],
          min: data.continuousWave[key + 'Min'],
          average: data.continuousWave[key + 'Avg']
        }
      }
    })
  }

  const updateIqData = data => {
    if (!data?.iqFromCrList || data.iqFromCrList.length === 0) {
      return
    }
    const obj = { i: [], q: [] }
    data.iqFromCrList.forEach(({ dataI, dataQ }) => {
      obj.i.push(dataI)
      obj.q.push(dataQ)
    })
    if (data.continuousWave?.codeRate && data.samplingRate) {
      obj.eyeStep = Math.round(data.sampleSize / data.continuousWave.codeRate)
    }
    iqData.value = obj
  }

  const updateModulation = data => {
    if (data?.continuousWave?.modulationMap && taskFunCode.value === SIGNAL_ANALYSE_CODE) {
      modulationList.value = Object.values(data.continuousWave.modulationMap)
        .filter(item => item.modulationNm !== 0)
        .sort((a, b) => b.num - a.num)
    }
  }

  const updateAudioWave = data => {
    if (data?.audioDataBlockShort) {
      audioWaveform.value = Array.isArray(data.audioDataBlockShort)
        ? data.audioDataBlockShort
        : data.audioDataBlockShort.split('').map(item => parseInt(item.charCodeAt(0), 10))
    }
  }

  const generateParams = form => {
    const { centerFreq, bandwidth, demodulationBandwidth, demodulationType } = signal
    if (taskFunCode.value === SIGNAL_ANALYSE_CODE) {
      const [minAzimuth, maxAzimuth] = signal.azimuth
      const [minElevation, maxElevation] = signal.pitch
      return {
        dataAcquisitionSwitch: 1,
        polarization: parseInt(form.polarizationType, 16),
        signalStrengthSwitch: 1,
        frequency: plotToNum(centerFreq + selectForm.value.centerFreq),
        analysisBandwidth: bandwidth,
        demodulationBandwidth,
        demodulationMode: demodulationType,
        dbBandwidthSelection: signal.bBandwidth,
        maxAzimuth,
        minAzimuth,
        minElevation,
        maxElevation
      }
    } else if (taskFunCode.value === SIGNAL_ORIENTATION_CODE) {
      return {
        dataAcquisitionSwitch: 1,
        polarization: parseInt(form.polarizationType, 16),
        analysisBandwidth: bandwidth,
        demodulationBandwidth,
        demodulationMode: demodulationType,
        threshold: form.threshold
      }
    }
  }
  const startWebsocket = debounce(form => {
    const params = generateParams(form)
    host.value = codeFindInfo.value.ip
    port.value = codeFindInfo.value.port
    linkScan(params, host.value, port.value, deviceCode.value)
  }, 300)
</script>

<style scoped lang="scss">
  @import '@/assets/styles/mixin.scss';

  .online-container {
    @include boxBg('@/assets/images/boxBg.png');
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 1rem;
    overflow-y: auto;

    .main {
      height: 100%;
    }
  }

  .chart-form {
    padding-left: 10px;
  }
</style>
