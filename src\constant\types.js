// 载频类型（工作频率取值定义）
export const workingFrequencyTypes = [
  { label: '未知', value: 0 },
  { label: '频率固定', value: 1 },
  { label: '射频可选择', value: 2 },
  { label: '频率分集', value: 3 },
  { label: '频率编码', value: 4 },
  { label: '射频捷变[脉间捷变]', value: 5 },
  { label: '射频参差', value: 6 },
  { label: '连续波频率调制', value: 7 },
  { label: '连续波编码调制', value: 8 },
  { label: '连续波噪声调制', value: 9 },
  { label: '脉内频率编码[脉内捷变]', value: 10 },
  { label: '频率组变[脉组捷变]', value: 11 },
  { label: '频率分集[同时分集]', value: 12 },
  { label: '无效', value: 255 }
]
// 脉宽类型取值定义
export const pulseWidthTypes = [
  { label: '未知', value: 0 },
  { label: '固定', value: 1 },
  { label: '脉宽分集', value: 2 },
  { label: '脉宽捷变', value: 3 },
  { label: '双脉冲', value: 4 },
  { label: '多脉冲', value: 5 },
  { label: '连续波', value: 6 },
  { label: '脉冲复合', value: 7 },
  { label: '脉宽可选择', value: 8 },
  { label: '脉宽编码', value: 9 },
  { label: '脉宽抖动', value: 10 },
  { label: '脉宽调制', value: 11 },
  { label: '脉冲组', value: 12 },
  { label: '无效', value: 255 }
]
// 脉冲重复周期类型取值定义
export const recurrenceTypes = [
  { label: '未知', value: 0 },
  { label: '脉冲重复周期固定', value: 1 },
  { label: '脉冲重复周期可选择', value: 2 },
  { label: '脉冲重复周期抖动', value: 3 },
  { label: '脉冲重复周期捷变', value: 4 },
  { label: '脉冲重复周期参差', value: 5 },
  { label: '脉冲重复周期编码', value: 6 },
  { label: '脉冲重复周期滑变', value: 7 },
  { label: '脉冲多普勒', value: 8 },
  { label: '脉冲重复周期驻留', value: 9 },
  { label: '脉冲重复周期转换', value: 10 },
  { label: '脉冲重复周期交替', value: 11 },
  { label: '脉冲重复周期复合', value: 12 },
  { label: '脉冲重复周期脉间参差', value: 12 },
  { label: '脉冲重复周期脉组参差（组变）', value: 12 },
  { label: '无效', value: 255 }
]
// 测量模式
export const measureModes = [
  { label: '低噪声', value: 0 },
  { label: '一般', value: 1 },
  { label: '低失真', value: 2 }
]
// 极化方式
export const polarTypes = [
  { label: '水平极化', value: 0, text: 'H' },
  { label: '垂直极化', value: 1, text: 'V' },
  { label: '右旋圆极化', value: 2, text: 'CR' },
  { label: '左旋圆极化', value: 3, text: 'CL' },
  { label: '右倾斜极化', value: 4, text: 'SR' },
  { label: '左倾斜极化', value: 5, text: 'SL' },
  { label: '双极化', value: 6, text: 'D' },
  { label: '混合极化', value: 7, text: 'M' },
  { label: '线极化', value: 8, text: 'L' },
  { label: '左旋椭圆极化', value: 9, text: 'TL' },
  { label: '右旋椭圆极化', value: 10, text: 'TR' }
]
// 检波方式
export const detectionTypes = [
  { label: '峰值(peak)', value: 0 },
  { label: '平均值(avg)', value: 1 },
  { label: '实时值(fast)', value: 2 },
  { label: '均方根(rms)', value: 3 },
  { label: '准峰值(qbk)', value: 3 }
]
// 衰减模式 0-关；1-人工衰减控制；2-自动衰减控制；3-低噪声；4-低损耗；5-常规(3~5对R&S设备有效)
export const attenuationModes = [
  { label: '关', value: 0 },
  { label: '人工衰减', value: 1 },
  { label: '自动衰减', value: 2 },
  { label: '低噪声', value: 3 },
  { label: '低损耗', value: 4 },
  { label: '常规', value: 5 }
]
// 增益模式 0-关；1-人工衰减控制；2-自动衰减控制；3-低噪声；4-低损耗；5-常规(3~5对R&S设备有效)
export const gainModes = [
  { label: '关', value: 0 },
  { label: '人工增益', value: 1 },
  { label: '自动增益', value: 2 }
]
// 门限类型 0代表自动，1代表手动
export const thresholdTypes = [
  { label: '自动', value: 0 },
  { label: '手动', value: 1 }
]

// 解调方式
export const demodulateTypes = [
  {
    label: 'UN',
    value: 0,
    text: '无调制'
  },
  {
    label: 'AM',
    value: 1,
    text: '调幅'
  },
  {
    label: 'FM',
    value: 2,
    text: '窄调频'
  },
  {
    label: 'WFM',
    value: 3,
    text: '宽调频'
  },
  {
    label: 'LSB',
    value: 4,
    text: '下边带调制'
  },
  {
    label: 'USB',
    value: 5,
    text: '上边带调制'
  },
  {
    label: 'ISB',
    value: 6,
    text: '独立边带调制'
  },
  {
    label: 'ASK',
    value: 7,
    text: '振幅键控调制'
  },
  {
    label: 'FSK',
    value: 8,
    text: '频移键控调制'
  },
  {
    label: 'PSK',
    value: 9,
    text: '相移键控调制'
  },
  {
    label: 'MPSK',
    value: 10,
    text: '多进制相移键控调制'
  },
  {
    label: 'MFSK',
    value: 11,
    text: '多进制频移键控调制'
  },
  {
    label: 'GMSK',
    value: 12,
    text: '预调制高斯滤波最小频移键控调制'
  },
  {
    label: 'MSK',
    value: 13,
    text: '最小频移键控调制'
  },
  {
    label: 'TFM',
    value: 14,
    text: '平滑调频'
  },
  {
    label: 'PCM',
    value: 15,
    text: '脉冲编码调制'
  },
  {
    label: 'QAM',
    value: 16,
    text: '正交幅度调制'
  },
  {
    label: 'CFM',
    value: 17,
    text: '连续波频率调制'
  },
  {
    label: 'CCM',
    value: 18,
    text: '连续波编码调制'
  },
  {
    label: 'CNM',
    value: 19,
    text: '连续波噪音调制'
  },
  {
    label: 'LFM',
    value: 20,
    text: '线性调频'
  },
  {
    label: 'NLFM',
    value: 21,
    text: '非线性调频'
  },
  {
    label: 'BPC',
    value: 22,
    text: '二相编码'
  },
  {
    label: 'QPC',
    value: 23,
    text: '四相编码'
  },
  {
    label: 'FC',
    value: 24,
    text: '脉冲频率编码'
  },
  {
    label: 'PNM',
    value: 25,
    text: '脉冲无调制'
  },
  {
    label: 'QS',
    value: 26,
    text: '其它调制方式'
  },
  {
    label: 'BPSK',
    value: 31,
    text: '二相相移键控'
  },
  {
    label: 'QPSK',
    value: 32,
    text: '四相相移键控'
  },
  {
    label: '8PSK',
    value: 33,
    text: '八相相移键控'
  },
  {
    label: '2FSK',
    value: 34,
    text: '二频移键控调制'
  },
  {
    label: '4FSK',
    value: 35,
    text: '四频移键控调制'
  },
  {
    label: 'SSB',
    value: 39,
    text: '单边带'
  },
  {
    label: 'DSB',
    value: 40,
    text: '双边带'
  },
  {
    label: 'RSB',
    value: 41,
    text: '残余边带'
  },
  {
    label: 'PAM',
    value: 42,
    text: '脉冲调幅'
  },
  {
    label: 'OFDM',
    value: 46,
    text: '正交频分多路'
  },
  {
    label: 'PWM',
    value: 51,
    text: '脉冲宽度调制'
  },
  {
    label: 'CW',
    value: 52,
    text: '连续波'
  },
  {
    label: 'IQ',
    value: 53,
    text: '相移调变'
  },
  {
    label: '16QAM',
    value: 60,
    text: '十六正交幅度调制'
  },
  {
    label: '32QAM',
    value: 61,
    text: '32正交幅度调制'
  },
  {
    label: '64QAM',
    value: 62,
    text: '64正交幅度调制'
  },
  {
    label: '128QAM',
    value: 63,
    text: '128正交幅度调制'
  },
  {
    label: '256QAM',
    value: 64,
    text: '256正交幅度调制'
  },
  {
    label: '512QAM',
    value: 65,
    text: '512正交幅度调制'
  },
  {
    label: '1024QAM',
    value: 66,
    text: '1024正交幅度调制'
  },
  {
    label: 'TETRA',
    value: 128,
    text: 'TETRA'
  },
  {
    label: 'DMR',
    value: 129,
    text: 'DMR'
  },
  {
    label: 'dPMR',
    value: 130,
    text: 'dPMR'
  },
  {
    label: 'NXDN',
    value: 131,
    text: 'NXDN'
  },
  {
    label: 'PDT',
    value: 132,
    text: 'PDT'
  },
  {
    label: 'DRM',
    value: 133,
    text: 'DRM'
  },
  {
    label: 'iDEN',
    value: 134,
    text: 'iDEN'
  }
]


// xdbBandwidth
export const xdbBandwidthTypes = [
  {
    label: '3',
    value: 3,
  },
  {
    label: '6',
    value: 6,
  },
  {
    label: '26',
    value: 26,
  }
]

// 音频解调方式
export const audioDemodulationTypes = [
  {
    label: 'AM',
    value: 1,
    text: '调幅'
  },
  {
    label: 'FM',
    value: 2,
    text: '窄调频'
  }
]
// 解调带宽
export const demodulateBw = [
  {
    label: '1kHz',
    value: 1000,
  },
  {
    label: '1.25kHz',
    value: 1250,
  },
  {
    label: '1.5kHz',
    value: 1500,
  },
  {
    label: '2kHz',
    value: 2000,
  },
  {
    label: '2.5kHz',
    value: 2500,
  },
  {
    label: '3.125kHz',
    value: 3125,
  },
  {
    label: '4kHz',
    value: 4000,
  },
  {
    label: '4.8kHz',
    value: 4800,
  },
  {
    label: '5kHz',
    value: 5000,
  },
  {
    label: '6kHz',
    value: 6000,
  },
  {
    label: '6.25kHz',
    value: 6250,
  },
  {
    label: '9kHz',
    value: 9000,
  },
  {
    label: '12.5kHz',
    value: 12500,
  },
  {
    label: '30kHz',
    value: 30000,
  },
  {
    label: '50kHz',
    value: 50000,
  },
  {
    label: '120kHz',
    value: 120000,
  },
  {
    label: '250kHz',
    value: 250000,
  },
  {
    label: '300kHz',
    value: 300000,
  },
  {
    label: '800kHz',
    value: 800000,
  },
]

// 解调带宽
export const analyzingBw = [
  {
    label: '1kHz',
    value: 1000,
  },
  {
    label: '1.25kHz',
    value: 1250,
  },
  {
    label: '1.5kHz',
    value: 1500,
  },
  {
    label: '2kHz',
    value: 2000,
  },
  {
    label: '2.5kHz',
    value: 2500,
  },
  {
    label: '3.125kHz',
    value: 3125,
  },
  {
    label: '4kHz',
    value: 4000,
  },
  {
    label: '4.8kHz',
    value: 4800,
  },
  {
    label: '5kHz',
    value: 5000,
  },
  {
    label: '6kHz',
    value: 6000,
  },
  {
    label: '6.25kHz',
    value: 6250,
  },
  {
    label: '9kHz',
    value: 9000,
  },
  {
    label: '12.5kHz',
    value: 12500,
  },
  {
    label: '30kHz',
    value: 30000,
  },
  {
    label: '50kHz',
    value: 50000,
  },
  {
    label: '120kHz',
    value: 120000,
  },
  {
    label: '250kHz',
    value: 250000,
  },
  {
    label: '300kHz',
    value: 300000,
  },
  {
    label: '800kHz',
    value: 800000,
  },
  {
    label: '2MHz',
    value: 2000000,
  },
  {
    label: '5MHz',
    value: 5000000,
  },
  {
    label: '8MHz',
    value: 8000000,
  },
  {
    label: '10MHz',
    value: 10000000,
  },
  {
    label: '12.5MHz',
    value: 12500000,
  },
  {
    label: '20MHz',
    value: 20000000,
  },
  {
    label: '40MHz',
    value: 40000000,
  },
  {
    label: '50MHz',
    value: 50000000,
  },
  {
    label: '60MHz',
    value: 60000000,
  },
  {
    label: '80MHz',
    value: 80000000,
  },
  {
    label: '100MHz',
    value: 100000000,
  }
]
// 定位带宽
export const positionBw = [
  {
    label: '1.25MHz',
    value: 1250000,
  },
  {
    label: '2.5MHz',
    value: 2500000,
  },
  {
    label: '5MHz',
    value: 5000000,
  },
  {
    label: '10MHz',
    value: 10000000,
  },

  {
    label: '20MHz',
    value: 20000000,
  },
  {
    label: '40MHz',
    value: 40000000,
  },
  {
    label: '80MHz',
    value: 80000000,
  },
]

export const paramTypes = {
  frequency: '信号频率（MHz）',
  bandwidth: '信号带宽（KHz）',
  dimensionCount: '示向度个数',
  dimensions: '示向度',
  elevationAngles: '俯仰角',
  signalStrengths: '信号场强(dBμV/m)'
}

export const signalParamTypes = {
  centerFrequency: '中心频率（MHz）',
  signalLevel: '信号电平（dBm）',
  xdbBandwidth: 'xdb带宽（kHz）',
  bbandwidth: 'β带宽（kHz）',
  codeRate: '码速率（kbps）',
  transferRate: '传输速率（kbps）',
  modulationDegree: '调制度（%）',
  frequencyOffset: '频偏（kHz）',
  modulationNm: '调制方式',

  // signalFieldStrength: '功率场强(dBμV/m)）'
}

// 信号类型
export const signalTypes = [
  { label: '未知', value: 0 },
  { label: '2GALE', value: 1 },
  { label: '3GALE', value: 2 },
  { label: 'Link11', value: 3 },
  { label: 'Link4A', value: 4 },
  { label: 'Link16', value: 5 },
  { label: 'AIS', value: 6 },
  { label: 'ADS_B', value: 7 },
  { label: '敌我识别', value: 8 },
  { label: '塔康', value: 9 },
  { label: '导航', value: 10 },
  { label: '移动通信', value: 11 },
  { label: '民用广播', value: 12 },
  { label: '扩展', value: [13, 254] },
  { label: '无效', value: 255 }
]

// 参数类型 代码
export const deviceParams = [
  { code: 0x00, label: '测量模式', key: 'measureMode' },
  // { code: 0x01, label: '增益模式', key: 'gainMode' },
  // { code: 0x02, label: '增益值', key: 'gain' },
  { code: 0x03, label: '衰减模式', key: 'attenuationMode' },
  { code: 0x04, label: '衰减值', key: 'attenuation' },
  // { code: 0x06, label: '检波方式', key: 'detectionMode' },
  // { code: 0x07, label: '驻留时间', key: 'lingerTime' }
]
