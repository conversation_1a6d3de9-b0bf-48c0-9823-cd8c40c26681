import { mergeChunks } from './upload'

let fileChunkList = {
  value: []
}

let fileHash = null

function setFileChunkList(val) {
  fileChunkList = val
}
function setFileHash(val) {
  fileHash = val
}

function clearFileHash(uploadSuccess, file, params) {
  if (!uploadSuccess && fileHash) {
    const formData = new FormData()
    formData.append('fileName', file.name)
    formData.append('md5', fileHash)
    mergeChunks(formData, params)
    fileChunkList.value = []
  }
  fileHash = null
}

export { fileChunkList, setFileChunkList, fileHash, setFileHash, clearFileHash }
