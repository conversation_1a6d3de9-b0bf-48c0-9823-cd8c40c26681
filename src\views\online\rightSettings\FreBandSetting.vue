<template>
  <div>
    <el-form class="left">
      <el-form-item label="中心频率">
        <el-input v-model="form.centerFreq" type="number" @change="dataChangeFun('centerFreq')">
          <template #append>
            <el-select v-model="selectForm.centerFreq" @change="unitChange('centerFreq')">
              <el-option v-for="unit in units" :key="unit" :value="unit" />
            </el-select>
          </template>
        </el-input>
        <!-- <el-button type="primary" style="position: absolute; right: -85px" @click="monitor">
          监测
        </el-button> -->
      </el-form-item>

      <el-form-item label="扫宽">
        <el-input v-model="form.bandwidth" type="number" @change="dataChangeFun('bandwidth')">
          <template #append>
            <el-select v-model="selectForm.bandwidth" @change="unitChange('bandwidth')">
              <el-option v-for="unit in units" :key="unit" :value="unit" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="起始频率">
        <el-input v-model="form.startFreq" type="number" @change="dataChangeFun('startFreq')">
          <template #append>
            <el-select v-model="selectForm.startFreq" @change="unitChange('startFreq')">
              <el-option v-for="unit in units" :key="unit" :value="unit" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="终止频率">
        <el-input v-model="form.endFreq" type="number" @change="dataChangeFun('endFreq')">
          <template #append>
            <el-select v-model="selectForm.endFreq" @change="unitChange('endFreq')">
              <el-option v-for="unit in units" :key="unit" :value="unit" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="步长">
        <el-select v-model="form.step" type="number" class="w-full">
          <el-option
            v-for="item in frequency_step"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="门限类型">
        <el-select v-model="form.thresholdType" class="w-full">
          <el-option
            v-for="item in threshold_type"
            :key="Number(item.value)"
            :label="item.label"
            :value="Number(item.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="门限">
        <el-input v-model="form.threshold" type="number">
          <template #append>
            <span class="pl-1 pr-1">dBm</span>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="信号组数">
        <el-input v-model="form.freqSectionNum" type="number">
          <template #append>
            <span class="pl-1 pr-1">个</span>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup>
  import useScanStore from '@/store/modules/scanMonitor'
  import { numToPlot, plotToNum } from '@/utils/utils'
  import { update } from '@/utils/instance'
  /**
   * 枚举值获取
   */
  const { proxy } = getCurrentInstance()
  // 字典项
  const { frequency_step, threshold_type } = proxy.useDict('frequency_step', 'threshold_type')

  const scanStore = useScanStore()
  const spectrum = reactive(scanStore.spectrumCopy)
  const form = ref(spectrum)
  const selectForm = ref({
    centerFreq: 'Hz',
    bandwidth: 'Hz',
    startFreq: 'Hz',
    endFreq: 'Hz'
  })
  const units = ['GHz', 'MHz', 'KHz', 'Hz']
  const dataChangeFun = async key => {
    if (key) {
      spectrum[key] = plotToNum(form.value[key] + selectForm.value[key])
      await nextTick()
      formatValue(key, spectrum[key])
      linkage(key)
      return
    }
    Object.keys(selectForm.value).forEach(async key => {
      formatValue(key, spectrum[key])
      linkage(key)
      await nextTick()
    })
  }

  const unitChange = async key => {
    await dataChangeFun(key)
  }

  const linkage = key => {
    const freStore = spectrum
    const startFreq = plotToNum(freStore.startFreq + selectForm.value.startFreq)
    const endFreq = plotToNum(freStore.endFreq + selectForm.value.endFreq)
    const bandwidth = plotToNum(freStore.bandwidth + selectForm.value.bandwidth)
    const centerFreq = plotToNum(freStore.centerFreq + selectForm.value.centerFreq)
    if (key === 'centerFreq' || key === 'bandwidth') {
      // 根据中心频率和带宽计算新的起始和终止频率
      let newStartFreq = centerFreq - bandwidth / 2
      let newEndFreq = centerFreq + bandwidth / 2

      // 确保起始频率不低于最小阈值
      if (newStartFreq < 20e6) {
        // 假设最小起始频率为20 MHz
        newStartFreq = 20e6
        newEndFreq = newStartFreq + bandwidth
      }

      freStore.startFreq = newStartFreq
      freStore.endFreq = newEndFreq

      formatValue('startFreq', freStore.startFreq)
      formatValue('endFreq', freStore.endFreq)
    }

    if (key === 'startFreq' || key === 'endFreq') {
      // 根据起始和终止频率计算新的带宽和中心频率
      let newBandwidth = endFreq - startFreq
      let newCenterFreq = startFreq + newBandwidth / 2
      // 确保带宽为正
      if (newBandwidth < 0) {
        newBandwidth = 0
      }
      freStore.bandwidth = newBandwidth
      freStore.centerFreq = newCenterFreq
      formatValue('centerFreq', freStore.centerFreq)
      formatValue('bandwidth', freStore.bandwidth)
    }
  }

  const formatValue = async (key, value) => {
    const newFormVal = numToPlot(value)
    // console.log(newFormVal, 'newFormVal')
    if (newFormVal.search('KHz') != -1) {
      form.value[key] = newFormVal.replace('KHz', '')
      selectForm.value[key] = 'KHz'
    } else if (newFormVal.search('MHz') != -1) {
      form.value[key] = newFormVal.replace('MHz', '')
      selectForm.value[key] = 'MHz'
    } else if (newFormVal.search('GHz') != -1) {
      form.value[key] = newFormVal.replace('GHz', '')
      selectForm.value[key] = 'GHz'
    }
    await nextTick()
    scanStore.updateSpectrumUnit('spectrumCopy', key, selectForm.value[key])
  }

  // const getXLabelFormat = (showNum, spectrum) => {
  //   let centerFreq = null
  //   let bandwidth = null
  //   centerFreq = plotToNum(spectrum.centerFreq + spectrum.centerFreqUnit)
  //   bandwidth = plotToNum(spectrum.bandwidth + spectrum.bandwidthUnit)
  //   const left = centerFreq - bandwidth / 2
  //   return function () {
  //     const val = (this.value / (showNum - 1)) * centerFreq + left
  //     return numToPlot(val)
  //   }
  // }
  // const monitor = () => {
  //   update('xAxis.labels.formatter', getXLabelFormat(spectrum.resultLen, spectrum))
  // }

  onMounted(() => {
    dataChangeFun()
  })

  defineExpose({
    selectForm,
    dataChangeFun
  })
</script>
<style scoped></style>
