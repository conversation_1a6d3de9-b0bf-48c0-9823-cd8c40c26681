<template>
  <div class="app-container">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      @selectionChange="rows => (selection = rows)"
    />
    <mtDetailInfo ref="refsMt" @actSuccess="getList()" />
    <PointDetail ref="refsPointDetail" />
  </div>
</template>
<script>
  import * as mtApi from '@/api/business/qa'
  import mtDetailInfo from './tmpInfo.vue'
  import PointDetail from '../points/detail.vue'
  import { useRoute } from 'vue-router'
  export default {
    name: 'Sctudents',
    components: { mtDetailInfo, PointDetail },
    data() {
      return {
        route: useRoute(),
        selection: [],
        tableFormConfig: {
          title: '知识点答疑',
          searchConfig: {
            labelWidth: '8em',
            formConfig: [
              // { title: '所属章节', name: 'qaNo' },
              { title: '问题名称', name: 'qa<PERSON>it<PERSON>' },
              { title: '提问人', name: 'qaRequsetName' },
              { title: '答疑人', name: 'qaAnswerName' }
            ],
            loadData: p => {
              if (this.route.query.pointId) {
                return mtApi.listQa({ pointId: this.route.query.pointId, ...p })
              } else {
                return mtApi.listQa(p)
              }
            }
          },
          actions: [
            // {
            //   label: '新增',
            //   type: 'primary',
            //   icon: 'el_Plus',
            //   permission: ['business:qa:add'],
            //   click: () => {
            //     this.$refs.refsMt.show()
            //   }
            // },
            {
              label: '导出',
              icon: 'el_upload',
              type: 'warning',
              permission: ['business:qa:export'],
              click: row =>
                this.download(
                  'business/qa/export',
                  {
                    // ...queryParams.value
                  },
                  `知识点答疑_${new Date().getTime()}.xlsx`
                )
            },
            {
              type: 'danger',
              label: '删除',
              permission: ['business:qa:remove'],
              disabled: () => !this.selection.length,
              icon: 'el_Delete',
              click: row => this.deleteAll()
            }
          ],
          tableConfig: {
            isChecked: true,
            cols: [
              {
                label: '知识点',
                prop: 'srPoints.pointName',
                renderFun: (h, { row }) => {
                  return h(
                    'a',
                    {
                      onClick: () => {
                        this.$refs.refsPointDetail.show(row.pointId, 'view')
                      }
                    },
                    row.srPoints.pointName
                  )
                }
              },
              { label: '问题', prop: 'qaTitle', minWidth: 200 },
              // {
              //   label: '问题内容',
              //   prop: 'qaContent',
              //   renderFun: (h, { row }) => {
              //     return h('span', { 'v-html': row.qaContent }, row.qaContent)
              //   },
              //   width: 300
              // },
              { label: '提问人', prop: 'qaRequsetName' },
              // { label: '解答人', prop: 'qaAnswerName' },
              // { label: '解答内容', prop: 'qaAnswerContent', width: 300 },
              { label: '状态', prop: 'status', type: 'dict', dictName: 'qa_status' },
              {
                label: '操作',
                type: 'action',
                width: '100',
                actions: [
                  {
                    title: '答疑',
                    label: '答疑',
                    // icon: 'el_Warning',
                    permission: ['business:qa:answer'],
                    click: row => this.$refs.refsMt.show(row.qaId)
                  },
                  {
                    title: '删除',
                    // icon: 'el_Delete',
                    label: '删除',
                    permission: ['business:qa:remove'],
                    click: row => this.deleteAll(row.qaId)
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList(data = null) {
        this.$refs.refsTableForm.loadData(data)
      },
      //批量删除模板
      deleteAll(id = null) {
        if (!this.selection.length && !id) return this.$modal.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选学生吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.qaId)
          mtApi.delQa(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.getList()
          })
        })
      }
    }
  }
</script>
