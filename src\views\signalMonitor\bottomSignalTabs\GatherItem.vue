<template>
  <div class="gather-item">
    <el-form class="left pt-4">
      <el-form-item label="检波器">
        <el-select v-model="form.detector">
          <el-option
            v-for="item in checkWaveItems"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文件名前缀">
        <el-input v-model="form.fileNamePrefix" />
      </el-form-item>
      <el-form-item label="记录时间">
        <el-input v-model="form.extractValue" />
      </el-form-item>
    </el-form>
    <ul class="right">
      <li v-for="item in gatherStatus" :key="item.label" class="info-list">
        <span class="label">{{ item.label }}</span>
        <span class="value">{{ item.value }}</span>
      </li>
      <li style="margin-top: 60px">
        <el-button class="ctl-btn mr-4" :class="{ close: ws }" @click="startGather">开始</el-button>
        <el-button class="ctl-btn" :class="{ close: !ws }" @click="stopGather">结束</el-button>
      </li>
    </ul>
  </div>
</template>

<script setup>
  import useScanStore from '@/store/modules/scanMonitor'
  import { linkWs } from '@/api/upperComputer/scan'
  import { IQ_GATHER_CODE } from '@/constant/funCodes'
  import { numToPlot, plotToNum } from '@/utils/utils'

  const scanStore = useScanStore()
  const { monitor } = scanStore
  const checkWaveItems = [
    { label: '平均', value: 0 },
    { label: '最大保持', value: 1 }
  ]
  const props = defineProps({
    type: {
      type: String,
      default: 'time'
    }
  })
  const ws = ref(null)
  const form = ref({
    detector: 0,
    fileNamePrefix: 'SWP_REC_',
    extractValue: 1
  })
  const gatherStatus = reactive({
    schedule: { label: '进度', value: '已保存' },
    recordNum: { label: '记录次数', value: '0次' },
    size: { label: '文件大小', value: '0B' }
  })

  const startGather = async () => {
    const data = {
      polarization: monitor.taskParams.polarization,
      centerFrequency: monitor.centerFreq,
      intermediateFrequencyBandwidth: monitor.bandwidth,
      samplingTime: form.value.extractValue * 1000,
      dataType: form.value.detector
    }
    ws.value = await linkWs({
      host: '*************',
      port: 44444,
      taskFunCode: IQ_GATHER_CODE,
      sendTime: 10,
      fileNameFirst: form.value.fileNamePrefix,
      path: 0,
      CollectNumber: 1,
      data: encodeURIComponent(JSON.stringify(data))
    })
    ws.value.onmessage = event => {
      // console.log('cccc', event.data)
      if (!event.data) {
        return
      }
      try {
        const { data } = JSON.parse(event.data)
        gatherStatus.schedule.value = '保存中'
        gatherStatus.recordNum.value = data.CollectNumber + '次'
        const sizeStr = numToPlot(data.fileLarge)
        gatherStatus.size.value = sizeStr.replace('Hz', 'B')
      } catch (err) {
        console.log(err)
      }
    }
    ws.value.onclose = event => {
      gatherStatus.schedule.value = '已保存'
      console.log('ws 连接断开')
    }
  }
  const stopGather = () => {
    ws.value && ws.value.close()
    ws.value = null
  }
</script>
