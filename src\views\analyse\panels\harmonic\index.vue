<template>
  <el-collapse v-model="activeItems" class="collapse-panels">
    <el-collapse-item
      v-for="panel in panels"
      :key="panel.value"
      :title="panel.title"
      :name="panel.value"
    >
      <component :is="dom[panel.value]" />
    </el-collapse-item>
  </el-collapse>
</template>

<script setup>

import AmplitudeSettings from '@/views/analyse/panels/harmonic/AmplitudeSettings'
import Measurements from '@/views/analyse/panels/harmonic/Measurements.vue'
import RateSettings from '@/views/analyse/panels/harmonic/RateSettings.vue'


const panels = [
  {
    title: '幅度',
    value: 'AmplitudeSettings',
  },
  {
    title: '频率',
    value: 'RateSettings',
  },
  {
    title: '测量',
    value: 'Measurements',
  }
]

const emit = defineEmits(['update:modelValue'])

const dom = shallowReactive({
  AmplitudeSettings, RateSettings, Measurements
})

const activeItems = ref(panels.map(panel => panel.value))

</script>

<style scoped lang="scss">
.panels-leave-active,
.panels-enter-active {
  transition: all 0.8s ease-in-out;
}

.panels-enter-from {
  transform: translateY(-200px);
  opacity: 0;
}
.panels-leave-to {
  transform: translateY(-200px);
  opacity: 0;
}
</style>
