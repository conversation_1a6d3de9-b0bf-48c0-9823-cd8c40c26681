import request from '@/utils/request'

// 查询知识点信息列表
export function listKnow(query) {
  return request({
    url: '/business/know/list',
    method: 'get',
    params: query
  })
}

// 查询知识点信息详细
export function getKnow(knowId) {
  return request({
    url: '/business/know/' + knowId,
    method: 'get'
  })
}

// 新增知识点信息
export function addKnow(data) {
  return request({
    url: '/business/know',
    method: 'post',
    data: data
  })
}

// 修改知识点信息
export function updateKnow(data) {
  return request({
    url: '/business/know',
    method: 'put',
    data: data
  })
}

// 删除知识点信息
export function delKnow(knowId) {
  return request({
    url: '/business/know/' + knowId,
    method: 'delete'
  })
}
