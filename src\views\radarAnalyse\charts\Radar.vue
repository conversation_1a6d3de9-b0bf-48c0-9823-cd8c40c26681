<template>
  <div style="height: 280px;">
    <div class="pt-1" style="line-height: 20px;color: #f3f3f3;text-align: center;background: #020e1f;">
      方向： {{ round(maxAngle[0], 2) }} &nbsp; 幅度：{{ round(maxAngle[1], 2) }}
    </div>
    <div :id="id" style="width: 260px;" />
  </div>
</template>

<script setup>
import Highcharts from '@/plugins/highcharts'
import { computed, onMounted } from 'vue';
import { round } from 'lodash'

let instance = null
const props = defineProps({
  data: {
    type: Array,
    default: () => new Array(50).fill(0).map((item, index) => {
      const rdm = (Math.random() * 2 - 1) / 4
      return [index/50 * 360, rdm] 
    })
  },
  id: {
    type: String,
    default: () => 'radarChart'
  }
})
const maxAngle = computed(() => {
  if (polarData.value.length === 0) {
    return [0, 0]
  }
  const max = polarData.value.reduce((prev, cur) => {
    if (prev[1] < cur[1]) {
      return cur
    }
    return prev
  })
  return max
})
const polarData = computed(() => {
  return handlePropsData(props.data)
})
const initHighChart = () => {
  const level = -100
  instance = new Highcharts.Chart(props.id, {
    chart: {
      polar: true,
      backgroundColor: '#020e1f',
      height: 260,
    },
    exporting: { enabled: false },
    title: {
      text: ''
    },
    pane: {
      startAngle: 0,
      endAngle: 360
    },
    credits: { enabled: false },
    reflow: true,
    accessibility: {
      enabled: false
    },
    xAxis: {
      tickInterval: 45,
      min: 0,
      max: 360,
      gridLineWidth: 0.5,
      lineWidth: 0.5,
      gridLineColor: '#eee',
      lineColor: '#f3f3f3',
      labels: {
        style: { color: '#aeadad' },
        formatter: function () {
          return this.value + '°';
        }
      },
    },
    yAxis: {
      min: level,
      max: level + 100,
      tickAmount: 5,
      gridLineColor: '#eee',
      lineColor: '#f3f3f3',
      lineWidth: 0.5,
      labels: {
        style: { color: '#aeadad' },
      }
    },
    boost: { useGPUTranslations: true },
    legend: {
      enabled: false
    },
    plotOptions: {
      series: {
        pointStart: 0,
        // pointInterval: 360 / props.data.length
      },
      column: {
        pointPadding: 0,
        groupPadding: 0
      }
    },
    series: [{
      type: 'area',
      name: '面积',
      stacking: 'normal',
      threshold: level,
      marker: { enabled: false },
      data: polarData.value,
      enableMouseTracking: false,
      lineWidth: 0.5,
      fillColor: 'rgba(7,59,92,.6)',
      color: '#f3f3f3'
    },
    {
      type: 'line',
      marker: { enabled: false },
      data: [[0, level], [maxAngle.value[0], 2]],
      enableMouseTracking: false,
      color: '#ff0000'
    }
  ]
  })
}
const handlePropsData = (data) => {
  const target = [], stack = []
  for (let i = 0; i < data.length; i++) {
    const item = data[i]
    if (stack.includes(item[0])) {
      continue
    }
    target.push(item)
    stack.push(item[0])
  }
  return target
}

onMounted(() => {
  initHighChart()
})
</script>