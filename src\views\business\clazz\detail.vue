<template>
  <el-dialog
    v-model="dia"
    v-loading="loading"
    width="800px"
    append-to-body
    :title="id ? '编辑期班信息' : '新增期班'"
  >
    <c-form
      :key="form.calId + '' + dia"
      v-bind="formConfig"
      ref="refsForm"
      v-model="form"
      v-loading="loading"
      class="mr-4"
    />
    <div class="ftitle">期班课程</div>
    <div class="mb-3">
      <el-button
        type="primary"
        :disabled="srCalzzCourseList.length >= 20"
        size="small"
        icon="Plus"
        @click="handleAddSrCalzzCourse"
      >
        添加
      </el-button>
    </div>
    <el-table :data="srCalzzCourseList" border stripe>
      <el-table-column label="课程" prop="courseId">
        <template #default="scope">
          <cu-select
            v-model="scope.row.courseId"
            query-info="business/course/list?pageNum=1&pageSize=100"
            :replace-fields="{ label: 'courName', value: 'courId' }"
            @change="
              ({ value }) => {
                scope.row.teaId = ''
              }
            "
          />
        </template>
      </el-table-column>
      <el-table-column label="任课老师" prop="teaId">
        <template #default="scope">
          <cu-select
            :key="scope.row.courseId"
            v-model="scope.row.teaId"
            placeholder="请选择任课老师"
            :disabled="!scope.row.courseId"
            :query-info="`business/course/getCourseTeacher/${scope.row.courseId}`"
            :replace-fields="{ label: 'teaName', value: 'teaId' }"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80">
        <template #default="{ row }">
          <el-link icon="Delete" @click="handleDeleteSrCalzzCourse(row.courseId)">删除</el-link>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button type="primary" @click="sub">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
  import { addClazz, updateClazz, getClazz } from '@/api/business/clazz'
  export default {
    emits: ['actSuccess', 'close'],
    data() {
      return {
        srCalzzCourseList: [],
        dia: false,
        loading: false,
        id: '',
        form: {},
        formConfig: {
          splice: 2,
          labelWidth: '7em',
          hideBtn: true,
          autoEmit: false,
          layout: 'block',
          formInfo: [
            {
              title: '期班编码',
              name: 'claCode',
              rules: { message: '请输入期班编码', required: true }
            },
            {
              title: '期班名称',
              name: 'claName',
              rules: { message: '请输入期班名称', required: true }
            },
            {
              title: '所属院系',
              name: 'claFaculty',
              required: true,
              type: 'dict',
              dictName: 'faculty',
              rules: { message: '请选择所属院系', required: true }
            },
            {
              title: '所属专业',
              name: 'claMajor',
              type: 'dict',
              dictName: 'stu_major',
              rules: { message: '请选择所属专业', required: true }
            },
            // {
            //   title: '所属年级',
            //   name: 'claGrade',
            //   required: true,
            //   type: 'dict',
            //   dictName: 'grade',
            //   rules: { message: '请选择所属年级', required: true }
            // },
            {
              title: '期班负责人',
              name: 'claMaster',
              type: 'cuselect',
              queryInfo: 'business/teacher/list?pageNum=1&pageSize=100',
              replaceFields: { label: 'teaName', value: 'teaId' },
              rules: { message: '请选择班主任', required: true }
            },
            {
              title: '指导老师',
              name: 'claInstructor',
              type: 'cuselect',
              queryInfo: 'business/teacher/list?pageNum=1&pageSize=100',
              replaceFields: { label: 'teaName', value: 'teaId' },
              rules: { message: '请选择指导老师', required: true }
            },
            // { title: '备注', name: 'stuHomeaddr', type: 'textarea', maxLenght: 200, isFull: true }
          ]
        }
      }
    },

    methods: {
      show(id = '') {
        this.id = id
        this.dia = true
        this.form = {}
        this.srCalzzCourseList = []
        id && this.getInfo(id)
      },
      getInfo(id) {
        this.loading = true
        getClazz(id)
          .then(res => {
            this.form = res.data
            this.srCalzzCourseList = res.data.srCalzzCourseList
          })
          .finally(() => {
            this.loading = false
          })
      },

      /** 期班课程关系添加按钮操作 */
      handleAddSrCalzzCourse() {
        let obj = {}
        obj.courseId = ''
        obj.teaId = ''
        obj.calId = ''
        this.srCalzzCourseList.push(obj)
      },
      /** 期班课程关系删除按钮操作 */
      handleDeleteSrCalzzCourse(courseId) {
        this.srCalzzCourseList = this.srCalzzCourseList.filter(v => {
          return v.courseId != courseId
        })
      },
      checkoutCourTeac(arr) {
        let noTeac = arr.filter(v => !v.teaId)
        if (!!noTeac.length) {
          this.$modal.msgWarning('请选择课程对应的老师')
          return false
        }
        let cmap = new Set()
        arr.map(v => cmap.add(v.courseId))
        if (Array.from(cmap).length != arr.length) {
          this.$modal.msgWarning('不可选择重复的课程')
          return false
        }
        return true
      },
      sub() {
        this.$refs.refsForm.subForm(form => {
          let checkC = this.checkoutCourTeac(this.srCalzzCourseList)
          if (!checkC) return false
          form.srCalzzCourseList = this.srCalzzCourseList
          let act = form.calId ? updateClazz : addClazz
          act(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
        })
      }
    }
  }
</script>
<style></style>
