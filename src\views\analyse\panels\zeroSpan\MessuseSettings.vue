<template>
  <data-form :fields="fields" :model="model" />
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import { timeUnits } from '@/constant/units'
  import ModelData from '@/common/classes/modelData';

  const fields = ref([
    {
      target: 'isAutoInterval',
      name: '自动间隔',
      isCheckbox: true,
    },
    {
      target: 'intervalOffset',
      name: '间隔偏移',
      options: timeUnits,
      appendSelect: true,
    },
    {
      target: 'intervalLen',
      name: '间隔长度',
      options: timeUnits,
      appendSelect: true,
    }
  ])

  const model = reactive(new ModelData(fields.value, 'zp'))
</script>
