<template>
  <el-form>
    <el-form-item label="测量模式">
      <el-select v-model="form.measureMode" type="number">
        <el-option
          v-for="item in measureModes"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="极化方式">
      <el-select v-model="form.polarization" type="number">
        <el-option
          v-for="item in polarTypes"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="检波方式">
      <el-select v-model="form.detectionMode" type="number">
        <el-option
          v-for="item in detectionTypes"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="检波速度">
      <el-input v-model="form.detectionSpeed" type="number" />
    </el-form-item>
    <el-form-item label="衰减模式">
      <el-select v-model="form.attenuationMode" type="number">
        <el-option
          v-for="item in attenuationModes"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="衰减值">
      <el-input v-model="form.attenuationValue" type="number">
        <template #append>
          <span class="pl-1 pr-1">dB</span>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label="增益模式">
      <el-select v-model="form.gainMode" type="number">
        <el-option
          v-for="item in gainModes"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="增益值">
      <el-input v-model="form.gainValue" type="number">
        <template #append>
          <span class="pl-1 pr-1">dbm</span>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label="驻留时间">
      <el-input v-model="form.stayTime" type="number">
        <template #append>
          <span class="pl-1 pr-1">ms</span>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label="门限类型">
      <el-select v-model="form.thresholdType">
        <el-option
          v-for="item in thresholdTypes"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="门限">
      <el-input v-model="form.threshold" type="number">
        <template #append>
          <span class="pl-1 pr-1">dBμV/m</span>
        </template>
      </el-input>
    </el-form-item>
    <slot name="formItem" :form="form" />
  </el-form>
</template>
<script setup>
  import {
    measureModes,
    polarTypes,
    detectionTypes,
    attenuationModes,
    thresholdTypes,
    gainModes
  } from '@/constant/types'

  const props = defineProps({
    model: {
      type: Object,
      default: () => ({})
    }
  })

  const initParams = (params = {}) => {
    if (props.model.taskParams) {
      props.model.taskParams = params
      return props.model.taskParams
    }
    return ref(params)
  }
  const form = initParams({
    measureMode: 0, // 测量模式
    polarization: 0, // 极化方式
    detectionMode: 0, // 检波方式
    detectionSpeed: 0, // 检波速度
    attenuationMode: 0, // 衰减模式
    attenuationValue: 0, // 衰减值
    gainMode: 0, // 增益模式
    gainValue: 0, // 增益值
    stayTime: 1000,
    thresholdType: 0,
    threshold: -50
  })

  defineExpose({
    form
  })
</script>
<style scoped>
  .el-select {
    width: 100%;
  }
</style>
