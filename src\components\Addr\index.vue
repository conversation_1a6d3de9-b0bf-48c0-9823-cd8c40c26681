<template>
  <span v-if="type == 'text'">{{ addr_text }}<slot /></span>
  <EluiChinaAreaDht v-else :model-value="modelValueArr" :leave="3" @change="changeAddr" />
</template>
<script>
  import { EluiChinaAreaDht } from 'elui-china-area-dht'
  const chinaData = new EluiChinaAreaDht.ChinaArea().chinaAreaflat
  export default {
    name: 'Addr',
    components: { EluiChinaAreaDht },
    props: ['modelValue', 'type', 'addrlist'],
    emits: ['update:modelValue', 'change'],
    data() {
      return {}
    },
    computed: {
      addr_text() {
        if (!this.modelValue.length) return ''
        let [m1, m2, m3] = this.modelValue || []
        return [chinaData[m1]?.label, chinaData[m2]?.label, chinaData[m3]?.label].join('-')
      },
      modelValueArr() {
        if (!this.modelValue) return []
        let str = this.modelValue
        let str1 = str.slice(0, 6)
        let str2 = str.slice(7, 13)
        let str3 = str.slice(14, 20)
        console.log('str1', str1, 'str2', str2, 'str3', str3)
        return [str1, str2, str3]
      }
    },
    mounted() {},
    methods: {
      changeAddr(addr) {
        let obj = {}
        this.addrlist?.length &&
          this.addrlist.map((v, i) => {
            obj[v] = addr[i]
          })
        let str = addr[0] + '/' + addr[1] + '/' + addr[2] + '/'
        this.$emit('update:modelValue', str)
        this.$emit('change', obj)
      }
    }
  }
</script>
<style lang="less"></style>
