<template>
  <div v-if="model" class="time-container">
    <data-chart
      :id="IQ_VS_TIME"
      :data="model.data"
      title="I/Q vs Time"
      :config="model.config"
    />
  </div>
</template>

<script setup>
  // FM vs Time 图表
  import { queryIqs } from '@/api/charts'
  import DataChart from '@/components/DataChart'
  import useChartsStore from '@/store/modules/charts'
  import useChartOptions from '@/common/hooks/chartOptions'
  import { cloneDeep } from 'lodash'
  import { IQ_VS_TIME } from '@/constant/chartConsts'

  const chartsStore = useChartsStore()
  const model = ref(null)
  const genearteChartsOptions = (chartOptions, data) => {
    const options = cloneDeep(chartOptions)
    options.yAxis.min = undefined
    options.yAxis.max = undefined
    options.yAxis.unit = 'mV'
    const colorMap = ['#00ff66', 'red']
    options.series = colorMap.map((color, index) => {
      return {
        color: color,
        marker: {
          enabled: false
        },
        animation: false,
        enableMouseTracking: false,
        type: 'line',
        data: data[index],
        lineWidth: 0.5
      }
    })
    return options
  }
  const viewNum = computed(() => chartsStore.static.enabled ? 'all' : chartsStore.viewNum)
  const init = async () => {
    const body = chartsStore.getFileInfo()
    const { data } = await queryIqs(body)
    const xMax = chartsStore.static.enabled ? data[1].length - 1 : chartsStore.viewNum - 1
    const { chartOptions } = useChartOptions('sequential', xMax)
    generateModel([data[1], data[2]], chartOptions.value)
  }
  const generateModel = (data, chartOptions) => {
    model.value = {
      data,
      config: {
        type: 'mutiTrend',
        chart: computed(() => genearteChartsOptions(chartOptions, data)),
        viewNum
      }
    }
  }

  onMounted(() => {
    init()
  })
</script>
