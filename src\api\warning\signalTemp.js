import request from '@/utils/request'

// 查询信号模板列表
export function getSignalTempList(data) {
  return request({
    url: '/alert/signalTemp/list',
    method: 'post',
    data: data
  })
}


// 获取信号模板详情
export function getSignalTempDetail(signalTempId) {
  return request({
    url: '/alert/signalTemp/detial?id=' + signalTempId,
    method: 'get',
    contentType:'application/x-www-form-urlencoded',
  })
}

// 新增/更新 信号模板
export function addOrUpdateSignalTemp(data) {
  return request({
    url: '/alert/signalTemp/saveOrUpdate',
    method: 'post',
    data: data
  })
}


// 删除信号模板
export function delSignalTemp(data) {
  return request({
    url: '/alert/signalTemp/del',
    method: 'post',
    data
  })
}

// 批量删除信号模板
export function batchDelSignalTemp(data) {
  return request({
    url: '/alert/signalTemp/batchDel',
    method: 'post',
    data
  })
}