import request from '@/utils/request'

// 查询答疑互动记录列表
export function listAnswer(query) {
  return request({
    url: '/business/answer/list',
    method: 'get',
    params: query
  })
}

// 查询答疑互动记录详细
export function getAnswer(answerId) {
  return request({
    url: '/business/answer/' + answerId,
    method: 'get'
  })
}

// 新增答疑互动记录
export function addAnswer(data) {
  return request({
    url: '/business/answer',
    method: 'post',
    data: data
  })
}

// 修改答疑互动记录
export function updateAnswer(data) {
  return request({
    url: '/business/answer',
    method: 'put',
    data: data
  })
}

// 删除答疑互动记录
export function delAnswer(answerId) {
  return request({
    url: '/business/answer/' + answerId,
    method: 'delete'
  })
}
