<template>
  <el-dialog v-model="dia" width="800px" append-to-body :title="id ? '问题详情' : '新增'">
    <c-form
      v-bind="formConfig"
      :key="form.qaId"
      ref="refsForm"
      v-model="form"
      v-loading="loading"
      class="mr-4"
    >
      <template #point="{ form }">
        <a @click="$refs.refsPointDetail.show(form.pointId, 'view')">{{
          form.srPoints?.pointName
        }}</a>
      </template>
      <template #history="{ form }">
        <a @click="openHistory(form)">查看详情</a>
      </template>
    </c-form>
    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button type="primary" @click="sub">保存</el-button>
    </template>
    <PointDetail ref="refsPointDetail" />
    <history ref="refsHistory" />
  </el-dialog>
</template>
<script>
  import { getQa, addQa, updateSrQaByTeacher } from '@/api/business/qa'
  import PointDetail from '../points/detail.vue'
  import history from './detail.vue'
  export default {
    components: { PointDetail ,history},
    emits: ['actSuccess', 'close'],
    data () {
      return {
        dia: false,
        loading: false,
        id: '',
        form: {},
        formConfig: {
          splice: 2,
          labelWidth: '8em',
          layout: 'block',
          autoEmit: false,
          hideBtn: true,
          formInfo: [
            { title: '知识点', type: 'slot', slotName: 'point' },
            // { title: '问题', name: 'qaTitle',isFull: true, readonly: true },
            {
              isFull: true,
              title: '问题',
              name: 'qaTitle',
              type: 'editor',
              readonly: true
            },
            {
              isFull: true,
              title: '解答状态',
              name: 'status',
              type: 'dict',
              readonly: true,
              dictName: 'qa_status'
            },
            { title: '历史解答', type: 'slot', slotName: 'history' },
            { isFull: true, title: '解答内容', name: 'qaAnswerContent', type: 'textarea' ,  rules: { message: '请输入解答内容', required: true }},
            // { isFull: true, title: '备注', name: 'remark', type: 'textarea' }
          ]
        }
      }
    },

    methods: {
      show (id) {
        this.id = id
        this.dia = true
        this.form = {}
        id && this.getInfo(id)
      },
      getInfo (id) {
        this.loading = true
        getQa(id)
          .then(res => {
            this.form = res.data
            this.form.qaAnswerContent = ''
          })
          .finally(() => {
            this.loading = false
          })
      },
      openHistory(rows){
        console.log('rows',rows)
        if(rows.srQaAnswerList.length){
          this.$refs.refsHistory.show(rows.srQaAnswerList)
        }else{
          
          this.$message.warning('暂无历史答疑')
          // this.$modal.msgSuccess('暂无历史答疑')
        }
      },
      sub () {
        this.$refs.refsForm.subForm(form => {
          let act = this.form.qaId ? updateSrQaByTeacher : addQa
          if (form.qaAnswerContent == '<p><br></p>') {
            form.status = '1'
          } else {
            form.status = '2'
          }
          if(act == updateSrQaByTeacher){
            act({anContent:form.qaAnswerContent,qaId:form.qaId}).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
          }else{
            act(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
          }
        
        })
      }
    }
  }
</script>
<style></style>
