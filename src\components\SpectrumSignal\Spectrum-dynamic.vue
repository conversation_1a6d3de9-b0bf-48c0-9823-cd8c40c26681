<!-- 信号监测HIghCharts -->
<template>
  <div class="spectrum-container">
    <div :id="usekey" />
    <div id="marker">
      <svg version="1.1" width="24" height="322">
        <path
          fill="#aeadad"
          d="M 0 322 L 24 322  L 12 298 Z M 12 298 L 12 0"
          stroke="#aeadad"
          stroke-width="1"
        />
        <text x="7" y="319" fill="rgba(1, 23, 13, 1)">1</text>
      </svg>
    </div>
    <div v-if="markers.length > 0" class="markers-info">
      <div class="markers-title">
        <span style="width: 36px">Mark</span>
        <span style="width: 75px">频率({{ speUnit }})</span>
        <span>幅度(dbuV/m)</span>
      </div>
      <div v-for="item in markers" :key="item.index" class="markers-list">
        <span style="width: 36px">{{ item.index }}</span>
        <span>{{ indexToPlot(item.point.x, model) }}</span>
        <span>{{ round(item.point.y, 3) }}</span>
      </div>
    </div>
    <div v-if="instance && instance.activeMarker" class="marker-values">
      <span v-for="item in currentValues" :key="item.label" style="white-space: nowrap">{{
        item.label + ':' + item.value
      }}</span>
    </div>
  </div>
</template>

<script setup>
  import Highcharts from '@/plugins/highcharts'
  import SpectrumMarker from '@/common/classes/spectrumMark'
  import TopMarker from '@/common/classes/topMarker'
  import { cloneDeep, round } from 'lodash'
  import { numToPlot, indexToPlot } from '@/utils/utils'
  import { chartConfig } from './chartConfig'
  import { setInstance } from '@/utils/instance'

  const props = defineProps({
    showLines: {
      type: Array,
      default: () => []
    },
    selectedFremarkers: {
      type: Array,
      default: () => []
    },
    isActive: {
      type: Boolean,
      default: true
    },
    isTopFollow: {
      type: Boolean,
      default: true
    },
    model: {
      type: Object,
      default: () => ({})
    },
    dataList: {
      type: Object,
      default: () => ({})
    },
    usekey: {
      type: String,
      default: 'default'
    }
  })
  const { dataList } = toRefs(props)
  const instance = ref(null)
  const speUnit = computed(() => {
    const { centerFreq } = props.model
    if (centerFreq < 1000) {
      return numToPlot(centerFreq).slice(-2)
    }
    return numToPlot(centerFreq).slice(-3)
  })
  const currentValues = computed(() => {
    const x = instance.value.activeMarker.point.x
    return [
      { label: '频率', value: indexToPlot(x, props.model) },
      { label: '幅度值', value: getRoundValue(dataList.value.current[x], 3) },
      { label: '门限值', value: getRoundValue(dataList.value.limit[0], 3) },
      { label: '最大值', value: getRoundValue(dataList.value.max[x], 3) },
      { label: '平均值', value: getRoundValue(dataList.value.average[x], 3) },
      { label: '最小值', value: getRoundValue(dataList.value.min[x], 3) }
    ]
  })
  const markers = computed(() => {
    if (!props.isActive || !instance.value) {
      return []
    }
    const chartMarks = instance.value.specturmMarkers || []
    const target = []
    props.selectedFremarkers.forEach(item => {
      let marker = chartMarks.find(mk => mk.index === item)
      if (marker) {
        marker.show()
      } else {
        marker = new SpectrumMarker(
          instance.value,
          item,
          document.getElementById('marker'),
          props.model
        )
      }
      target.push(marker)
    })
    props.isTopFollow && target.push(instance.value.topMarker)
    return target
  })
  const initHighChart = () => {
    instance.value = new Highcharts.Chart(props.usekey, chartConfig)
    setInstance(markRaw(instance.value))
  }
  const getRoundValue = (value, slot) => {
    const num = round(value, slot)
    if (isNaN(num)) {
      return '--'
    }
    return num
  }
  const getXlabelFormat = (showNum, spectrum) => {
    const left = spectrum.centerFreq - spectrum.bandwidth / 2
    return function () {
      const val = (this.value / (showNum - 1)) * spectrum.bandwidth + left
      return numToPlot(val, '', 2)
    }
  }
  const getXAisConfig = (showNum, spectrum) => {
    const axis = cloneDeep(chartConfig.xAxis)
    axis.max = showNum - 1
    axis.min = 0
    axis.tickAmount = 11
    axis.tickInterval = (showNum - 1) / 10
    axis.labels.formatter = getXlabelFormat(showNum, spectrum)
    return axis
  }
  const getDispersedXConfig = spectrum => {
    const axis = cloneDeep(chartConfig.xAxis)
    const [min, max] = spectrum.discrete.reduce(
      (prev, item) => {
        const startFre = item.frequency - item.analysisBandwidth / 2
        const endFre = item.frequency + item.analysisBandwidth / 2
        if (startFre < prev[0]) {
          prev[0] = startFre
        }
        if (endFre > prev[1]) {
          prev[1] = endFre
        }
        return prev
      },
      [Infinity, -Infinity]
    )
    axis.min = min === Infinity ? spectrum.startFre : min
    axis.max = max === -Infinity ? spectrum.endFre : max
    axis.labels.formatter = function () {
      return numToPlot(this.value)
    }
    return axis
  }
  const updateChartConfig = config => {
    try {
      instance.value.update(config)
    } catch (err) {
      console.log(err)
    }
  }
  const updateViewLines = val => {
    instance.value.series.forEach(item => {
      if (val.includes(item.name)) {
        item.setVisible(true)
      } else {
        item.setVisible(false)
      }
    })
  }
  const updateSeriesData = () => {
    if (!instance.value?.series) {
      return
    }
    instance.value.series.forEach((s, i) => {
      s.setData(dataList.value[s.options.id], false)
    })
    instance.value.redraw()
  }

  defineExpose({
    instance
  })

  watch(
    () => props.showLines,
    val => updateViewLines(val)
  )
  watch(
    () => props.isTopFollow,
    val => {
      if (!instance.value.topMarker) {
        new TopMarker(instance.value)
      }
      instance.value.topMarker.setVisible(val)
    }
  )
  watch(
    () => dataList.value.current,
    (val, oldVal) => {
      if (val.length !== oldVal.length) {
        const xAxisConfig =
          props.model.type && props.model.discrete
            ? getDispersedXConfig(props.model)
            : getXAisConfig(val.length, props.model)
        updateChartConfig({ xAxis: xAxisConfig })
        props.model.resultLen = val.length
      }
      updateSeriesData()
    }
  )
  watch(
    () => props.model.refLevel,
    val => {
      const yAxis = cloneDeep(chartConfig.yAxis)
      yAxis.max = val * 1
      yAxis.min = val - 100
      updateChartConfig({ yAxis })
    }
  )

  onMounted(() => {
    initHighChart()
  })
  onActivated(async () => {
    instance.value?.redraw()
  })
  onDeactivated(() => {})
</script>

<style scoped lang="scss">
  .spectrum-container {
    position: relative;
    height: 400px;
  }

  #spectrum {
    height: 100%;
  }

  #marker {
    position: absolute;
    bottom: 48px;
    cursor: pointer;
    display: none;
    opacity: 0.6;
  }

  .markers-info {
    font-size: 12px;
    color: var(--scan-text-color);
    position: absolute;
    right: 0;
    top: 12px;

    span {
      padding: 0 6px;
      display: inline-block;
    }
  }

  .marker-values {
    font-size: 12px;
    color: var(--scan-text-color);
    position: absolute;
    left: 60px;
    top: 12px;
    width: 300px;

    span {
      padding: 0 6px;
      display: inline-block;

      &:nth-child(4),
      &:nth-child(1) {
        width: 106px;
      }
    }
  }
</style>
