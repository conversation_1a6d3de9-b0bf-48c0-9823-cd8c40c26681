<template>
  <div v-if="!item.hidden">
    <template
      v-if="!item.alwaysShow"
    >
      <app-link
        v-if="onlyOneChild.meta"
        :to="resolvePath(onlyOneChild.path, onlyOneChild.query)"
      >
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          class="uc-sidebar-item"
        >
          <svg-icon
            :icon-class="
              onlyOneChild.meta.icon || (item.meta && item.meta.icon)
            "
            style="font-size: 24px;"
          />
          <template #title>
            <span
              class="menu-title"
              :title="hasTitle(onlyOneChild.meta.title)"
            >{{ onlyOneChild.meta.title }}</span>
          </template>
        </el-menu-item>
      </app-link>
    </template>

    <el-menu-item
      v-else
      :index="resolvePath(onlyOneChild.path)"
      class="uc-sidebar-item"
    >
      <el-popover placement="right" :width="200" trigger="click">
        <template #reference>
          <div style="display: flex; flex-direction: column;align-items: center;">
            <svg-icon
              :icon-class="
                onlyOneChild.meta.icon || (item.meta && item.meta.icon)
              "
              style="font-size: 24px;"
            />
            <span
              class="menu-title"
              :title="hasTitle(onlyOneChild.meta.title)"
            >{{ onlyOneChild.meta.title }}</span>
          </div>
        </template>
        <div style="width: 200px;height: 600px;overflow: auto;">
          <sidebar-item
            v-for="child in item.children"
            :key="child.path"
            :is-nest="true"
            :item="child"
            :base-path="basePath"
            class="nest-menu"
          />
        </div>
      </el-popover>
    </el-menu-item>
  </div>
</template>

<script setup>
import { isExternal } from "@/utils/validate";
import AppLink from "./Link";
import { getNormalPath } from "@/utils/utils";
import { computed } from "vue";

const props = defineProps({
  // route object
  item: {
    type: Object,
    required: true,
  },
  isNest: {
    type: Boolean,
    default: false,
  },
  basePath: {
    type: String,
    default: "",
  },
});

const onlyOneChild = computed(() => {
  const children = props.item.children
  if (!children){
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    // props.item.path = ''
  } else if (props.item.children.length === 1) {
    return props.item.children[0]
  }
  return props.item
});

function resolvePath(routePath, routeQuery) {
  if (isExternal(routePath)) {
    return routePath;
  }
  if (isExternal(props.basePath)) {
    return props.basePath;
  }
  if (routeQuery) {
    let query = JSON.parse(routeQuery);
    return {
      path: getNormalPath(props.basePath + "/" + routePath),
      query: query,
    };
  }
  return getNormalPath(props.basePath + "/" + routePath);
}

function hasTitle(title) {
  if (title.length > 5) {
    return title;
  } else {
    return "";
  }
}
</script>

<style lang="scss">
.uc-sidebar-item {
  width: 120px;
  height: 120px!important;
  border: 1px solid #00BFBF;
  color: #fff !important;
  margin: 30px auto;
  border-radius: 15px;
  background: linear-gradient(134deg, rgba(1, 84, 120, 1) 26%, rgba(0, 0, 0, 1) 78%);
  opacity: 0.6;
  flex-direction: column;
  justify-content: center;
  padding-left: 20px !important;
  &.is-active {
    color: #fff !important;
    opacity: 1;
  }
}
</style>