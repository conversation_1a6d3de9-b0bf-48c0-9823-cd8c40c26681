<template>
  <el-dialog v-model="dia" width="800px" append-to-body :title="id ? '问题详情' : '新增'">
    <c-form
      v-bind="formConfig"
      :key="form.qaId"
      ref="refsForm"
      v-model="form"
      v-loading="loading"
      class="mr-4"
    >
      <template #point="{ form }">
        <a @click="$refs.refsPointDetail.show(form.pointId, 'view')">{{
          form.srPoints?.pointName
        }}</a>
      </template>
    </c-form>
    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button type="primary" @click="sub">保存</el-button>
    </template>
    <PointDetail ref="refsPointDetail" />
  </el-dialog>
</template>
<script>
  import { getQa, addQa, updateSrQaByTeacher } from '@/api/business/qa'
  import PointDetail from '../points/detail.vue'
  export default {
    components: { PointDetail },
    emits: ['actSuccess', 'close'],
    data () {
      return {
        dia: false,
        loading: false,
        id: '',
        form: {},
        formConfig: {
          splice: 2,
          labelWidth: '8em',
          layout: 'block',
          autoEmit: false,
          hideBtn: true,
          formInfo: [
            { title: '知识点', type: 'slot', slotName: 'point' },
            { title: '标题', name: 'qaTitle', readonly: true },

            {
              isFull: true,
              title: '问题内容',
              name: 'qaContent',
              type: 'editor',
              readonly: true
            },
            {
              isFull: true,
              title: '解答状态',
              name: 'status',
              type: 'dict',
              dictName: 'qa_status'
            },
            { isFull: true, title: '解答内容', name: 'qaAnswerContent', type: 'editor' },
            { isFull: true, title: '备注', name: 'remark', type: 'textarea' }
          ]
        }
      }
    },

    methods: {
      show (id) {
        this.id = id
        this.dia = true
        this.form = {}
        id && this.getInfo(id)
      },
      getInfo (id) {
        this.loading = true
        getQa(id)
          .then(res => {
            this.form = res.data
          })
          .finally(() => {
            this.loading = false
          })
      },
      sub () {
        this.$refs.refsForm.subForm(form => {
          let act = this.form.qaId ? updateSrQaByTeacher : addQa
          act(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
        })
      }
    }
  }
</script>
<style></style>
