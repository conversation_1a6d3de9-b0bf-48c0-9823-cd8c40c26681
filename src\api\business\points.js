import request from '@/utils/request'

// 查询知识点信息列表
export function listPoints (query) {
  return request({
    url: '/business/points/list',
    method: 'get',
    params: query
  })
}

// 查询知识点信息详细
export function getPoints (pointId) {
  return request({
    url: '/business/points/' + pointId,
    method: 'get'
  })
}

// 新增知识点信息
export function addPoints (data) {
  return request({
    url: '/business/points',
    method: 'post',
    data: data
  })
}

// 修改知识点信息
export function updatePoints (data) {
  return request({
    url: '/business/points',
    method: 'put',
    data: data
  })
}

// 删除知识点信息
export function delPoints (pointId) {
  return request({
    url: '/business/points/' + pointId,
    method: 'delete'
  })
}

// 课程章节树
export function knowTreeData () {
  return request({
    url: '/business/points/knowTreeData'
  })
}
