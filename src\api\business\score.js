import request from '@/utils/request'

// 查询学生成绩信息列表
export function listScore(query) {
  return request({
    url: '/business/score/list',
    method: 'get',
    params: query
  })
}

// 查询学生成绩信息详细
export function getScore(scoId) {
  return request({
    url: '/business/score/' + scoId,
    method: 'get'
  })
}

// 新增学生成绩信息
export function addScore(data) {
  return request({
    url: '/business/score',
    method: 'post',
    data: data
  })
}

// 修改学生成绩信息
export function updateScore(data) {
  return request({
    url: '/business/score',
    method: 'put',
    data: data
  })
}

// 删除学生成绩信息
export function delScore(scoId) {
  return request({
    url: '/business/score/' + scoId,
    method: 'delete'
  })
}
