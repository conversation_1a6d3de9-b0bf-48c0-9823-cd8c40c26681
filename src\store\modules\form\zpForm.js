import { defineStore } from 'pinia'
import useChartsStore from '../charts'

const { global } = useChartsStore()
export default defineStore('zpFormStore', {
  state: () => {
    return {
      step: 10, // 频率步进
      autoIfBw: true,
      triggerType: 1,
      triggerSide: 1,
      triggerLevel: 0,
      triggerPosition: 10,
      isAutoInterval: true,
      intervalOffset: 0,
      intervalLen: 1000,
      rbw: 10,
      autOverlap: true,
      overlap: 99,
      maxFft: 1000,
      fftStep: 3.775
    }
  },
  getters: {
    centerFreqIn() {
      return global.centerFreqIn
    },
    debugMode() {
      return global.debugMode
    },
    samplingRate() {
      // 采样率
      return global.samplingRate
    },
    bitRate() {
      // 码速率
      return global.bitRate
    },
    intermediateFrequencyBandwidth() {
      // 中频带宽
      return global.intermediateFrequencyBandwidth
    },
    refLevel() {
      return global.refLevel
    },
    swpTime() {
      // 扫描时间
      return global.swpTime
    }
  }
})
