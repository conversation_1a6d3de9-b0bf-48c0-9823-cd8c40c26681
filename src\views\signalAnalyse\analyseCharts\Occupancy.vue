<template>
  <div id="occupancy" />
</template>

<script setup>
  import { chartConfig } from '@/components/SpectrumSignal/chartConfig'
  import Highcharts from '@/plugins/highcharts'
  import useScanStore from '@/store/modules/scanMonitor'
  import { numToPlot, indexToPlot } from '@/utils/utils'
  import { cloneDeep } from 'lodash'

  let instance = null
  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    },
    model: {
      type: Object,
      default: () => ({})
    }
  })
  const scanStore = useScanStore()
  const occupancyConfig = cloneDeep(chartConfig)
  occupancyConfig.yAxis.max = 100
  occupancyConfig.yAxis.min = 0
  occupancyConfig.yAxis.title.text = '占用度(%)'
  occupancyConfig.chart.height = 300
  occupancyConfig.series = [
    {
      color: 'green',
      marker: { enabled: false },
      animation: false,
      enableMouseTracking: false,
      type: 'line',
      data: [],
      lineWidth: 0.5,
      gapSize: 1
    }
  ]

  const getXlabelFormat = (showNum, spectrum) => {
    const left = spectrum.centerFreq - spectrum.bandwidth / 2
    return function () {
      const val = (this.value / (showNum - 1)) * spectrum.bandwidth + left
      return numToPlot(val)
    }
  }
  const getXAisConfig = (showNum, spectrum) => {
    const axis = cloneDeep(chartConfig.xAxis)
    axis.max = showNum - 1
    axis.min = 0
    axis.tickAmount = 11
    axis.tickInterval = (showNum - 1) / 10
    axis.labels.formatter = getXlabelFormat(showNum, spectrum)
    return axis
  }
  const getDispersedXConfig = spectrum => {
    const axis = cloneDeep(chartConfig.xAxis)
    const [min, max] = spectrum.discrete.reduce(
      (prev, item) => {
        const startFre = item.frequency - item.analysisBandwidth / 2
        const endFre = item.frequency + item.analysisBandwidth / 2
        if (startFre < prev[0]) {
          prev[0] = startFre
        }
        if (endFre > prev[1]) {
          prev[1] = endFre
        }
        return prev
      },
      [Infinity, -Infinity]
    )
    axis.min = min === Infinity ? spectrum.startFre : min
    axis.max = max === -Infinity ? spectrum.endFre : max
    axis.labels.formatter = function () {
      return numToPlot(this.value)
    }
    return axis
  }

  watch(
    () => props.data,
    (value, oldVal) => {
      if (!instance) {
        return
      }
      if (value.length !== oldVal.length) {
        const xAxisConfig =
          props.model.type && props.model.discrete
            ? getDispersedXConfig(props.model)
            : getXAisConfig(value.length, props.model)
        instance.update({ xAxis: xAxisConfig })
      }
      instance.series[0].setData(value)
    }
  )

  onMounted(() => {
    instance = new Highcharts.Chart('occupancy', occupancyConfig)
  })
</script>
