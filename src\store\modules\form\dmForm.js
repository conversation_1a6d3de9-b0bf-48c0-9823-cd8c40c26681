import { defineStore } from 'pinia'
import useChartsStore from '../charts'
import { FILE_TYPE_BITS } from '@/constant/file'

const { global } = useChartsStore()
export default defineStore('dmFormStore', {
  state: () => {
    return {
      // 调制分析
      resultLen: 32,
      freqStep: 1000, // 频率步进
      inputPower: 100, // 输入功率
      filter: 1, // 滤波器
      filterAlpha: 0.35, // 滤波器滚降系数
      autoIfBw: true, // 自动中频带宽
      iqOvertrun: false, // IQ翻转
      corvage: false,
      corvageNum: 10,
      ptsSym: 16,
      iqOffset: true,
      amFading: false,
      // 触发设置
      triggerType: 1,
      triggerLevel: -50,
      triggerTimeOut: 0,
      // 同步搜索
      enabled: false,
      patern: 'FF',
      syncCodeLen: 4,
      searchLen: 1024,
      offset: 0,
      // 均衡
      eqEnabled: false,
      filterLen: 5,
      convergence: 1.0,
      hold: false
    }
  },
  getters: {
    centerFreqIn() {
      return global.centerFreqIn
    },
    debugMode() {
      return global.debugMode
    },
    samplingRate() {
      return global.samplingRate
    },
    bitRate() {
      return global.bitRate
    },
    intermediateFrequencyBandwidth() {
      return global.intermediateFrequencyBandwidth
    },
    modulation() {
      return FILE_TYPE_BITS[this.debugMode]?.bits
    }
  }
})
