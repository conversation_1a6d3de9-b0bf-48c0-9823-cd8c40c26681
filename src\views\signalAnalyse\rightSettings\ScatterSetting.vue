<template>
  <div>
    <el-form class="left">
      <!--  Begin:Mod by xf at 2023-12-05 -->
      <el-form-item label="起始频率">
        <el-input v-model="form.startfre" type="number">
          <template #append>
            <el-select v-model="form.cfUnit">
              <el-option v-for="unit in units" :key="unit" :label="unit" :value="unit" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="终止频率">
        <el-input v-model="form.endfre" type="number">
          <template #append>
            <el-select v-model="form.cfUnit">
              <el-option v-for="unit in units" :key="unit" :label="unit" :value="unit" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="驻留时间">
        <el-input v-model="form.stopTime" type="number">
          <template #append>
            <span class="pl-1 pr-1">ms</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="衰减">
        <el-input v-model="form.attenuation" type="number">
          <template #append>
            <span class="pl-1 pr-1">dBm</span>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup>
  const form = ref({
    startfre: 100,
    endfre: 100,
    stopTime: 100,
    attenuation: 100,
    cfUnit: 1,
    bwUnit: 1,
    rrUnit: 1
  })
  const units = ['GHz', 'MHz', 'KHz', 'Hz']
</script>
<!--  End:Mod by xf at 2023-12-05 -->
