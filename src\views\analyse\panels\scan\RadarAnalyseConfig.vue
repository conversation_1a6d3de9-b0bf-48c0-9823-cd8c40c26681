<template>
  <data-form :fields="fields" :model="model" />
  <el-button style="width: 80%;margin-left: 10% ;" @click="resetFrequencyBand">全频段</el-button>
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData';
  import useRadarScanFormStore from '@/store/modules/form/radarScanForm'
  import { rateUnits } from '@/constant/units'
  import { reactive } from 'vue';

  const scanForm = useRadarScanFormStore()
  const fields = ref([
    {
      target: 'pickCf',
      name: '中心频率',
      options: rateUnits,
      appendSelect: true,
      change() {
        scanForm.setRateByBw()
        scanForm.setPoint()
      }
    },
    {
      target: 'pickBw',
      name: '中频带宽',
      options: rateUnits,
      appendSelect: true,
      change() {
        scanForm.setRateByBw()
        scanForm.setPoint()
      }
    },
    {
      target: 'startRate',
      name: '起始频率',
      options: rateUnits,
      appendSelect: true,
      change() {
        scanForm.setPoint()
        scanForm.setCfAndBwByRate()
      }
    },
    {
      target: 'endRate',
      name: '终止频率',
      options: rateUnits,
      appendSelect: true,
      change() {
        scanForm.setPoint()
        scanForm.setCfAndBwByRate()
      }
    },
  ])

  const model = reactive(new ModelData(fields.value, 'radarScan'))
  const resetFrequencyBand = () => {
    scanForm.init(scanForm.fileIfbw, scanForm.fileCenterFreIn)
  }
  watch(() => scanForm, () => {
    model.update()
  }, { deep: true })
</script>