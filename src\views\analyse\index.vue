<template>
  <el-steps
    v-if="stepsVisible"
    :active="active"
    finish-status="success"
    simple
    class="mb-4 steps"
  >
    <el-step
      v-for="(item, index) in views"
      :key="item.name"
      :title="item.label"
      @click="toItemPath(item, index)"
    />
  </el-steps>
  <div class="main-view">
    <!-- <component :is="viewMain" /> -->
    <router-view v-slot="{ Component }">
      <component :is="Component" />
    </router-view>
  </div>
  <div v-if="stepsVisible" class="bottom">
    <el-button @click="prev">上一步</el-button>
    <el-button @click="next">下一步</el-button>
  </div>
</template>

<script setup>
  import useChartsStore from '@/store/modules/charts'
  import { useRoute } from 'vue-router';

  const views = computed(() => {
    const list = [
      { label: '选择分析区域', name: 'SectionArea', path: 'sectionarea' },
      { label: '配置全局参数', name: 'GlobalConfig', path: 'globalconfig' },
      { label: '查看分析结果', name: 'ZeroSpan', path: 'zerospan'},
    ]
    if (useRoute().path.includes('radar')) { // 雷达分析页面
      list.forEach(item => item.name = 'Radar' + item.name)
    }
    return list
  })
  const chartsStore = useChartsStore()
  const defaultIndex = views.value.findIndex(view => useRoute().path.includes(view.path)) || 0
  const active = ref(defaultIndex)
  const router = useRouter()
  const stepsVisible = ref(false)
  const next = () => {
    active.value = active.value + 1
    if (active.value > 2) active.value = 0
    go(views.value[active.value])
  }

  const prev = () => {
    active.value -= 1
    if (active.value < 0) active.value = 2
    go(viewviews.values[active.value])
  }

  const toItemPath = (item, index) => {
    if (active.value < index) {
      return
    }
    active.value = index
    go(item)
  }

  const go = (item,) => {
    router.push({ name: item.name })
  }

  // const viewMain = computed(() => {    
  //   switch(active.value) {
  //     case 0:
  //       return defineAsyncComponent(() => import('@/views/analyse/pages/SectionArea.vue'))
  //     case 1:
  //       return defineAsyncComponent(() => import('@/views/analyse/pages/GlobalConfig.vue'))
  //     case 2:
  //       return defineAsyncComponent(() => import('@/views/analyse/mode/Harmonic.vue'))
  //   }
  // })

  onBeforeMount(() => {
    // 将本地缓存写入store里面的内容
    chartsStore.readStorage()
  })

  onUnmounted(() => {
    // 将store里面的内容写入本地缓存
    chartsStore.writeStorage()
  })
  window.onunload = function() {
    chartsStore.writeStorage()
  }
</script>

<style scoped>
.main-view {
  /* height: calc(100% - 100px); */
  height: 100%
}
.bottom {
  float: right;
  padding-top: 6px;
}
</style>