import { defineStore } from 'pinia'
import useChartsStore from '../charts'
import { round, cloneDeep } from 'lodash'

const { global } = useChartsStore()
export default defineStore('randarScanFormStore', {
  state: () => {
    const { radarfile } = useChartsStore()
    return {
      // 信号文件参数
      fileCenterFreIn: 0,
      fileDebugMode: 0,
      fileSamplingRate: 0,
      fileBitRate: 0,
      fileIfbw: 0,
      iqNum: 0,
      iqReverse: false, // IQ 颠倒
      logarithm: false, // 取对数

      //下变频
      downCenterFreIn: 0,

      //滤波
      filterorder: 7,
      coefficientDown: 0.33,

      // 参考线
      refLine: false,
      level: -80,

      // 分析设置
      allLen: 0,
      pickCf: 0,
      pickBw: 0,
      startRate: 0,
      endRate: 0,

      //信号选取
      allPointLen: 0,
      startPoint: 0,
      endPoint: 65535,

      amScale: 10,
      capital: false,
      currentFileParams: radarfile,
      path: 1
    }
  },
  getters: {
    len() {
      return round(this.endPoint) - round(this.startPoint) + 1
    },
    bandwidth() {
      return this.endRate - this.startRate
    },
    centerFreqIn() {
      return global.radarcenterFreqIn
    },
    debugMode() {
      return global.debugMode
    },
    samplingRate() {
      return global.samplingRate
    },
    bitRate() {
      return global.bitRate
    },
    intermediateFrequencyBandwidth() {
      return global.radarintermediateFrequency
    },
    refLevel() {
      return global.refLevel
    },
    scale() {
      if (this.fileIfbw) {
        return this.allLen / this.fileIfbw
      }
    },
    left() {
      return this.fileCenterFreIn - this.fileIfbw / 2
    }
  },
  actions: {
    init(bw, cf) {
      this.pickCf = cf * 1
      this.pickBw = bw * 1
      this.setRateByBw()
      this.setPoint()
    },
    // 根据中心频率，中频带宽设置起始点
    setRateByBw() {
      this.startRate = this.pickCf - this.pickBw / 2
      this.endRate = this.pickCf + this.pickBw / 2
    },
    setCfAndBwByRate() {
      this.pickBw = this.endRate - this.startRate
      this.pickCf = this.startRate + this.pickBw / 2
    },
    setRateByPoint() {
      if (!this.scale) {
        return
      }
      this.startRate = this.startPoint / this.scale + this.left
      this.endRate = (this.endPoint + 1) / this.scale + this.left
    },
    setPoint() {
      if (!this.scale) {
        return
      }
      this.startPoint = round(this.scale * (this.startRate - this.left))
      this.endPoint = round(this.scale * (this.endRate - this.left) - 1)
    },

    setFilterorder() {
      return this.filterorder
    },
    setCoefficientDown(val) {
      this.coefficientDown = parseFloat(val)
    },
    setCenterFre() {
      if (this.downCenterFreIn !== 0) {
        this.fileCenterFreIn = this.downCenterFreIn
      }
    },
    getFileParams() {
      return cloneDeep(this.currentFileParams)
    },
    setFileParams(data) {
      this.currentFileParams = data
    }
  }
})
