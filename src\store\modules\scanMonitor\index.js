import { defineStore } from 'pinia'

export default defineStore('scanStore', {
  state: () => {
    return {
      spectrum: {
        // 频段扫描
        centerFreq: 300000000, // 中心频率
        bandwidth: 20000000, // 扫宽
        startFre: 20000000, // 起始频率
        endFre: 40000000, // 起始频率
        stepLength: 200000, // 步长 一次前进多少: 2000,
        centerFreqUnit: 'Hz', // 中心频率单位 Hz/KHz/MHz/GHz
        bandwidthUnit: 'Hz', // 扫宽单位 Hz/KHz/MHz/GHz
        startFreqUnit: 'Hz', // 起始频率单位 Hz/KHz/MHz/GHz
        endFreqUnit: 'Hz', // 结束频率单位 Hz/KHz/MHz/GHz
        hideTab: true, // 是否隐藏扫描/测向切换tab
        freNum: 5, // 频段数
        refLevel: -60,
        discrete: [], // 离散频率列表
        commandType: 0, // 指令类型 0 分析 1 测向
        type: 0, // 分析类型， 0 全景  1 离散
        taskParams: {},
        resultLen: 0,
        thresholdType: 0,
        threshold: 0,
        status: 0, // 数据传输状态 0 开始传输数据 1 结束传输数据 // Begin:Add by xf at 2023-12-07
      },
      hostSpectrum: {
        // 频段扫描
        centerFreq: 30000000, // 中心频率
        bandwidth: 20000000, // 扫宽
        startFre: 20000000, // 起始频率
        endFre: 40000000, // 起始频率
        stepLength: 200000, // 步长 一次前进多少: 2000,
        freNum: 5, // 频段数
        refLevel: -60,
        discrete: [], // 离散频率列表
        commandType: 0, // 指令类型 0 分析 1 测向
        type: 0, // 分析类型， 0 全景  1 离散
        taskParams: {},
        resultLen: 0,
        thresholdType: 0,
        threshold: 0,
        status: 0, // 数据传输状态 0 开始传输数据 1 结束传输数据 // Begin:Add by xf at 2023-12-07
      },
      handsetSpectrum1: {
        // 频段扫描
        centerFreq: 30000000, // 中心频率
        bandwidth: 20000000, // 扫宽
        startFre: 20000000, // 起始频率
        endFre: 40000000, // 起始频率
        stepLength: 200000, // 步长 一次前进多少: 2000,
        freNum: 5, // 频段数
        refLevel: -60,
        discrete: [], // 离散频率列表
        commandType: 0, // 指令类型 0 分析 1 测向
        type: 0, // 分析类型， 0 全景  1 离散
        taskParams: {},
        resultLen: 0,
        thresholdType: 0,
        threshold: 0,
        status: 0, // 数据传输状态 0 开始传输数据 1 结束传输数据 // Begin:Add by xf at 2023-12-07
      },
      handsetSpectrum2: {
        // 频段扫描
        centerFreq: 30000000, // 中心频率
        bandwidth: 20000000, // 扫宽
        startFre: 20000000, // 起始频率
        endFre: 40000000, // 起始频率
        stepLength: 200000, // 步长 一次前进多少: 2000,
        freNum: 5, // 频段数
        refLevel: -60,
        discrete: [], // 离散频率列表
        commandType: 0, // 指令类型 0 分析 1 测向
        type: 0, // 分析类型， 0 全景  1 离散
        taskParams: {},
        resultLen: 0,
        thresholdType: 0,
        threshold: 0,
        status: 0, // 数据传输状态 0 开始传输数据 1 结束传输数据 // Begin:Add by xf at 2023-12-07
      },
      spectrumCopy: {
        // 频段扫描
        centerFreq: 30000000, // 中心频率
        bandwidth: 20000000, // 扫宽
        startFreq: 20000000, // 起始频率
        endFreq: 40000000, // 起始频率
        step: '12500', // 步长 一次前进多少: 2000,
        centerFreqUnit: 'Hz', // 中心频率单位 Hz/KHz/MHz/GHz
        bandwidthUnit: 'Hz', // 扫宽单位 Hz/KHz/MHz/GHz
        startFreqUnit: 'Hz', // 起始频率单位 Hz/KHz/MHz/GHz
        endFreqUnit: 'Hz', // 结束频率单位 Hz/KHz/MHz/GHz
        hideTab: true, // 是否隐藏扫描/测向切换tab
        freqSectionNum: 5, // 频段数
        refLevel: -60,
        isSaveFreq: false,
        discrete: [
          // test-测试离散数据:
          // {
          //   "frequency": 100000000,
          //   "analysisBandwidth": 10000000
          // },
          // {
          //   "frequency": 200000000,
          //   "analysisBandwidth": 20000000
          // },
          // {
          //   "frequency": 300000000,
          //   "analysisBandwidth": 30000000
          // }
        ]
        , // 离散频率列表
        freqBandStr: '',
        commandType: 0, // 指令类型 0 分析 1 测向
        type: 0, // 分析类型， 0 全景  1 离散
        taskParams: {},
        resultLen: 0,
        thresholdType: 0,
        threshold: 0,
        status: 0, // 数据传输状态 0 开始传输数据 1 结束传输数据 // Begin:Add by xf at 2023-12-07
      },
      routineAnalysis: {
        // 频段扫描
        centerFreq: 30000000, // 中心频率
        bandwidth: 20000000, // 扫宽
        startFreq: 20000000, // 起始频率
        endFreq: 40000000, // 起始频率
        step: '12500', // 步长 一次前进多少: 2000,
        centerFreqUnit: 'Hz', // 中心频率单位 Hz/KHz/MHz/GHz
        bandwidthUnit: 'Hz', // 扫宽单位 Hz/KHz/MHz/GHz
        startFreqUnit: 'Hz', // 起始频率单位 Hz/KHz/MHz/GHz
        endFreqUnit: 'Hz', // 结束频率单位 Hz/KHz/MHz/GHz
        hideTab: true, // 是否隐藏扫描/测向切换tab
        freqSectionNum: 5, // 频段数
        refLevel: -60,
        discrete: [], // 离散频率列表
        freqBandStr: '',
        commandType: 0, // 指令类型 0 分析 1 测向
        type: 0, // 分析类型， 0 全景  1 离散
        taskParams: {},
        resultLen: 0,
        thresholdType: 0,
        threshold: 0,
        status: 0, // 数据传输状态 0 开始传输数据 1 结束传输数据 // Begin:Add by xf at 2023-12-07
      },
      monitor: {
        // 信号（雷达）监测
        centerFreq: 30e6,
        bandwidth: 20e6,
        startFre: 20e6,
        endFre: 40e6,
        resolutionRatio: 25e6,
        hideTab: true, // 是否隐藏扫描/测向切换tab
        stayTime: 1000,
        attenuation: 0,
        commandType: 0, // 指令类型 0 分析 1 测向
        status: 0, // 数据传输状态 0 未传输 1 传输中
        type: 0, // 监测类型 0 通信 1 雷达
        taskParams: {},
        refLevel: -60,
        resultLen: 0
      },
      radar: {
        // 雷达分析
        centerFreq: 30e6,
        bandwidth: 20e6,
        startFre: 20e6,
        endFre: 40e6,
        resolutionRatio: 25e6,
        commandType: 0, // 指令类型 0 分析 1 测向
        status: 0, // 数据传输状态 0 未传输 1 传输中
        type: 1,
        hideTab: true, // 是否隐藏扫描/测向切换tab
        refLevel: -60,
        directionAmplitudes: {
          north: [],
          car: [],
          am: []
        },
        resultLen: 0
      },
      signal: {
        // 信号分析
        centerFreq: 30e6,
        bandwidth: 120e3,
        startFreq: 20e6,
        endFreq: 40e6,
        centerFreqUnit: 'Hz', // 中心频率单位 Hz/KHz/MHz/GHz
        bandwidthUnit: 'Hz', // 扫宽单位 Hz/KHz/MHz/GHz
        startFreqUnit: 'Hz', // 起始频率单位 Hz/KHz/MHz/GHz
        endFreqUnit: 'Hz', // 结束频率单位 Hz/KHz/MHz/GHz
        commandType: 0, // 指令类型 0 分析 1 测向
        status: 0, // 数据传输状态 0 未传输 1 传输中
        type: 0,
        hideTab: true, // 是否隐藏扫描/测向切换tab
        demodulationBandwidth: 120e3,
        audioDemodulation: 1,
        xdbBandwidth: 3,
        bBandwidth: 999,
        azimuth: [0, 3600],
        pitch: [-900, 900],
        demodulationType: 1,
        refLevel: -60,
        resultLen: 0
      },
      playFfts: reactive([]), // 播放的点
      playSignalList: [], // 播放的信号列表
      playAnalyzeFfts: reactive([]), // 播放的点
      playAnalyzeSignalList: [], // 播放的信号列表
    }
  },
  getters: {},
  actions: {
    setSpectrumData(target, data) {
      this.$patch(state => {
        Object.assign(state[target], data);
      });
    },
    resetSpectrumData(target) {
      this.$patch(state => {
        state[target] = { ...state.defaultSpectrumCopy };
      });
    },
    updateTaskParams(target, newTaskParams) {
      this.$patch(state => {
        state[target].taskParams = { ...newTaskParams };
      });
    },
    setPlayFfts(newFfts) {
      this.playFfts = newFfts;
    },
    setAnalyzePlayFfts(newFfts) {
      this.playAnalyzeFfts = newFfts;
    },
    updateSpectrumUnit(spectrumKey, key, unit) {
      this.$patch(state => {
        state[spectrumKey][key + 'Unit'] = unit;
      });
    }
  },
})
