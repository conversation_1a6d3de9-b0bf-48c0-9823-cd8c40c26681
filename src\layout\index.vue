<template>
  <div :class="classObj" class="app-wrapper" :style="wrapperStyle()">
    <template v-if="!upperComputerMode">
      <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
      <sidebar v-if="!sidebar.hide" class="sidebar-container" />
    </template>
    <uc-nav v-if="upperComputerMode" />
    <uc-sidebar v-if="upperComputerMode" />
    <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }" class="main-container">
      <div v-if="!upperComputerMode" :class="{ 'fixed-header': fixedHeader }">
        <navbar @setLayout="setLayout" />
        <div class="app-top-views">
          <tags-view v-if="needTagsView" />
          <step-control v-if="needStepControl" />
        </div>
      </div>
      <app-main />
      <settings ref="settingRef" />
    </div>
  </div>
</template>

<script setup name="MainView">
import { useWindowSize } from '@vueuse/core'
import Sidebar from './components/Sidebar/index.vue'
import { AppMain, Navbar, Settings, TagsView } from './components'
import defaultSettings from '@/settings'
import StepControl from '@/views/analyse/header/StepControl.vue'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import { settings as globalSettings } from '@/utils/settings'
import UcSidebar from './components/UcSidebar'
import UcNav from './components/UcNav'
import UcBg from '@/assets/images/specscanbg.png'
import { useRoute } from 'vue-router'

defineComponent([StepControl])

const settingsStore = useSettingsStore()
const theme = computed(() => settingsStore.theme);
const sideTheme = computed(() => settingsStore.sideTheme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const needTagsView = computed(() => settingsStore.tagsView);
const fixedHeader = computed(() => settingsStore.fixedHeader);
const needStepControl = computed(() => useRoute().path.includes('analyse'))
const upperComputerMode = computed(() => Boolean(globalSettings.VITE_GLOB_UPPER_COMPUTER_MODE * 1))
const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === 'mobile'
}))

const { width, height } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

watchEffect(() => {
  if (device.value === 'mobile' && sidebar.value.opened) {
    useAppStore().closeSideBar({ withoutAnimation: false })
  }
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice('mobile')
    useAppStore().closeSideBar({ withoutAnimation: true })
  } else {
    useAppStore().toggleDevice('desktop')
  }
})

const wrapperStyle = () => {
  const style = { '--current-color': theme }
  if (upperComputerMode.value) {
    style.background = `url(${UcBg}) no-repeat`
    style.backgroundSize = 'cover'
  } 
  return style
}

function handleClickOutside() {
  useAppStore().closeSideBar({ withoutAnimation: false })
}

const settingRef = ref(null);
function setLayout() {
  settingRef.value.openSetting();
}
</script>

<style lang="scss" scoped>
  @import "@/assets/styles/mixin.scss";
  @import "@/assets/styles/variables.module.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}

.app-top-views {
  display: flex;
  background-color: var(--el-bg-color);
  align-items: center;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
}

</style>