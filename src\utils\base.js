import request from '@/utils/request'
import { ElMessage, ElLoading } from 'element-plus'
import { tansParams, blobValidate } from '@/utils/utils'
import { saveAs } from 'file-saver'
let downloadLoadingInstance
// 通用下载方法
export function download (url, params, filename, config) {
  downloadLoadingInstance = ElLoading.service({
    text: '正在下载数据，请稍候',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  return request({
    url,
    params,
    transformRequest: [
      params => {
        return tansParams(params)
      }
    ],
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob',
    ...config
  })
    .then(async data => {
      const isLogin = await blobValidate(data)
      if (isLogin) {
        const blob = new Blob([data])
        saveAs(blob, filename)
        ElMessage.success('下载成功！')
      } else {
        const resText = await data.text()
        const rspObj = JSON.parse(resText)
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
        ElMessage.error(errMsg)
      }
      downloadLoadingInstance.close()
    })
    .catch(r => {
      console.error(r)
      ElMessage.error('下载文件出现错误，请联系管理员！')
      downloadLoadingInstance.close()
    })
}
