<script setup name="BottomTable">
  import CommonTable from './CommonTable.vue'
  import MeasureTable from './Measure.vue'
  import OrientationTable from './Orientation.vue'
  import useScanMonitorStore from '@/store/modules/scanMonitor'

  const { signal } = useScanMonitorStore()

  const props = defineProps({
    paramList: {
      type: Object,
      default: () => ({})
    },
    modulationList: {
      type: Array,
      default: () => []
    }
  })
</script>

<template>
  <el-tabs class="mt-6">
    <el-row :gutter="8">
      <el-col :span="16">
        <OrientationTable v-if="signal.commandType" :param-list="paramList" />
        <MeasureTable v-else :param-list="paramList" />
      </el-col>
      <el-col :span="8">
        <CommonTable :modulation-list="modulationList" />
      </el-col>
    </el-row>
  </el-tabs>
</template>
