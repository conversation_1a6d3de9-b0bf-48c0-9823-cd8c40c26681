<template>
  <div class="app-container">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      @selectionChange="rows => (selection = rows)"
    />
    <mtDetailInfo ref="refsMt" @actSuccess="getList()" />
    <c-import ref="refsImport" />
  </div>
</template>

<script>
  import * as mtApi from '@/api/business/course'
  import mtDetailInfo from './tmpInfo.vue'
  export default {
    name: 'Course',
    components: { mtDetailInfo },
    props: {},
    data () {
      return {
        selection: [],
        tableFormConfig: {
          title: '课程管理',
          searchConfig: {
            formConfig: [
              { title: '课程编号', name: 'courCode' },
              { title: '课程名称', name: 'courName' }
            ],
            loadData: p => {
              return mtApi.listCourse(p)
            }
          },
          actions: [
            {
              label: '新增',
              type: 'primary',
              icon: 'el_Plus',
              click: () => {
                this.$refs.refsMt.show()
              }
            },
            {
              label: '导出',
              icon: 'el_upload',
              type:'warning',
              isPlain: true,
              click: row =>
                this.download(
                  'business/course/export',
                  {
                    // ...queryParams.value
                  },
                  `课程管理_${new Date().getTime()}.xlsx`
                )
            },
            {
              type: 'danger',
              label: '删除',
              disabled: () => !this.selection.length,
              isPlain: true,
              icon: 'el_Delete',
              click: row => this.deleteAll()
            },
            {
             
              label: '导入',  
              type: 'info',       
              icon: 'el_download',
              click: row => {
                this.importFile()
              }
            }
          ],
          tableConfig: {
            isChecked: true,
            cols: [
              { label: '课程名称', prop: 'courName', minWidth: 200 },
              { label: '课程编号', prop: 'courCode', width: 200 },
              { label: '教材', prop: 'courBook', width: 200 },
              { label: '教材年份', prop: 'courYear' },
              {
                label: '操作',
                type: 'action',
                width: '120',
                actions: [
                  {
                    title: '编辑',
                    icon: 'el_Edit',
                    permission: ['business:course:edit'],
                    click: row => this.$refs.refsMt.show(row.courId, row.oldBook, true)
                  },
                  {
                    title: '复制',
                    icon: 'el_CopyDocument',
                    permission: ['business:course:copySrCourse'],
                    click: row =>
                      this.$modal.prompt('课程名称', '提示').then(({ value }) => {
                        if (!value || value.length > 50)
                          return this.$modal.msgWarning('请输入课程名称，最多50字')
                          mtApi.copyCourse({ ...row, courName: value }).then(res => {
                          this.$modal.msgSuccess('操作成功')
                          this.getList()
                        })
                      })
                  },
                  {
                    title: '删除',
                    icon: 'el_Delete',
                    permission: ['business:course:remove'],
                    click: row => this.deleteAll(row.courId)
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList (data = null) {
        this.$refs.refsTableForm.loadData(data)
      },
      //批量删除模板
      deleteAll (id = null) {
        if (!this.selection.length && !id) return this.$model.msgwarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选课程吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.courId)
          mtApi.delCourse(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.getList()
          })
        })
      },
      importFile(){
        this.$refs.refsImport.show({
         tmpUrl: '/business/course/excelTemplate',
         tmpName:'课程管理',
         uploadUrl: '/business/course/importData',
          ok: res => {
            this.getList()
          }
        })
      }
    }
  }
</script>
