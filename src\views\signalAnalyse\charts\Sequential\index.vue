<template>
  <div id="sequential" />
</template>

<script setup name="FmPlot">
import Highcharts from '@/plugins/highcharts'
import config from './config';
import  mockFn from './mock';

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

let instance = null

const updateData = (data) => {
  if (!instance) {
    return
  }
  instance.series[0].setData(data)
}
onMounted(() => {
  instance = new Highcharts.Chart('sequential', config)
  const data = mockFn.getData(1000)
  updateData(data)
})

watch(() => props.data, (val) => {
  if (val.length > 0) {
    updateData(val)
  }
})
</script>