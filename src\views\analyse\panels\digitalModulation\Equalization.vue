<template>
  <data-form :model="model" :fields="fields" />
  <el-button>重设过滤器</el-button>
</template>

<script setup>

import DataForm from '@/components/DataForm'
import ModelData from '@/common/classes/modelData'

const fields = [
  { target: 'eqEnabled', name: "功能开关", isCheckbox: true },
  { target: 'filterLen', name: "过滤长度", appendText: '符号' },
  { target: 'convergence', name: "收敛" },
  { target: 'hold', name: "保持", isCheckbox: true },
]

const model = reactive(new ModelData(fields, 'dm'))
</script>
