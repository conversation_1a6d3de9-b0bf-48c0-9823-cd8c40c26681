<script setup>
  import { ref, reactive } from 'vue'

  // 手动校准数据
  const manualCalibration = reactive({
    startFreq: '30.000000',
    stopFreq: '5.999999',
    calibrationWidth: '40MHz',
    calibrationItem: '全部',
    calibrationType: 'internal' // 'internal' or 'external'
  })

  // 自动校准数据
  const autoCalibration = reactive({
    startFreq: '30.000',
    stopFreq: '',
    calibrationWidth: '40MHz',
    calibrationItem: '全部',
    workMode: '全部',
    signalSource: 'SigHound',
    scpiPort: '',
    signalPower: '10',
    signalPowerUnit: 'dBm',
    signalFreqOffset: '100',
    signalFreqOffsetUnit: 'ms',
    measurementTime: '3',
    measurementTimeUnit: 'dB'
  })

  // 校准数据表格
  const calibrationData = ref([
    {
      id: 1,
      startFreq: '',
      stopFreq: '',
      bandwidth: '',
      workMode: '',
      calibrationFactor: '',
      selected: false
    }
  ])

  // 分页数据
  const pagination = reactive({
    current: 2,
    total: 100,
    pageSize: 10
  })

  // 方法
  const generateCalibrationData = () => {
    console.log('生成校准对象')
  }

  const startCalibration = () => {
    console.log('开始校准')
  }

  const saveCalibration = () => {
    console.log('保存校准')
  }

  const startAutoCalibration = () => {
    console.log('启动校准')
  }

  const addRow = () => {
    console.log('新增')
  }

  const importData = () => {
    console.log('导入')
  }

  const linkData = () => {
    console.log('链路')
  }

  const deleteRow = () => {
    console.log('删除')
  }

  const clearData = () => {
    console.log('清空')
  }

  const saveData = () => {
    console.log('保存')
  }

  const exportData = () => {
    console.log('导出')
  }

  const deleteSelected = () => {
    console.log('删除选中行')
  }
</script>

<template>
  <div class="level-calibration">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <!-- 设备选择 -->
      <div class="device-section">
        <label class="section-label">设备选择</label>
        <select class="device-select">
          <option>从设备1</option>
        </select>
      </div>

      <!-- 手动校准 -->
      <div class="manual-calibration">
        <h3 class="section-title">手动校准</h3>

        <div class="form-group">
          <label>起始频率</label>
          <div class="input-with-unit">
            <input v-model="manualCalibration.startFreq" type="text" />
            <select>
              <option>MHz</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>截止频率</label>
          <div class="input-with-unit">
            <input v-model="manualCalibration.stopFreq" type="text" />
            <select>
              <option>GHz</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>校准带宽</label>
          <select v-model="manualCalibration.calibrationWidth">
            <option>40MHz</option>
          </select>
        </div>

        <div class="form-group">
          <label>校准项目</label>
          <select v-model="manualCalibration.calibrationItem">
            <option>全部</option>
          </select>
        </div>

        <div class="radio-group">
          <label>
            <input type="radio" v-model="manualCalibration.calibrationType" value="internal" />
            内部校准
          </label>
          <label>
            <input type="radio" v-model="manualCalibration.calibrationType" value="external" />
            外部校准
          </label>
        </div>

        <button class="generate-btn" @click="generateCalibrationData"> 生成校准对象 </button>
      </div>

      <!-- 自动校准 -->
      <div class="auto-calibration">
        <h3 class="section-title">自动校准</h3>

        <div class="form-group">
          <label>起始频率</label>
          <div class="input-with-unit">
            <input v-model="autoCalibration.startFreq" type="text" />
            <select>
              <option>MHz</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>截止频率</label>
          <div class="input-with-unit">
            <input v-model="autoCalibration.stopFreq" type="text" />
            <select>
              <option>GHz</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>校准带宽</label>
          <select v-model="autoCalibration.calibrationWidth">
            <option>40MHz</option>
          </select>
        </div>

        <div class="form-group">
          <label>校准项目</label>
          <select v-model="autoCalibration.calibrationItem">
            <option>全部</option>
          </select>
        </div>

        <div class="form-group">
          <label>工作模式</label>
          <select v-model="autoCalibration.workMode">
            <option>全部</option>
          </select>
        </div>

        <div class="form-group">
          <label>信号源设备</label>
          <select v-model="autoCalibration.signalSource">
            <option>SigHound</option>
          </select>
        </div>

        <div class="form-group">
          <label>信号源IP</label>
          <input v-model="autoCalibration.scpiPort" type="text" />
        </div>

        <div class="form-group">
          <label>SCPI端口</label>
          <input v-model="autoCalibration.scpiPort" type="text" />
        </div>

        <div class="form-group">
          <label>信号功率</label>
          <div class="input-with-unit">
            <input v-model="autoCalibration.signalPower" type="text" />
            <select v-model="autoCalibration.signalPowerUnit">
              <option>dBm</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>信号频偏时间</label>
          <div class="input-with-unit">
            <input v-model="autoCalibration.signalFreqOffset" type="text" />
            <select v-model="autoCalibration.signalFreqOffsetUnit">
              <option>ms</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>测量时间</label>
          <div class="input-with-unit">
            <input v-model="autoCalibration.measurementTime" type="text" />
            <select v-model="autoCalibration.measurementTimeUnit">
              <option>dB</option>
            </select>
          </div>
        </div>

        <div class="warning-text"> 务必确认线缆数据准确！！！ </div>

        <div class="auto-buttons">
          <button class="start-btn" @click="startCalibration">开始校准</button>
          <button class="save-btn" @click="saveCalibration">保存校准</button>
        </div>

        <button class="auto-calibration-btn" @click="startAutoCalibration"> 启动校准 </button>
      </div>
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <div class="calibration-data-section">
        <h3 class="section-title">校准数据</h3>

        <!-- 数据类型选择 -->
        <div class="data-type-tabs">
          <button class="tab-btn active">数据校准数据</button>
          <button class="tab-btn">IQ校准数据</button>
        </div>

        <!-- 频率范围输入 -->
        <div class="freq-range">
          <div class="freq-input-group">
            <label>起始频率</label>
            <div class="input-with-unit">
              <input value="30.000000" type="text" />
              <span class="unit">MHz</span>
            </div>
          </div>
          <div class="freq-input-group">
            <label>截止频率</label>
            <div class="input-with-unit">
              <input value="1.000" type="text" />
              <span class="unit">kHz</span>
            </div>
          </div>
          <button class="auto-freq-btn">自动频率校准</button>
        </div>

        <div class="freq-range">
          <div class="freq-input-group">
            <label>截止频率</label>
            <div class="input-with-unit">
              <input value="5999.999999" type="text" />
              <span class="unit">MHz</span>
            </div>
          </div>
          <div class="freq-input-group">
            <label>截止频率</label>
            <div class="input-with-unit">
              <input value="1.000" type="text" />
              <span class="unit">kHz</span>
            </div>
          </div>
          <button class="save-freq-btn">保存频率校准数据</button>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button class="btn btn-primary" @click="addRow">新增</button>
          <button class="btn btn-secondary" @click="importData">导入</button>
          <button class="btn btn-secondary" @click="linkData">链路</button>
          <button class="btn btn-secondary" @click="deleteRow">删除</button>
          <button class="btn btn-danger" @click="clearData">清空</button>
          <button class="btn btn-success" @click="saveData">保存</button>
          <button class="btn btn-secondary" @click="exportData">导出</button>
        </div>

        <!-- 数据表格 -->
        <div class="data-table">
          <table>
            <thead>
              <tr>
                <th><input type="checkbox" /></th>
                <th>序号</th>
                <th>起始频率(MHz)</th>
                <th>截止频率(MHz)</th>
                <th>带宽(MHz)</th>
                <th>工作模式</th>
                <th>校准因子(dBm)</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in calibrationData" :key="item.id">
                <td><input type="checkbox" v-model="item.selected" /></td>
                <td>{{ index + 1 }}</td>
                <td>{{ item.startFreq }}</td>
                <td>{{ item.stopFreq }}</td>
                <td>{{ item.bandwidth }}</td>
                <td>{{ item.workMode }}</td>
                <td>{{ item.calibrationFactor }}</td>
                <td>
                  <button class="btn-delete" @click="deleteSelected">删除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <button class="page-btn">上一个</button>
          <span class="page-number">{{ pagination.current }}</span>
          <button class="page-btn active">2</button>
          <span class="page-number">3</span>
          <button class="page-btn">下一个</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .level-calibration {
    display: flex;
    height: 100vh;
    font-family: 'Microsoft YaHei', sans-serif;
    position: relative;
  }

  .left-panel {
    width: 360px;
    padding: 20px;
    border-right: 1px solid;
    overflow-y: auto;
  }

  .right-panel {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .device-section {
    margin-bottom: 20px;

    .section-label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .device-select {
      width: 100%;
      padding: 8px;
      border: 1px solid;
      border-radius: 4px;
      font-size: 14px;
    }
  }

  .manual-calibration,
  .auto-calibration {
    border: 1px solid;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;

    .section-title {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: bold;
      border-bottom: 1px solid;
      padding-bottom: 8px;
    }
  }

  .form-group {
    margin-bottom: 12px;

    label {
      display: block;
      margin-bottom: 4px;
      font-size: 12px;
    }

    input,
    select {
      width: 100%;
      padding: 6px 8px;
      border: 1px solid;
      border-radius: 4px;
      font-size: 12px;
    }
  }

  .input-with-unit {
    display: flex;
    gap: 4px;

    input {
      flex: 1;
    }

    select {
      width: 60px;
      flex-shrink: 0;
    }
  }

  .radio-group {
    display: flex;
    gap: 15px;
    margin: 12px 0;

    label {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      cursor: pointer;

      input[type='radio'] {
        width: auto;
        margin: 0;
      }
    }
  }

  .generate-btn,
  .auto-calibration-btn {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    margin-top: 15px;
  }

  .auto-buttons {
    display: flex;
    gap: 10px;
    margin: 15px 0;

    .start-btn,
    .save-btn {
      flex: 1;
      padding: 8px;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
    }
  }

  .warning-text {
    font-size: 12px;
    text-align: center;
    margin: 10px 0;
    font-weight: bold;
  }

  .calibration-data-section {
    border: 1px solid;
    border-radius: 8px;
    padding: 20px;

    .section-title {
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: bold;
    }
  }

  .data-type-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;

    .tab-btn {
      padding: 8px 16px;
      border: 1px solid;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
    }
  }

  .freq-range {
    display: flex;
    align-items: flex-end;
    gap: 15px;
    margin-bottom: 15px;

    .freq-input-group {
      flex: 1;

      label {
        display: block;
        margin-bottom: 4px;
        font-size: 12px;
      }

      .input-with-unit {
        display: flex;
        align-items: center;

        input {
          flex: 1;
          padding: 6px 8px;
          border: 1px solid;
          border-radius: 4px;
          font-size: 12px;
        }

        .unit {
          margin-left: 8px;
          font-size: 12px;
        }
      }
    }

    .auto-freq-btn,
    .save-freq-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      white-space: nowrap;
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    flex-wrap: wrap;

    .btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
    }
  }

  .data-table {
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;

    table {
      width: 100%;
      border-collapse: collapse;

      th,
      td {
        padding: 8px 12px;
        text-align: left;
        border-bottom: 1px solid;
        font-size: 12px;
      }

      th {
        font-weight: bold;
      }

      .btn-delete {
        padding: 4px 8px;
        border: none;
        border-radius: 3px;
        font-size: 11px;
        cursor: pointer;
      }
    }
  }

  .pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;

    .page-btn {
      padding: 6px 12px;
      border: 1px solid;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
    }

    .page-number {
      font-size: 12px;
    }
  }
</style>
