<script setup>
  import { getEquipList } from '@/api/system/equipment'

  // 选中的设备
  const selectedDevice = ref('')

  // 设备列表
  const deviceList = ref([])

  // 手动校准数据
  const manualCalibration = reactive({
    startFreq: '30.000000',
    startFreqUnit: 'MHz',
    stopFreq: '5.999999',
    stopFreqUnit: 'GHz',
    calibrationWidth: '40MHz',
    calibrationItem: '全部',
    calibrationType: 'increment' // 'increment' or 'overwrite'
  })

  // 自动校准数据
  const autoCalibration = reactive({
    startFreq: '30.000',
    startFreqUnit: 'MHz',
    stopFreq: '',
    stopFreqUnit: 'GHz',
    calibrationWidth: '40MHz',
    calibrationItem: '全部',
    workMode: '全部',
    signalSource: 'SigHound',
    scpiPort: '',
    signalPower: '10',
    signalPowerUnit: 'dBm',
    signalFreqOffset: '100',
    signalFreqOffsetUnit: 'ms',
    measurementTime: '3',
    measurementTimeUnit: 'dB'
  })
  // 查询参数
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10
  })
  // 频率单位选项
  const frequencyUnits = [
    { label: 'GHz', value: 'GHz' },
    { label: 'MHz', value: 'MHz' },
    { label: 'kHz', value: 'kHz' }
  ]

  const calibrationTypeOptions = [
    { label: '增量校准', value: 'increment' },
    { label: '覆盖校准', value: 'overwrite' }
  ]

  // 校准数据区域
  const calibrationDataForm = reactive({
    startFreq1: '30.000000',
    startFreqUnit1: 'MHz',
    stopFreq1: '1.000',
    stopFreqUnit1: 'kHz',
    startFreq2: '5999.999999',
    startFreqUnit2: 'MHz',
    stopFreq2: '1.000',
    stopFreqUnit2: 'kHz'
  })

  // 活动标签页
  const activeTab = ref('data')

  // 校准数据表格
  const calibrationData = ref([
    {
      id: 1,
      startFreq: '',
      stopFreq: '',
      bandwidth: '',
      workMode: '',
      calibrationFactor: '',
      selected: false
    }
  ])

  // 分页数据
  const pagination = reactive({
    current: 2,
    total: 100,
    pageSize: 10
  })

  // 方法
  const generateCalibrationData = () => {
    console.log('生成校准对象')
  }

  const startAutoCalibration = () => {
    console.log('启动校准')
  }

  const addRow = () => {
    console.log('新增')
  }

  const importData = () => {
    console.log('导入')
  }

  const linkData = () => {
    console.log('链路')
  }

  const deleteRow = () => {
    console.log('删除')
  }

  const clearData = () => {
    console.log('清空')
  }

  const saveData = () => {
    console.log('保存')
  }

  const exportData = () => {
    console.log('导出')
  }

  const deleteSelected = () => {
    console.log('删除选中行')
  }

  const getList = async () => {
    await getEquipList(queryParams.value).then(res => {
      deviceList.value = []
      selectedDevice.value = res.data.list[0].code
      res.data.list.forEach(item => {
        deviceList.value.push({
          label: item.name,
          value: item.code,
          ip: item.ip,
          port: item.port
        })
      })
    })
  }

  onMounted(() => {
    getList()
  })
</script>

<template>
  <div class="level-calibration">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <!-- 设备选择 -->
      <div class="device-section">
        <label class="section-label">设备选择</label>
        <el-select v-model="selectedDevice" placeholder="请选择设备" style="width: 100%">
          <el-option
            v-for="item in deviceList"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </div>

      <!-- 手动校准 -->
      <el-card class="manual-calibration">
        <template #header>
          <span class="section-title">手动校准</span>
        </template>

        <el-form :model="manualCalibration" label-width="80px" size="small">
          <el-form-item label="起始频率">
            <el-input v-model="manualCalibration.startFreq">
              <template #append>
                <el-select v-model="manualCalibration.startFreqUnit" style="width: 80px">
                  <el-option label="GHz" value="GHz" />
                  <el-option label="MHz" value="MHz" />
                  <el-option label="kHz" value="kHz" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="截止频率">
            <el-input v-model="manualCalibration.stopFreq">
              <template #append>
                <el-select v-model="manualCalibration.stopFreqUnit" style="width: 80px">
                  <el-option label="GHz" value="GHz" />
                  <el-option label="MHz" value="MHz" />
                  <el-option label="kHz" value="kHz" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="校准带宽">
            <el-select v-model="manualCalibration.calibrationWidth" style="width: 100%">
              <el-option label="40MHz" value="40MHz" />
            </el-select>
          </el-form-item>

          <el-form-item label="校准项目">
            <el-select v-model="manualCalibration.calibrationItem" style="width: 100%">
              <el-option label="全部" value="全部" />
            </el-select>
          </el-form-item>

          <el-form-item label="校准类型">
            <el-radio-group v-model="manualCalibration.calibrationType">
              <el-radio v-for="item in calibrationTypeOptions" :label="item.value">{{
                item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-button
            size="default"
            type="primary"
            @click="generateCalibrationData"
            style="width: 100%"
          >
            生成校准对象
          </el-button>
        </el-form>
      </el-card>

      <!-- 自动校准 -->
      <el-card class="auto-calibration">
        <template #header>
          <span class="section-title">自动校准</span>
        </template>

        <el-form :model="autoCalibration" label-width="80px" size="small">
          <el-form-item label="起始频率">
            <el-input v-model="autoCalibration.startFreq">
              <template #append>
                <el-select v-model="autoCalibration.startFreqUnit" style="width: 80px">
                  <el-option label="GHz" value="GHz" />
                  <el-option label="MHz" value="MHz" />
                  <el-option label="kHz" value="kHz" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="截止频率">
            <el-input v-model="autoCalibration.stopFreq">
              <template #append>
                <el-select v-model="autoCalibration.stopFreqUnit" style="width: 80px">
                  <el-option label="GHz" value="GHz" />
                  <el-option label="MHz" value="MHz" />
                  <el-option label="kHz" value="kHz" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="校准带宽">
            <el-select v-model="autoCalibration.calibrationWidth" style="width: 100%">
              <el-option label="40MHz" value="40MHz" />
            </el-select>
          </el-form-item>

          <el-form-item label="校准项目">
            <el-select v-model="autoCalibration.calibrationItem" style="width: 100%">
              <el-option label="全部" value="全部" />
            </el-select>
          </el-form-item>

          <el-form-item label="工作模式">
            <el-select v-model="autoCalibration.workMode" style="width: 100%">
              <el-option label="全部" value="全部" />
            </el-select>
          </el-form-item>

          <el-form-item label="信号源设备">
            <el-select v-model="autoCalibration.signalSource" style="width: 100%">
              <el-option label="SigHound" value="SigHound" />
            </el-select>
          </el-form-item>

          <el-form-item label="信号源IP">
            <el-input v-model="autoCalibration.scpiPort" />
          </el-form-item>

          <el-form-item label="SCPI端口">
            <el-input v-model="autoCalibration.scpiPort" />
          </el-form-item>

          <el-form-item label="信号功率">
            <el-input v-model="autoCalibration.signalPower">
              <template #append>
                <el-select v-model="autoCalibration.signalPowerUnit" style="width: 80px">
                  <el-option label="dBm" value="dBm" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="信号频偏时间">
            <el-input v-model="autoCalibration.signalFreqOffset">
              <template #append>
                <el-select v-model="autoCalibration.signalFreqOffsetUnit" style="width: 80px">
                  <el-option label="ms" value="ms" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="测量时间">
            <el-input v-model="autoCalibration.measurementTime">
              <template #append>
                <el-select v-model="autoCalibration.measurementTimeUnit" style="width: 80px">
                  <el-option label="dB" value="dB" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>

          <el-alert
            title="务必确认线缆数据准确！！！"
            type="warning"
            :closable="false"
            style="margin: 15px 0"
          />

          <el-button
            size="default"
            type="success"
            @click="startAutoCalibration"
            style="width: 100%"
          >
            启动校准
          </el-button>
        </el-form>
      </el-card>
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <el-card class="calibration-data-section">
        <template #header>
          <span class="section-title">校准数据</span>
        </template>

        <!-- 数据类型选择 -->
        <el-tabs v-model="activeTab" class="data-type-tabs">
          <el-tab-pane label="数据校准数据" name="data" />
          <el-tab-pane label="IQ校准数据" name="iq" />
        </el-tabs>

        <!-- 频率范围输入 -->
        <div class="freq-range">
          <el-form :model="calibrationDataForm" label-width="80px" size="small" inline>
            <el-form-item label="起始频率">
              <el-input v-model="calibrationDataForm.startFreq1" style="width: 150px">
                <template #append>
                  <el-select v-model="calibrationDataForm.startFreqUnit1" style="width: 80px">
                    <el-option label="GHz" value="GHz" />
                    <el-option label="MHz" value="MHz" />
                    <el-option label="kHz" value="kHz" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="截止频率">
              <el-input v-model="calibrationDataForm.stopFreq1" style="width: 150px">
                <template #append>
                  <el-select v-model="calibrationDataForm.stopFreqUnit1" style="width: 80px">
                    <el-option label="GHz" value="GHz" />
                    <el-option label="MHz" value="MHz" />
                    <el-option label="kHz" value="kHz" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary">自动频率校准</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="freq-range">
          <el-form :model="calibrationDataForm" label-width="80px" size="small" inline>
            <el-form-item label="截止频率">
              <el-input v-model="calibrationDataForm.startFreq2" style="width: 150px">
                <template #append>
                  <el-select v-model="calibrationDataForm.startFreqUnit2" style="width: 80px">
                    <el-option label="GHz" value="GHz" />
                    <el-option label="MHz" value="MHz" />
                    <el-option label="kHz" value="kHz" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="截止频率">
              <el-input v-model="calibrationDataForm.stopFreq2" style="width: 150px">
                <template #append>
                  <el-select v-model="calibrationDataForm.stopFreqUnit2" style="width: 80px">
                    <el-option label="GHz" value="GHz" />
                    <el-option label="MHz" value="MHz" />
                    <el-option label="kHz" value="kHz" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="success">保存频率校准数据</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button type="primary" @click="addRow">新增</el-button>
          <el-button @click="importData">导入</el-button>
          <el-button @click="linkData">链路</el-button>
          <el-button @click="deleteRow">删除</el-button>
          <el-button type="danger" @click="clearData">清空</el-button>
          <el-button type="success" @click="saveData">保存</el-button>
          <el-button @click="exportData">导出</el-button>
        </div>

        <!-- 数据表格 -->
        <el-table :data="calibrationData" style="width: 100%" size="small">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="序号" width="80" />
          <el-table-column prop="startFreq" label="起始频率(MHz)" />
          <el-table-column prop="stopFreq" label="截止频率(MHz)" />
          <el-table-column prop="bandwidth" label="带宽(MHz)" />
          <el-table-column prop="workMode" label="工作模式" />
          <el-table-column prop="calibrationFactor" label="校准因子(dBm)" />
          <el-table-column label="操作" width="100">
            <template #default>
              <el-button type="danger" size="small" @click="deleteSelected">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-model:current-page="pagination.current"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          layout="prev, pager, next"
          style="margin-top: 20px; text-align: center"
        />
      </el-card>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .level-calibration {
    display: flex;
    height: 100%;
    font-family: 'Microsoft YaHei', sans-serif;
  }

  .left-panel {
    width: 400px;
    padding: 20px;
    overflow-y: auto;
  }

  .right-panel {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .device-section {
    margin-bottom: 20px;

    .section-label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
    }
  }

  .manual-calibration,
  .auto-calibration {
    margin-bottom: 20px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
    }
  }

  .auto-buttons {
    display: flex;
    gap: 10px;
    margin: 15px 0;
  }

  .freq-range {
    margin-bottom: 15px;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }
</style>
