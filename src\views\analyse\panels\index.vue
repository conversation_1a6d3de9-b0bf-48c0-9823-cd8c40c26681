<template>
  <el-drawer title="设置" width="300">
    <modulePanel v-if="modulePanel" :form="form" />
  </el-drawer>
</template>

<script setup name="panels">
  const modulePanel = ref(null)
  const route = useRoute()

  watchEffect(() => {
    const routePath = route.path
    if (routePath.includes('zeroSpan')) {
      modulePanel.value = defineAsyncComponent(() => import('./zeroSpan/index.vue'))
    } else if (routePath.includes('digitalModulation')) {
      modulePanel.value = defineAsyncComponent(() => import('./digitalModulation/index.vue'))
    } else if (routePath.includes('harmonic')) {
      modulePanel.value = defineAsyncComponent(() => import('./harmonic/index.vue'))
    }
  })
</script>
