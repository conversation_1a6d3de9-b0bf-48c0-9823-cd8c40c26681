<template>
  <div class="app-container">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      @selectionChange="rows => (selection = rows)"
    />
    <DetailInfo ref="refsMt" @actSuccess="getList()" />
    <ApprovalInfo ref="refsApproval" @actSuccess="getList()" />
    <ProcessDetailInfo ref="refsProcess" @actSuccess="getList()" />
    <FileListPreview ref="refsFile" />
    <ExperStu ref="refStu" />
  </div>
</template>
<script>
  import * as mtApi from '@/api/business/experiment'
  import DetailInfo from './detail.vue'
  import ProcessDetailInfo from '../process/detail.vue'
  import ApprovalInfo from './approval.vue'
  import ExperStu from './stu.vue'
  import { ElProgress } from 'element-plus'
  export default {
    name: 'ExperimentList',
    components: { DetailInfo, ProcessDetailInfo, ExperStu, ApprovalInfo },
    props: {},
    data() {
      return {
        selection: [],
        tableFormConfig: {
          title: '实验项目管理',
          searchConfig: {
            formConfig: [
              { title: '项目名称', name: 'expName' },
              { title: '项目负责人', name: 'expChargeName' },
              { title: '申请人', name: 'expApplyName' },
              {
                title: '申请时间',
                name: 'expApplyTime',
                type: 'daterange',
                childName: ['start', 'end']
              },
              {
                title: '审核状态',
                name: 'expAduitStatus',
                type: 'dict',
                dictName: 'exp_aduit_status'
              },
              {
                title: '项目状态',
                name: 'expStatus',
                type: 'dict',
                multi: true,
                dictName: 'exp_status'
              }
            ],
            loadData: p => {
              p.expStatus && (p.expStatus = p.expStatus.join())
              return mtApi.listExperiment(p)
            }
          },
          actions: [
            {
              label: '申请',
              type: 'primary',
              icon: 'el_Plus',
              permission: ['business:experiment:add'],
              click: () => {
                this.$refs.refsMt.show()
              }
            },
            {
              label: '导出',
              icon: 'el_upload',
              type:'warning',
              permission: ['business:experiment:export'],
              click: row =>
                this.download(
                  'business/experiment/export',
                  {
                    // ...queryParams.value
                  },
                  `实验项目管理_${new Date().getTime()}.xlsx`
                )
            },
            {
              type: 'danger',
              label: '删除',
              permission: ['business:experiment:remove'],
              disabled: () => !this.selection.length,
              icon: 'el_Delete',
              click: row => this.deleteAll()
            }
          ],
          tableConfig: {
            isChecked: true,
            cols: [
              { label: '项目名称', prop: 'expName', minWidth: 200 },
              { label: '实验编码', prop: 'expCode', minWidth: 200 },
              {
                label: '实验进度',
                prop: 'expProcess',
                renderFun: (h, { row }) => {
                  return h(ElProgress, {
                    percentage: row.expProcess * 1 || 0,
                    textInside: true,
                    strokeWidth: 20
                  })
                }
              },
              { label: '申请经费(万元)', prop: 'expPlanCost' },
              { label: '负责人', prop: 'expChargeName' },
              { label: '申请人', prop: 'expApplyName' },
              { label: '申请时间', prop: 'expApplyTime' },
              { label: '实验项目状态', prop: 'expStatus', type: 'dict', dictName: 'exp_status' },
              { label: '审核时间', prop: 'expAuditTime' },
              {
                label: '审核状态',
                prop: 'expAduitStatus',
                type: 'dict',
                fixed: 'right',
                dictName: 'exp_aduit_status'
              },
              {
                label: '操作',
                type: 'action',
                width: '200',
                actions: [
                  {
                    title: '审核',
                    icon: 'icons5_DocumentAttachSharp',
                    permission: ['business:experiment:approval'],
                    click: row => this.$refs.refsApproval.show(row.expId)
                  },
                  {
                    title: '编辑',
                    icon: 'el_Edit',
                    permission: ['business:experiment:edit'],
                    click: row => this.$refs.refsMt.show(row.expId)
                  },
                  {
                    title: '实验学生',
                    icon: 'el_User',
                    disabled: row => row.expAduitStatus == '1',
                    permission: ['business:process:add'],
                    click: row => this.$refs.refStu.show(row.expId)
                  },
                  {
                    title: '添加进度',
                    icon: 'el_Operation',
                    permission: ['business:process:add'],
                    disabled: row => row.expAduitStatus == '1' && row.expStatus != '2',
                    click: row => this.$refs.refsProcess.show('', row.expId, row.expProcess * 1)
                  },
                  {
                    title: '实验成果',
                    icon: 'el_TakeawayBox',
                    permission: ['business:experiment:files'],
                    disabled: row => row.expProcess * 1 > 0,
                    click: row =>
                      mtApi.fileExperiment(row.expId).then(res => {
                        this.$refs.refsFile.show(res.data)
                      })
                  },
                  {
                    title: '删除',
                    permission: ['business:experiment:remove'],
                    icon: 'el_Delete',
                    click: row => this.deleteAll(row.expId)
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList(data = null) {
        this.$refs.refsTableForm.loadData(data)
      },
      //批量删除模板
      deleteAll(id = null) {
        if (!this.selection.length && !id) return this.$modal.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选实验项目吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.expId)
          mtApi.delExperiment(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.getList()
          })
        })
      }
    }
  }
</script>
