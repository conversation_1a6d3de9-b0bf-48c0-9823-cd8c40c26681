<template>
  <div style="width: 800px; padding-left: 16px">
    <!-- <el-row :gutter="16"> -->
    <el-row>
      <Constellation class="w-full" :data="iqData" />
    </el-row>
    <!-- <el-col :span="12" class="mt-4">
        <IqPlot :data="iqData" />
      </el-col> -->
    <el-col v-if="false" :span="12" class="mt-4">
      <Sequentail />
    </el-col>
    <!-- <el-col class="mt-4"> -->
    <el-row class="mt-4 w-full">
      <Eye :data="eyeData" />
    </el-row>
    <el-row class="mt-4 w-full" v-if="audioWaveform.length > 0">
      <FmPlot :data="audioWaveform" />
      <AudioPlay :audio-data="audioWaveform" />
      <!-- <wave-surfer-player :audioData="audioWaveform"></wave-surfer-player> -->
    </el-row>
    <!-- </el-row> -->
  </div>
</template>

<script setup name="CpeChart">
  import Constellation from './Constellation'
  import FmPlot from './Fm'
  // import IqPlot from './IqPlot'
  import Sequentail from './Sequential'
  import Eye from './Eye'
  import AudioPlay from './AudioPlay/index.vue'
  // import WaveSurferPlayer from './AudioPlay/demo.vue'

  const props = defineProps({
    iqData: {
      type: Object,
      default: () => ({})
    },
    audioWaveform: {
      type: Array,
      default: () => []
    }
  })

  const eyeData = computed(() => {
    const result = {
      pdIBase: props.iqData.i,
      pdQBase: props.iqData.q,
      eyestep: props.iqData.eyeStep || 4
    }
    return result
  })
</script>
