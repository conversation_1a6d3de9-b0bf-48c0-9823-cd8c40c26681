<template>
  <el-dialog
    :key="dia"
    v-model="dia"
    width="800px"
    append-to-body
    :title="id ? '课程信息' : '新增课程'"
  >
    <c-form
      v-bind="formConfig"
      :key="formCourse.courId + '' + dia"
      ref="refsForm"
      v-model="formCourse"
      v-loading="loading"
      class="mr-4"
    >
      <!-- <template #courPublish="{ form }">{{ form.courPublish }}</template> -->
    </c-form>

    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button type="primary" @click="sub">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
  import { getCourse, addCourse, updateCourse, searchBook } from '@/api/business/course'
  export default {
    emits: ['actSuccess', 'close'],
    data () {
      return {
        dia: false,
        loading: false,
        id: '',
        oldBookId: '',
        boIds: '',
        formCourse: {},
        formConfig: {
          splice: 2,
          labelWidth: '8em',
          layout: 'block',
          autoEmit: false,
          hideBtn: true,
          value: {},
          formInfo: [
            {
              title: '课程编码',
              name: 'courCode',
              rules: { required: true, message: '请输入课程编码' }
            },
            {
              title: '课程名称',
              name: 'courName',
              rules: { required: true, message: '请输入课程名称' }
            },
            {
              title: '教材名称',
              name: 'courBook',
              type: 'cuselect',
              queryInfo: '/business/book/selectBook?pageNum=1&pageSize=100',
              replaceFields: { label: 'BO_NAME', value: 'BO_ID' },
              // rules: { message: '请选择教材名称', required: true },
              callback: res => {
                searchBook(res.value).then(result => {
                  let { boName, boPublish, boWriter, boYear, boVersion, boId } = result.data
                  this.formCourse.courBook = boName
                  // this.formCourse.courPublish = boPublish
                  // this.formCourse.courWriter = boWriter
                  // this.formCourse.courYear = boYear
                  // this.formCourse.courVersion = boVersion
                  // console.log('form', this.formCourse)
                  this.$refs.refsForm.setFormParams({
                    courVersion: boVersion,
                    courBook: boName,
                    courPublish: boPublish,
                    courYear: boYear,
                    courWriter: boWriter
                  })
                  this.boIds = boId
                })
              }
            },
            // {
            //   title: '课程教材',
            //   name: 'courBook',
            //   rules: { required: true, message: '请输入课程教材' }
            // },
            {
              title: '教材出版社',
              name: 'courPublish',
              readonly: true
            },
            { title: '教材编写人', name: 'courWriter', readonly: true },
            { title: '教材年份', name: 'courYear', readonly: true },
            { title: '教材版本', name: 'courVersion', readonly: true },
            {
              title: '课程授课老师',
              name: 'teaIds',
              type: 'cuselect',
              queryInfo: 'business/teacher/list?pageNum=1&pageSize=100',
              multi: true,
              replaceFields: { label: 'teaName', value: 'teaId' },
              rules: { message: '请选择课程授课老师', required: true }
            },
            { title: '课程简介', isFull: true, name: 'courBiz', type: 'textarea' }
          ]
        }
      }
    },

    methods: {
      show (id, oldBooks) {
        this.id = id
        this.dia = true
        this.oldBookId = oldBooks
        this.formCourse = { courId: '' }
        this.formCourse.oldBook = oldBooks
        id && this.getInfo(id)
      },
      getInfo (id) {
        this.loading = true
        getCourse(id)
          .then(res => {
            res.data.teaIds = res.data.srCourseTeacherList.map(v => v.teaId)
            this.formCourse = res.data
          })
          .finally(() => {
            this.loading = false
          })
      },
      sub () {
        this.$refs.refsForm.subForm(form => {
          let act = this.formCourse.courId ? updateCourse : addCourse
          form.srCourseTeacherList = form.teaIds.map(v => {
            return { courseId: this.formCourse.courId, teaId: v }
          })
          form.srBook = {
            boId: this.boIds,
            boName: form.courBook,
            boPublish: form.courPublish,
            boWriter: form.courWriter,
            boYear: form.courYear,
            boVersion: form.courVersion
          }
          form.oldBook = this.oldBookId
          act(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
        })
      }
    }
  }
</script>
<style></style>
