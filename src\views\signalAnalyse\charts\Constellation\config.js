export default {
  chart: {
    backgroundColor: '#020e1f', // 设置图表的背景颜色为深蓝色
    type: 'line', // 图表类型为折线图
    height: 400, // 图表高度为360像素
    spacingTop: 10, // 图表上方的间距
    spacingBottom: 20, // 图表下方的间距
    spacingLeft: 10, // 图表左侧的间距
    spacingRight: 20, // 图表右侧的间距
    events: {} // 图表事件，目前为空
  },
  accessibility: {
    enabled: false // 禁用无障碍功能
  },
  exporting: {
    enabled: false // 禁用导出功能
  },
  reflow: true, // 当窗口大小变化时，图表将自动调整大小
  credits: {
    enabled: false // 禁用图表的版权信息显示
  },
  xAxis: {
    labels: {
      style: {
        color: '#f3f3f3' // x轴标签的颜色为浅灰色
      }
    },
    tickAmount: 5, // x轴上显示的刻度数量
    gridLineWidth: 1, // x轴网格线的宽度
    gridLineColor: '#f3f3f3', // x轴网格线的颜色为浅灰色
    gridLineDashStyle: 'dash', // x轴网格线的样式为虚线
    lineColor: '#f3f3f3', // x轴线条的颜色为浅灰色
    zoomEnabled: false, // 禁用缩放功能
    min: -1, // x轴的最小值
    max: 1 // x轴的最大值
  },
  yAxis: {
    title: {
      enabled: false // 禁用y轴标题
    },
    min: -1, // y轴的最小值
    max: 1, // y轴的最大值
    gridLineColor: '#f3f3f3', // y轴网格线的颜色为浅灰色
    gridLineDashStyle: 'dash', // y轴网格线的样式为虚线
    lineColor: '#f3f3f3', // y轴线条的颜色为浅灰色
    tickAmount: 5, // y轴上显示的刻度数量
    labels: {
      style: {
        color: '#f3f3f3' // y轴标签的颜色为浅灰色
      }
    }
  },
  title: {
    text: '星座图', // 图表的标题为“星座图”
    margin: 8, // 标题与图表顶部的间距
    style: {
      color: '#f3f3f3', // 标题的颜色为浅灰色
      fontSize: 16, // 标题的字体大小为16像素
      fontWeight: 700 // 标题的字体粗细为700
    }
  },
  boost: {
    enabled: true,
    useGPUTranslations: true,
    usePreAllocated: true,
    seriesThreshold: 1
  },
  legend: {
    enabled: false // 禁用图例显示
  },
  series: [
    {
      type: 'scatter', // 数据系列类型为散点图
      enableMouseTracking: false, // 禁用鼠标追踪功能
      animation: false, // 禁用动画效果
      marker: {
        enabled: true, // 启用数据点标记
        fillColor: '#1E71FF', // 数据点标记的填充颜色为蓝色
        radius: 2 // 数据点标记的半径为2像素
      },
      data: [] // 数据数组，目前为空
    }
  ],
  plotOptions: {
    series: {
      turboThreshold: 500, // 启用 Turbo 模式，当数据量大于 1000 时启用
      boostThreshold: 1,
      marker: { enabled: false },
      animation: false,
      enableMouseTracking: false, // 如果不需要交互，禁用鼠标跟踪
      states: {
        hover: {
          enabled: false
        }
      }
    },
  },
}
