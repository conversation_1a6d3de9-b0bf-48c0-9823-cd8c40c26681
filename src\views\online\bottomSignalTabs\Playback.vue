<template>
  <div class="container max-h-[270px] h-[270px] px-2 py-1">
    <el-row>
      <el-col :span="12">
        <div class="play-tool">
          <!-- 添加播放文件按钮 -->
          <el-button style="margin-left: 0; padding: 6px 0" @click="addPlayFile">
            <x-icon icon="CirclePlus" class="px-1" source="el" size="20" />
          </el-button>
          <!-- 播放 -->
          <el-button @click="togglePlay">
            <x-icon icon="CaretRight" source="el" size="24" :disabled="isDisabled" />
          </el-button>
          <!-- 停止按钮 -->
          <el-button @click="stop">
            <x-icon icon="stop" size="24" source="cus" :disabled="isDisabled" />
          </el-button>
          <!-- 暂停按钮 -->
          <el-button @click="pause">
            <x-icon icon="pause" size="24" source="cus" :disabled="isDisabled" />
          </el-button>
          <!-- 快退按钮 -->
          <el-button @click="fastback">
            <x-icon icon="fastback" size="24" source="cus" :disabled="isDisabled" />
          </el-button>
          <!-- 后退按钮 -->
          <el-button @click="back">
            <x-icon icon="back" size="24" source="cus" :disabled="isDisabled" />
          </el-button>
          <!-- 前进按钮 -->
          <el-button @click="forward">
            <x-icon icon="forward" size="24" source="cus" :disabled="isDisabled" />
          </el-button>
          <!-- 刷新按钮 -->
          <el-button @click="refresh">
            <x-icon icon="Refresh" size="24" source="el" />
          </el-button>
          <!-- 当前播放文件信息 -->
          <el-tooltip :content="file.name" placement="top">
            <span class="current-play">{{ statusText }} {{ file.name }}</span>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <!-- 进度条组件 -->
    <Slider
      v-model="startOffset"
      class="my-5"
      :min="0"
      :max="maxOffset"
      :view-num="viewNum"
      :is-drag="!isDisabled"
      @update:modelValue="updateStartOffset"
      @change:startOffset="handleStartOffsetChange"
    />
    <!-- 信息显示 -->
    <div class="info">
      <div class="info-item" style="padding-top: 0"> 回放进度: {{ sweep }} </div>
      <div class="info-item"> 日期/时间: {{ file.dateTime }} </div>
      <div class="info-item">
        <label style="font-weight: 200">回放速度</label>
        <el-input v-model="time" class="common-input ml-3">
          <template #suffix> ms </template>
        </el-input>
      </div>
    </div>
    <!-- 文件列表对话框 -->
    <el-dialog v-model="fileListVisible" title="文件列表" class="el-tabs">
      <el-table v-loading="loading" :data="fileList">
        <el-table-column property="fileName" label="文件名" />
        <el-table-column property="uploadTime" label="采集时间">
          <template #default="{ row }">
            {{ formatUploadTime(row.uploadTime) }}
          </template>
        </el-table-column>
        <el-table-column property="op" label="操作" width="200">
          <template #default="{ row }">
            <el-button @click="playfile(row)">播放</el-button>
            <el-button @click="downfile(row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getFileList"
      />
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, defineProps, nextTick } from 'vue'
  import { ElMessage } from 'element-plus'
  import dayjs from 'dayjs'
  import Slider from '@/components/Slider/index.vue'
  import useScanStore from '@/store/modules/scanMonitor'
  import { allSignalListGet } from '@/api/singalManage'
  import { getSignalSort } from '@/api/charts'

  // 引入 store 模块
  const scanStore = useScanStore()
  const spectrum = scanStore.spectrumCopy

  // 定义状态变量
  const currentFileFfts = ref([])
  const signalList = ref([])
  const signalFile = ref(null)
  const startOffset = ref(0)
  const viewNum = ref(0)
  const maxOffset = ref(0)
  const time = ref(64)
  const interval = ref(null)
  const fileListVisible = ref(false)
  const fileList = ref([])
  const loading = ref(false)
  const file = reactive({
    name: '',
    dateTime: ''
  })
  const isRequestInProgress = ref(false)
  const isFetching = ref(false) // 请求锁
  const isFinished = ref(false)
  const isPlaying = ref(false)

  const props = defineProps({
    reloadCharts: {
      type: Function,
      default: null
    }
  })

  const freBandRef = inject('freBandRef')

  // 计算扫描进度
  const sweep = computed(() => {
    if (maxOffset.value === 0) return '0%'
    if (maxOffset.value === 1) return '100%'
    const percent = (startOffset.value / maxOffset.value) * 100
    return `${percent.toFixed(2)}%`
  })

  const isDisabled = computed(() => {
    return maxOffset.value === 1
  })

  // 计算播放状态文本
  const statusText = computed(() => {
    console.log('isPlaying', isPlaying.value)
    if (isPlaying.value && maxOffset.value > 0) {
      return '播放中'
    } else if (
      (startOffset.value === maxOffset.value && !isPlaying.value && maxOffset.value > 0) ||
      maxOffset.value === 1
    ) {
      return '播放完毕'
    }
    return '等待播放'
  })

  const queryParams = ref({
    pageNum: 1,
    pageSize: 10
  })
  const total = ref(0)

  // 添加播放文件
  const addPlayFile = () => {
    if (scanStore.spectrumCopy.status === 1) {
      ElMessage.warning('请先停止频谱扫描再播放')
      return
    }
    getFileList()
    fileListVisible.value = true
  }

  // 播放文件
  const playfile = async row => {
    spectrum.centerFreq = row.centerFreqIn
    spectrum.bandwidth = row.intermediateFrequencyBandwidth
    freBandRef.value.dataChangeFun()
    signalFile.value = row
    loading.value = true
    file.dateTime = formatUploadTime(row.uploadTime)
    file.name = row.fileName
    startOffset.value = 0
    maxOffset.value = 0
    scanStore.setPlayFfts([])
    scanStore.playSignalList = []
    await play()
    loading.value = false
    fileListVisible.value = false
  }

  // 格式化上传时间
  const formatUploadTime = uploadTime => {
    return dayjs(uploadTime, 'YYYYMMDDHHmmss').format('YYYY/MM/DD HH:mm:ss')
  }

  const fetchData = async offset => {
    if (isFetching.value) return // 如果正在请求中，则直接返回
    isFetching.value = true

    if (maxOffset.value === 1) {
      isPlaying.value = false
      isFinished.value = true
      clearInterval(interval.value)
      isFetching.value = false
      return
    }

    try {
      if (maxOffset.value - offset <= 1 && maxOffset.value > 1) {
        const result = await getSignalSort({ ...signalFile.value, startOffset: offset })
        startOffset.value = result.data.maxOffset
        currentFileFfts.value = result.data.pSpecData_Out
        signalList.value = result.data.pSIG_DATA_Out
        clearInterval(interval.value)
        interval.value = null
        isRequestInProgress.value = false
        isFinished.value = true
        isPlaying.value = false
        isFetching.value = false
        await nextTick()
        return
      }

      const result = await getSignalSort({ ...signalFile.value, startOffset: offset })
      startOffset.value = result.data.startOffset
      readRowInfo(result.data)
      generatePlayFfts()
    } catch (error) {
      console.error('Error fetching data:', error)
      clearInterval(interval.value)
      interval.value = null
      isPlaying.value = false
    } finally {
      isRequestInProgress.value = false
      isFetching.value = false
    }
  }

  // const play = async () => {
  //   if (!file.name) {
  //     ElMessage.warning('请先添加播放文件')
  //     return
  //   }
  //   if (isPlaying.value) {
  //     ElMessage.warning('正在播放中')
  //     return
  //   }
  //   if (startOffset.value === maxOffset.value) {
  //     startOffset.value = 0
  //   }
  //   isPlaying.value = true
  //   console.log(isRequestInProgress.value)
  //   console.log(isFetching.value)
  //   interval.value = setInterval(async () => {
  //     if (!isRequestInProgress.value && !isFetching.value) {
  //       console.log('Playing', startOffset.value, maxOffset.value)
  //       try {
  //         await fetchData(startOffset.value)
  //       } catch (error) {
  //         clearInterval(interval.value)
  //       }

  //       console.log('Played', startOffset.value, maxOffset.value)
  //     }
  //   }, time.value)
  // }

  // 切换播放状态
  const play = async () => {
    if (!file.name) {
      ElMessage.warning('请先添加播放文件')
      return
    }
    if (isPlaying.value) {
      ElMessage.warning('正在播放中')
      return
    }
    if (startOffset.value === maxOffset.value) {
      startOffset.value = 0
    }
    isPlaying.value = true

    interval.value = setInterval(async () => {
      if (!isRequestInProgress.value && !isFetching.value) {
        try {
          await fetchData(startOffset.value)
        } catch (error) {
          console.error('Error during playback:', error)
          clearInterval(interval.value)
          interval.value = null
          isPlaying.value = false
        }
      }
    }, time.value)
  }

  const togglePlay = () => {
    if (isPlaying.value) {
      ElMessage.warning('正在播放中')
    } else {
      if (maxOffset.value === startOffset.value) {
        startOffset.value = 0
        maxOffset.value = 0
      }
      play()
    }
  }

  // 读取行信息
  const readRowInfo = data => {
    maxOffset.value = data.maxOffset
    startOffset.value = data.startOffset
    currentFileFfts.value = data.pSpecData_Out
    signalList.value = data.pSIG_DATA_Out
  }

  // 生成播放频谱数据
  const generatePlayFfts = () => {
    scanStore.setPlayFfts(currentFileFfts.value)
    scanStore.playSignalList = signalList.value
  }

  // 暂停
  const pause = () => {
    if (!isPlaying.value) {
      ElMessage.warning('已暂停')
      return
    }
    if (interval.value) {
      clearInterval(interval.value)
      interval.value = null
    }
    isPlaying.value = false
  }

  // 停止
  const stop = () => {
    if (interval.value) {
      clearInterval(interval.value)
      interval.value = null
    }
    isPlaying.value = false
    startOffset.value = 0
    maxOffset.value = 0
    isFinished.value = false
  }

  // 快退
  const fastback = () => {
    if (!file.name) {
      ElMessage.warning('请先添加播放文件')
      return
    }
    if (interval.value) {
      clearInterval(interval.value)
      interval.value = null
    }
    isPlaying.value = false
    startOffset.value = 0
    isFinished.value = false
  }

  // 后退
  const back = async () => {
    if (!file.name) {
      ElMessage.warning('请先添加播放文件')
      return
    }
    // if (isFinished.value) {
    //   ElMessage.warning('已播放完毕')
    //   return
    // }
    if (interval.value) {
      clearInterval(interval.value)
      interval.value = null
    }
    isPlaying.value = false
    const calNum = startOffset.value - 2 < 0 ? 0 : startOffset.value - 2
    if (startOffset.value === maxOffset.value) {
      startOffset.value -= 2
    } else {
      startOffset.value - 1 < 0 ? 0 : (startOffset.value -= 1)
    }

    if (startOffset.value === 0 || maxOffset.value - startOffset.value === 1) {
      return
    }

    console.log(calNum)
    await fetchData(calNum)
  }

  // 前进
  const forward = async () => {
    if (!file.name) {
      ElMessage.warning('请先添加播放文件')
      return
    }
    if (isFinished.value && startOffset.value === maxOffset.value) {
      ElMessage.warning('已播放完毕')
      return
    }
    if (interval.value) {
      clearInterval(interval.value)
      interval.value = null
    }
    isPlaying.value = false
    await fetchData(startOffset.value)
  }

  // 刷新
  const refresh = () => {
    if (interval.value) {
      clearInterval(interval.value)
      interval.value = null
    }
    isPlaying.value = false
    startOffset.value = 0
    if (maxOffset.value === 1) {
      maxOffset.value = 0
    }
    isFinished.value = false
    play()
  }

  // 获取文件列表
  const getFileList = () => {
    allSignalListGet(queryParams.value).then(rsp => {
      fileList.value = rsp.data.rows
      total.value = rsp.data.total || 0
    })
  }

  // 更新开始偏移量
  const updateStartOffset = value => {
    startOffset.value = value
  }

  // 处理开始偏移量变化
  const handleStartOffsetChange = value => {
    console.log('StartOffset changed:', value)
    play()
  }
</script>

<style scoped lang="scss">
  .container {
    background-color: #222;
    color: var(--scan-play-color);
    font-weight: 100;
    width: 100%;

    .container-header {
      display: flex;
      justify-content: center;
      cursor: pointer;
    }

    .play-tool {
      margin: 2px;

      .el-button {
        padding: 4px 0;
        margin: 0;
        border-radius: 0;
        border: var(--play-bar-slider-border);
        background-color: var(--play-bar-slider);
        margin: 2px;
        width: auto;
        height: auto;

        &:hover {
          background-color: #7c4918;
        }

        .x-icon {
          color: var(--scan-text-color);
        }
      }
    }

    .info {
      .info-item {
        padding: 8px 0;
      }
    }
  }
</style>
