﻿# 通用表单组件

```js
<c-form splice="2" :labelWitdh="labelWitdh" :key="JSON.stringify(formInfo)" :formInfo="formInfo" @sub="subForm" @change="changeForm" hideBtn ref="form" :rules="{name:[{required:true}]}">
  <template #slotA>
  个性化显示内容
  </template>

  <template #slotB="scope">
  个性化显示组件带作用域
  {{scope.form}}//可获取到表单对象内容
  <a-input v-model="slotInput">
  <a-input v-model="scope.form.testAttr"/>
  </template>
</c-form>
//转换软属性数组
import { parseAttr } from '@/utils/parseAttr'
{
  data(){
    // 自定义表单字段校验方法
    const codeValid = async (r, v, c) => {
      if (!v) return c(new Error('请输入部件编码'))
      await partInfoApi.eqPartsNumberApi({code: v }).then(res => {
        c()
      }).catch(err => {
        c(err)
      })
    }
    return {
      labelWitdh:6,//'130px'
      formInfo:[],
      rules:{
        name:[{required:true,message:'请输入名称'}],
        code: [{ validator: codeValid, trigger: 'change' }, { required: true, message: '请选择部件编码' }]
      },
      form:{},
      slotInput:''
    }
  },
  method:{
    parseForm(moreAttr){
      let data=this.form
      const arr = [
         // 普通文字 noAttr:true代表字段不返回表单只做展示
        {title:'产品',name:'productName',value:'产品名啊',type:'info',noAttr:true},
        // 普通文本
        {title:'编码',name:'code',value:data.code},
        // 字典下拉选择
        {title:'性别',type:'dict',name:'sex',value:'1',dictName:'sex',rules:[{required:true}]},
        // 位置
        { title: '位置', value: data.pathId || this.folderId,path: data.path, name: 'pathId', type: 'pathArea', folderId: data.pathId || this.folderId, containerId: data.productId},
        // 下拉选择
        { title:'国家',type:'select',name:'location',value:data.location,child:[{label:'美',value:'1'},{label:'中',value:'0'}]
        },
        // 文本类型
        { title: '备注', name: 'remark', value: data.remark || '', type: 'textarea', maxLen: 200 },
        // 自定义插槽
        {title:'',name:'',type:'slot',slotName:'slotA',isFull:true},
         {title:'个性组件',name:'',type:'slot',slotName:'slotB'},
      ]
      this.formInfo=[...arr,parseAttr(moreAttr)]
    },
    changeForm(form){
      console.log('表单内容发生改动',form)
    },
    // 如果显示表单提交按钮并点击后广播出sub事件返回表单内容
    subForm(form){
      console.log('表单触发了提交',form)
    },
    // 主动获取表单内容
    getFormInfo(){
      // 回调方式
      this.$refs.form.subForm(res=>{
        console.log('主动获取的表单内容',form)
      })
      // promise方式
      this.$refs.form.subForm().then(res=>{
        console.log('主动获取的表单内容',form)
      }).catch(_=>{
        console.log('表单验证失败')
      })
    }
  }
}
```

## form attr

| 属性 | 必选 | 值类型 | 描述 | 默认 | 可选 |
| --- | --- | --- | --- | --- | --- |
| formInfo | true | array | 表单参数列表 | [] | -- |
| splice | false | string | 每行显表单内容个数，默认显示 3 个，弹窗中表单请输入 2 | 3 | 1,2,3 |
| labelWidth | false | string,number | label 宽度可输入具体长度'160px'或输入数字，4 代表 4em,4 个字符宽度 | 120px |  |
| autoEmit | false | boolean | 是否初始化后就返回 form 内容 | true | - |
| hideBtn | false | boolean | 是否隐藏按钮 | false | - |
| rules | false | array | 表单验证 rule | [] | - |

## item attr

| 属性 | 必选 | 值类型 | 描述 | 默认 | 可选 |
| --- | --- | --- | --- | --- | --- |
| title | false | string | 表单标题 | - | -- |
| name | false | string | 表单 name | - | -- |
| value | false | string | 表单值 | - | -- |
| noAttr | false | false | 字段只用于展示，不返回到最终 form | false | true |
| type | false | string | 表单类型 | input | ['input','number','textarea','hidden','select','radio','date','time','range','rangedate','rangedatetime','rangNum','checkbox','selectDict','tree-select','selectPerson','file','cuselect','slot'] |

```js
[{
  title:'名称',//表单行标题
  name:'productName',//表单属性名
  type:'select',//表单类型：
  value:data.productName,//表单属性默认值,区间或多选的为[value1,value2]
  child:[{
    label:'子选项名',
    value:'子选项值'
  }],// type为select,radio,checkbox时传入 作为选择项
  noAttr:false,//字段只用于展示，不返回到最终form
  placeholder:'表单placeholder',//默认使用label加类型组合显示，默认无需传，有定制化需要再传
  noClear:false,//是否允许删除，下拉框不允许删除时设置true
  rules:[{name:[{required:true,message:'请输入名称'}]}],//表单字段校验，
  isFull:true,//是否单独显示一行
  multi:false,//多选，一些下拉选择或自定义组件支持
  hide:false,//是否隐藏，true时隐藏 表单显示·更多条件·按钮，点击后显示该表单行
  childName:['attr1','attr2']//type为range时间区间或rangNum数字区间时传入，对应前后2个属性名，对应值为value[0],value[1],
  callback:(res)=>{
    //一些组件值变更回调
  },
  click:res=>{
    // textClick类型下输入框右侧按钮点击回调
    console.log('')
  }
}]
```

### 个性化组件类型传值

1. 字典下拉选择

   ```js
   {
     title:'性别',
     name:'sex',
     isText:false,
     type:'dict',
     dictValue:data.sex,
     multi:false,//是否多选
     dictName:'sex'// 字典名
   }
   ```

2. tree-select

   > 下拉选择树,tree-data:下拉数据树结构，接口返回一维数组可用 parseTree 方法转换成树结构，默认为树结构的 可穿 replaceFields 匹配树与数据对应关系

3. selectPerson|person-select|select-person

   > 选人组件，selectIds：已有人员 id 数组，selectName:已有人员 name 数组,选择侯返回人员 id 数组

4. file

   > 文件上传 传入已有文件列表 返回最新文件列表

5. 路径 path|pathArea|path-area
   ```js

   ```

{ title:'位置', type:'path'||'pathArea',//有上下文选择的时候为 pathArea value:data.pathId, name:'pathId', path:data.path,//路径显示名，非必传，传值时则不用根据 pathid 再去查询对应的显示值 containerId:data.containerId,//所在产品或存储库 id folderId:data.folderId//文件夹 id,一般同 pathid 相同 }

````

6. cuselect
 >自带查询地址的选择下拉框，queryInfo[object|string]:查询地址信息，可输入请求地址，复杂请求用对象格式传递
 >
```js
 queryInfo：{url:'/查询地址',method:'post',data:{},params:{}}||'/plm-web/search'//查询地址url或请求对象
 queryFun:()=>{return Promise()},// 传递查询接口的promise,promise返回为结果
 replaceFields:{label:'title',value:'name'},//组件默认显示键值对为label:value，如果接口返回的键值对是其他的 需传递此字段进行转换
 type:'cuselect'// 传递type:'text',显示样式为文本，不是下拉框,
 callback:({value,obj})=>{
   //切换选中回调， value,选中值，obj,选中对象
 }
````

7. slot 自定义插槽
   > 可自定义单字段内容为插槽
   ```js

   ```

{ title:'',//根据实际情况是否需要显示标题 isFull:true,//需要单独显示一行可传入 true, type:'slot',// slotName:'slotA',//插槽名 }

````

8. file文件上传
> 可自定义单字段内容为插槽
```js

{
 title:'文件',//根据实际情况是否需要显示标题
 type:'file',//
 maxSize:100,//最大上次文件大小,默认50 单位M
 maxSelect:1,//最大支持选择文件数,默认100个,
 multi:false,//是否支持多选
 beforeUpload:beforeUploadFun,
}
<!-- 上传前校验自定义函数 传入file,返回boolean或Promise -->
beforeUploadFun(file){
 if(file.name.split('.').pop()!='jpg'){
   this.$message.warn('不支持上传非jpg格式文件')
   return false
 }else{
   return new Promise((res,rej)=>{
     if(true){
        res()
     }else{
        rej('失败不上传')
     }
   })
 }
}
````

## method

| 方法名  | 描述                                                    | 传参 | 返回          |
| ------- | ------------------------------------------------------- | ---- | ------------- |
| getForm | 获取表单内容                                            | null | Object[form]  |
| subForm | 提交验证表单并触发 emit sub 方法,父组件获得对应表单内容 | null | Promise[form] |
