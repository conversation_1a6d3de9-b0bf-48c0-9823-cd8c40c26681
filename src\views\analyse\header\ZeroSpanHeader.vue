<template>
  <el-tooltip content="模块" placement="top" effect="light">
    <el-dropdown trigger="click" class="ml-4">
      <x-icon icon="fold" size="20" source="el" />
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="item in viewItems"
            :key="item.value"
            @click="renderView(item)"
          >
            <div style="width: 24px">
              <x-icon v-show="isInView(item.value)" icon="check" source="el" />
            </div>
            {{ item.title }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </el-tooltip>
</template>

<script setup>
  import useChartsStore from '@/store/modules/charts'
  const chartsStore = useChartsStore()

  const viewItems = [
    { title: '时域总览', value: 'Squential' },
    { title: '幅度 vs 时间', value: 'AmVsTime' },
    { title: '幅度 vs 时间(线性)', value: 'AmLinearVsTime' },
    { title: 'FM vs 时间', value: 'PmVsTime' },
    // { title: 'FM vs 时间(弧度)', value: 4 },
    // { title: 'FM vs 时间(角度)', value: 5 },
    { title: 'IQ vs 时间', value: 'IqVsTime' },
    { title: '频谱绘制', value: 'Spectrum' },
    { title: 'CCDF', value: 'Ccdf' },
    // { title: 'IQ Polar', value: 9 },
    { title: '瀑布图', value: 'WaterFallPlot' },
    // { title: 'Channel Power', value: 11 }
  ]
  const compMap = {}
  const renderView = async item => {
    const { zeroSpanViews } = chartsStore
    if (zeroSpanViews.find(v => v.name === item.value)) {
      return
    }
    if (!compMap[item.value]) {
      switch (item.value) {
        case 'AmVsTime':
          compMap[item.value] = defineAsyncComponent(() => import('@/views/analyse/modules/AmVsTime'))
          break
        case 'Squential':
          compMap[item.value] = defineAsyncComponent(() => import('@/views/analyse/modules/Squential.vue'))
          break
        case 'Spectrum':
          compMap[item.value] = defineAsyncComponent(() => import('@/views/analyse/modules/Spectrum.vue'))
          break
        case 'AmLinearVsTime':
          compMap[item.value] = defineAsyncComponent(() => import('@/views/analyse/modules/AmLinearVsTime'))
          break
        case 'IqVsTime':
          compMap[item.value] = defineAsyncComponent(() => import('@/views/analyse/modules/IqVsTime'))
          break
        case 'Ccdf':
          compMap[item.value] = defineAsyncComponent(() => import('@/views/analyse/modules/Ccdf'))
          break
        case 'PmVsTime':
          compMap[item.value] = defineAsyncComponent(() => import('@/views/analyse/modules/PmVsTime'))
          break
        case 'WaterFallPlot':
          compMap[item.value] = defineAsyncComponent(() => import('@/views/analyse/modules/WaterFallPlot'))
          break
        default:
      }
      if (!compMap[item.value]) {
        
        return
      }
    }
    zeroSpanViews.push({
      name: item.value,
      comp: compMap[item.value]
    })
  }
  const isInView = (val) => {
    const item = chartsStore.zeroSpanViews.find(v => v.name === val)
    return !!item
  }
  onMounted(() => {
    if (chartsStore.zeroSpanViews.length === 0) {
      renderView({ value: 'AmVsTime' })
      renderView({ value: 'Spectrum' })
      renderView({ value: 'Ccdf' })
      renderView({ value: 'PmVsTime' })
    }
  })
  onBeforeUnmount(() => {
    chartsStore.zeroSpanViews = []
  })
</script>