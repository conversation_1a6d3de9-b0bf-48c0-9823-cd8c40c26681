<template>
  <el-dialog v-model="dia" width="960px" append-to-body :title="id ? '学生信息' : '新增'">
    <c-form
      v-bind="formConfig"
      :key="form.stuId + '' + dia"
      ref="refsForm"
      v-model="form"
      v-loading="loading"
      class="mr-4"
    />
    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button type="primary" @click="sub">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
  import pattern from '@/utils/pattern'
  import { getStudents, addStudents, updateStudents } from '@/api/business/students'
  export default {
    emits: ['actSuccess', 'close'],
    data() {
      return {
        dia: false,
        loading: false,
        id: '',
        form: {},
        formConfig: {
          splice: 2,
          labelWidth: '8em',
          layout: 'block',
          autoEmit: false,
          hideBtn: true,
          formInfo: [
            {
              title: '学生学号',
              name: 'stuCode',
              rules: { required: true, message: '请输入学号' }
            },
            { title: '姓名', name: 'stuName', rules: { required: true, message: '请输入姓名' } },
            {
              title: '学生性别',
              name: 'stuSex',
              required: true,
              type: 'dict',
              dictName: 'sys_user_sex',
              rules: { required: true, message: '请选择性别' }
            },

            { title: '学制', name: 'stuYear', type: 'dict', dictName: 'stu_year' },
            {
              title: '手机号码',
              name: 'stuPhone',
              maxLen: 11,
              rules: [{ pattern: pattern.TEL, message: '请输入11位手机号码' }]
            },
            { title: '籍贯', name: 'stuNative' ,  rules: [{ pattern: pattern.STRING, message: '请输入正确籍贯' }]},
            {
              title: '出生日期',
              name: 'stuBirth',
              type: 'date',
              // rules: { required: true, message: '请选择出生日期' }
            },
            {
              title: '用户邮箱',
              name: 'stuEmail',
              rules: [
                {
                  pattern: pattern.EMAIL,
                  message: '请输入正确的邮箱'
                }
              ]
            },
            { title: '专业', name: 'stuMajor', type: 'dict', dictName: 'stu_major' },
            {
              title: '政治面貌',
              name: 'stuPolitical',
              type: 'dict',
              dictName: 'teaching_political'
            },
            {
              title: '入学时间',
              name: 'stuIndate',
              type: 'date',
              // rules: { required: true, message: '请输入入学时间' }
            },
            {
              title: '所属期班',
              name: 'calId',
              type: 'cuselect',
              queryInfo: 'business/clazz/list?pageNum=1&pageSize=100',
              replaceFields: { label: 'claName', value: 'calId' },
              rules: { message: '请选择所属期班', required: true }
            },

            {
              title: '家庭住址',
              name: 'stuHomeaddr',
              type: 'textarea',
              maxLen: 200
            },
            { title: '照片', name: 'stuAvatar', type: 'image-upload', limit: 1 }
          ]
        }
      }
    },

    methods: {
      show(id) {
        this.id = id
        this.dia = true
        this.form = {}
        id && this.getInfo(id)
      },
      getInfo(id) {
        this.loading = true
        getStudents(id)
          .then(res => {
            this.form = res.data
          })
          .finally(() => {
            this.loading = false
          })
      },
      sub() {
        this.$refs.refsForm.subForm(form => {
          let act = this.form.stuId ? updateStudents : addStudents
          act(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
        })
      }
    }
  }
</script>
<style></style>
