<template>
  <el-table
    ref="table"
    border
    :size="size"
    table-layout="fixed"
    :data="dataList"
    height="100%"
    width="100%"
    stripe
    :row-key="tableConfig.rowKey"
    @selection-change="selectionChange"
    @row-click="rowClick"
    @row-dblclick="rowDblclick"
    @row-contextmenu="rowContextmenu"
    v-on="$attrs"
  >
    <el-table-column
      v-if="tableConfig.isChecked"
      type="selection"
      align="center"
      width="40"
      fixed="left"
    />
    <el-table-column v-if="tableConfig.radioCheck" width="55" align="center" label="#" fixed="left">
      <template #default="scope">
        <el-radio
          v-model="tmpSelection"
          class="radio-no-text"
          :label="row[tableConfig.rowKey || 'id']"
          @click.stop
          @change="value => rowClick(row, value)"
        >
          &nbsp;
        </el-radio>
      </template>
    </el-table-column>
    <!-- <el-table-column label="序号" align="center" width="55" type="index" fixed="left" /> -->
    <el-table-column label="序号" align="center" width="55" type="index" fixed="left">
      <template #default="{ $index }">
        {{ pageParams ? $index + pageParams.pageSize * (pageParams.pageNum - 1) + 1 : $index + 1 }}
      </template>
    </el-table-column>
    <el-table-column
      v-if="tableConfig.warning && warningType"
      label="预警标识"
      align="center"
      width="85"
      type="index"
    >
      <template #default="scope">
        <c-tag :color="row.warning" :text="row.warningMsg" />
      </template>
    </el-table-column>
    <template v-if="tableConfig.cols?.length">
      <el-table-column
        v-for="(v, i) in tableConfig.cols.filter(v => v)"
        :key="i"
        :label="v.label"
        :prop="v.prop"
        :show-overflow-tooltip="!v.showAll"
        :width="v.width || (!v.minWidth ? 150 : '')"
        :align="v.align"
        :min-width="v.minWidth"
        :fixed="v.fixed || (v.type == 'action' || v.actions || v.actionFun ? 'right' : false)"
      >
        <template #default="scope">
          <div v-if="v.renderFun">
            <exSlot :render-fun="v.renderFun" :obj-data="scope" />
          </div>
          <el-link
            v-else-if="v.type == 'text'"
            v-hasPermi="v.permission"
            :underline="false"
            @click.stop="v.click(scope?.row)"
          >
            {{ scope?.row[v.prop] }}
          </el-link>
          <c-tag
            v-else-if="v.type == 'warning'"
            :color="scope.row.warning"
            :text="scope.row.warningMsg"
          />
          <template v-else-if="v.actions || v.actionFun">
            <div>
              <el-link
                v-for="(k, j) in v.actions || v.actionFun(scope)"
                :key="j"
                v-hasPermi="k.permission"
                type="primary"
                @click.stop="k.click(scope.row)"
              >
                <x-icon v-if="k.icon" :source-icon="k.icon" />{{ k.label }}
              </el-link>
            </div>
          </template>
          <DictSelect
            v-else-if="v.type == 'dict'"
            :dict-name="v.dictName"
            type="text"
            :model-value="scope.row[v.prop] + ''"
          />
          <span v-else>
            {{ scope?.row[v.prop] }}
          </span>
        </template>
      </el-table-column>
    </template>
    <slot />
  </el-table>
</template>
<script>
  import { defineComponent, h } from 'vue'
  import { tableScrollBtm } from '@/utils/scroll-to'
  let exSlot = defineComponent({
    name: 'CusRender',
    props: {
      objData: Object,
      renderFun: Function
    },
    render() {
      return this.renderFun(h, this.objData)
    }
  })
  export default {
    components: { exSlot },
    props: {
      tableConfig: {
        type: Object,
        default: () => {
          return {
            rowKey: 'id',
            isChecked: false
          }
        }
      },
      size: {
        type: String
      },
      warningType: {
        type: Number,
        default: 0
      },
      dataList: {
        type: Array,
        default: () => []
      },
      pageParams: {
        type: Object,
        default: () => {}
      }
    },
    emits: ['rowClick', 'selectionChange', 'rowDblclick', 'rowContextmenu', 'reset', 'changeData'],
    data() {
      return {
        tmpSelection: '',
        selection: []
      }
    },
    computed: {
      table() {
        return this.$refs.table
      }
    },
    methods: {
      scrollBtm() {
        tableScrollBtm(this.$refs.table)
      },
      selectionChange(selections, row) {
        this.$emit('selectionChange', selections, row)
      },
      rowContextmenu(row) {
        this.$emit('rowContextmenu', row)
      },
      rowDblclick(row) {
        this.$emit('rowDblclick', row)
      },
      rowClick(row, event) {
        // console.log('event', event.target?.localName)
        // let outTargets = ['INPUT', 'SELECT']
        // if (outTargets.includes(event.target?.localName)) return
        if (this.tableConfig.radioCheck) {
          this.tmpSelection = row[this.tableConfig.rowKey || 'id']
          this.$emit('selectionChange', [row])
        }
        if (this.tableConfig.isChecked) {
          this.$refs.table.toggleRowSelection(row)
        }
        this.$emit('rowClick', row)
      }
    }
  }
</script>
