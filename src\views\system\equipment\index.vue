<template>
  <div class="app-container">
    <div class="ptitle">设备管理</div>

    <el-row :gutter="10" class="mb-8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:export']"
          type="warning"
          plain
          icon="upload"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:edit']"
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      class="fit-table"
      :data="equipmentList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        type="index"
        label="序号"
        width="55"
        align="center"
      />
      <el-table-column label="设备类型" align="center" prop="type">
        <template #default="scope">
          <dict-tag :options="sys_equip_type" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="设备编号" align="center" prop="code" />
      <el-table-column label="设备名称" align="center" prop="name" />
      <el-table-column label="IP地址" align="center" prop="ip" />
      <el-table-column label="端口" align="center" prop="port" />
      <el-table-column label="操作" fixed="right">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:post:edit']"
            link
            icon="Edit"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            v-hasPermi="['system:post:remove']"
            link
            icon="Delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改信号模板对话框 -->
    <el-dialog
      v-model="open"
      :title="title"
      width="500px"
      append-to-body
    >
      <el-form
        ref="equipRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="设备类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio v-for="dict in sys_equip_type" :key="dict.value" :label="dict.value">
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备编号" prop="code">
          <el-input v-model="form.code" placeholder="请输入编号" />
        </el-form-item>
        <el-form-item label="ip" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input v-model="form.port" placeholder="请输入端口" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Post">
  import {
    getEquipList,
    addOrUpdateEquip,
    getEquipDetail,
    delEquip,
    batchDelEquip
  } from '@/api/system/equipment'
  const { proxy } = getCurrentInstance()
  const { sys_equip_type } = proxy.useDict('sys_equip_type')

  /**信号模板列表 */
  const equipmentList = ref([])
  const open = ref(false)
  const loading = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10
    },
    rules: {
      name: [{ required: true, message: '设备名称不能为空', trigger: 'blur' }],
      code: [{ required: true, message: '设备编号不能为空', trigger: 'blur' }],
      ip: [{ required: true, message: 'IP不能为空', trigger: 'blur' }],
      port: [{ required: true, message: '端口不能为空', trigger: 'blur' }]
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  /** 查询信号模板列表 */
  function getList() {
    loading.value = true
    getEquipList(queryParams.value).then(response => {
      equipmentList.value = response.data.list
      total.value = response.data.total || 0
      loading.value = false
    })
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false
    reset()
  }
  /** 表单重置 */
  function reset() {
    form.value = {
      id: undefined,
      ip: undefined,
      name: undefined,
      code: undefined,
      port: undefined,
      type: '0'
    }
    proxy.resetForm('equipRef')
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    handleQuery()
  }
  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = []
    selection.forEach(item => {
      ids.value.push(item.id)
    })
    single.value = selection.length != 1
    multiple.value = !selection.length
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset()
    open.value = true
    title.value = '添加设备'
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset()
    const equipId = row.id || ids.value
    getEquipDetail(equipId).then(response => {
      form.value = response.data
      form.value.type = form.value.type.toString()
      open.value = true
      title.value = '修改设备'
    })
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['equipRef'].validate(valid => {
      if (valid) {
        const submitData = {
          name: form.value.name,
          code: form.value.code,
          ip: form.value.ip,
          port: form.value.port,
          type: form.value.type
        }
        console.log(submitData)
        open.value = false
        if (form.value.id != undefined) {
          submitData.id = form.value.id
          addOrUpdateEquip(submitData).then(response => {
            proxy.$modal.msgSuccess('修改成功')
            open.value = false
            getList()
          })
        } else {
          addOrUpdateEquip(submitData).then(response => {
            proxy.$modal.msgSuccess('新增成功')
            open.value = false
            getList()
          })
        }
      }
    })
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确认删除')
      .then(function () {
        if (ids.value.length > 0) {
          return batchDelEquip(ids.value)
        } else {
          return delEquip({ id: row.id })
        }
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess('删除成功')
      })
      .catch(() => {})
  }
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      '/device/export',
      {
        ...queryParams.value
      },
      `post_${new Date().getTime()}.xlsx`
    )
  }

  getList()
</script>
