<template>
  <el-table
    border
    :data="tableList"
    :height="350"
  >
    <el-table-column
      label="序号"
      type="index"
      width="60"
      align="center"
    />
    <el-table-column
      label="信号频率（MHz）"
      prop="carrierFrequency"
      align="center"
    />
    <el-table-column label="脉冲宽度（KHz）" prop="pulseWidth" align="center" />
    <el-table-column
      label="脉冲幅度"
      prop="pulseAmplitude"
      align="center"
      width="120"
    />
    <el-table-column label="重周" prop="repetitionPeriod" align="center" />
    <el-table-column label="雷达型号" prop="randarType" align="center" />
  </el-table>
</template>

<script setup>
const props = defineProps({
  tableList: {
    type: Array,
    default: () => []
  }
})
</script>