<template>
  <div v-if="false" style="width: 240px">
    <el-button
      plain
      size="small"
      class="ml-3"
      @click="single"
    >
      单次
    </el-button>
    <el-button plain size="small" @click="auto">自动</el-button>
    <el-button plain size="small" class="mr-4">文件</el-button>
  </div>
</template>

<script setup>
  import usePlayControl from '@/store/modules/playControl'
  // import useChartsStore from '@/store/modules/charts'

  const playControl = usePlayControl()
  // const chartsStore = useChartsStore()

  const single = () => {
    playControl.single()
  }
  const auto = () => {
    playControl.auto()

  }
</script>
