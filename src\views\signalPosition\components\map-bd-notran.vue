<template>
  <!-- 地图 -->
  <div id="map" ref="map" class="w-full h-full" />
  <!-- 地图坐标数据 -->
  <div id="overlay-box" />
</template>

<script setup>
  import 'ol/ol.css'
  import TileLayer from 'ol/layer/Tile'
  import VectorLayer from 'ol/layer/Vector'
  import VectorSource from 'ol/source/Vector'
  import XYZ from 'ol/source/XYZ'
  import { Map, View, Feature } from 'ol'
  import { Stroke, Style, Icon } from 'ol/style'
  import { LineString } from 'ol/geom'
  import { fromLonLat } from 'ol/proj'
  import { Point } from 'ol/geom'
  import Overlay from 'ol/Overlay'
  import { onMounted, ref, watch } from 'vue'
  import mapList from '@/views/maptype'
  import { TileImage } from 'ol/source'
  import { Tile } from 'ol/layer'
  import TileGrid from 'ol/tilegrid/TileGrid'

  let map = ref(null)
  let pointLayer = {}

  const props = defineProps({
    // 地图固定坐标数据（点数据）
    fixedData: {
      type: Array,
      default: () => []
    },
    dynamicData: {
      type: Object,
      default: () => {}
    }
  })

  const baiduLayer = new TileLayer({
    source: new XYZ({
      projection: 'EPSG:3857',
      tileUrlFunction: function (tileCoord) {
        var z = tileCoord[0]
        var x = tileCoord[1]
        var y = -tileCoord[2] - 1 // 注意百度瓦片Y坐标的处理方式
        return (
          'http://online0.map.bdimg.com/onlinelabel/?qt=tile&x=' +
          x +
          '&y=' +
          y +
          '&z=' +
          z +
          '&styles=pl&scaler=1&p=1'
        )
      },
      tilePixelRatio: 2 // 百度使用的是高清瓦片，像素比可能需要调整
    })
  })

  onMounted(() => {
    initMap()
  })

  const initMap = () => {
    let resolutions = []
    let baiduX, baiduY

    for (let i = 0; i < 19; i++) {
      resolutions[i] = Math.pow(2, 18 - i)
    }
    let tilegrid = new TileGrid({
      origin: [0, 0],
      resolutions: resolutions
    })

    let baidu_source = new TileImage({
      projection: 'EPSG:3857',
      tileGrid: tilegrid,
      tileUrlFunction: function (tileCoord) {
        if (!tileCoord) return ''
        let z = tileCoord[0]
        let x = tileCoord[1]
        let y = tileCoord[2]
        // 对编号xy处理
        baiduX = x < 0 ? x : 'M' + -x
        baiduY = -y
        return (
          'http://online3.map.bdimg.com/onlinelabel/?qt=tile&x=' +
          baiduX +
          '&y=' +
          baiduY +
          '&z=' +
          z +
          '&styles=pl&udt=20151021&scaler=1&p=1'
        )
      }
    })

    let baidu_layer = new Tile({
      source: baidu_source
    })
    map.value = new Map({
      target: 'map',
      layers: [
        // new TileLayer({
        //   source: new XYZ({
        //     url: 'http://map.geoq.cn/ArcGIS/rest/services/ChinaOnlineStreetPurplishBlue/MapServer/tile/{z}/{y}/{x}'
        //   })
        // })
        baidu_layer
      ],
      view: new View({
        projection: 'EPSG:3857',
        center: fromLonLat([119.421003, 32.393159]),
        zoom: 7
      })
    })
    // 如果在mounted时数据已经存在，初始化地图要素
    if (props.fixedData.length > 0) {
      props.fixedData.forEach(item => {
        addPoints(item)
      })
    }
    if (Object.keys(props.dynamicData).length > 0) {
      Object.values(props.dynamicData).forEach(group => {
        drawLine(group.positions)
      })
    }
  }

  /**
   * 根据经纬度坐标添加监测站要素
   */
  const addPoints = pointInfo => {
    if (Object.keys(pointLayer).length == 0) {
      // 创建图层
      pointLayer = new VectorLayer({
        source: new VectorSource()
      })
      // 图层添加到地图上
      map.value.addLayer(pointLayer)
    }

    console.log(pointInfo.value)
    console.log(new Point(fromLonLat(pointInfo.value)))
    // 创建feature要素，一个feature就是一个点坐标信息
    const feature = new Feature({
      geometry: new Point(fromLonLat(pointInfo.value))
    })
    // 设置要素的图标
    feature.setStyle(
      new Style({
        // 设置图片效果
        image: new Icon({
          src: '../src/assets/images/pos.jpg',
          scale: 0.1
        })
      })
    )
    // 要素添加到地图图层上
    pointLayer.getSource().addFeature(feature)
    // 设置文字信息
    addText(pointInfo)

    // 检查是否是主机
    if (pointInfo.name === '主机') {
      // 将视图中心设置为第一个点的位置
      map.value.getView().setCenter(fromLonLat(pointInfo.value))
    }
  }

  /**
   * 添加文本到地图指定位置
   * @param coordinate 坐标点数组，包含经纬度信息
   */
  const addText = pointInfo => {
    const overlayBox = document.getElementById('overlay-box') //获取一个div
    const oSpan = document.createElement('span') //创建一个span
    oSpan.contentEditable = true //设置文字是否可编辑
    oSpan.id = pointInfo.value[0] //创建一个id
    let pText = document.createTextNode(pointInfo.name) //创建span的文本信息
    // 设置颜色和字体大小
    oSpan.style = 'color: #d62f20; font-size: 16px;font-weight:bold'
    oSpan.appendChild(pText) //将文本信息添加到span
    overlayBox.appendChild(oSpan) //将span添加到div中
    let textInfo = new Overlay({
      position: fromLonLat(pointInfo.value), //设置位置
      element: document.getElementById(pointInfo.value[0]),
      offset: [-15, -50] //设置偏移
    })
    map.value.addOverlay(textInfo)
  }

  /**
   * 绘制线段
   * @param coordinatesArr 点坐标数组
   */
  // const drawLine = coordinatesArr => {
  //   console.log(coordinatesArr, 12123)
  //   // 动态处理坐标数组，将其转换为投影坐标
  //   const coordinates = coordinatesArr.map(coord => fromLonLat(coord))
  //   // 创建线段几何
  //   const lineString = new LineString(coordinates)
  //   // 创建线段要素
  //   const feature = new Feature({
  //     geometry: lineString
  //   })

  //   // 创建样式
  //   const style = new Style({
  //     stroke: new Stroke({
  //       color: '#ffcc33',
  //       width: 2
  //     })
  //   })

  //   feature.setStyle(style)

  //   // 创建矢量涂层
  //   const vectorSource = new VectorSource({
  //     features: [feature]
  //   })
  //   const vectorLayer = new VectorLayer({
  //     source: vectorSource
  //   })

  //   // 添加矢量图层到地图
  //   map.value.addLayer(vectorLayer)
  // }
  const drawLine = coordinatesArr => {
    const coordinates = coordinatesArr.map(coord => fromLonLat(coord))
    const lineString = new LineString(coordinates)
    const feature = new Feature({
      geometry: lineString
    })

    const style = new Style({
      stroke: new Stroke({
        color: '#d62f20',
        width: 2
      })
    })

    feature.setStyle(style)

    const vectorSource = new VectorSource({
      features: [feature]
    })
    const vectorLayer = new VectorLayer({
      source: vectorSource
    })

    map.value.addLayer(vectorLayer)
  }

  watch(
    () => props.fixedData,
    newData => {
      if (map.value && newData.length > 0) {
        newData.forEach(item => {
          addPoints(item)
        })
      }
    },
    { immediate: true, deep: true }
  )

  // watch(
  //   () => props.dynamicData,
  //   newData => {
  //     console.log(newData)
  //     if (map.value && Object.keys(newData).length > 0) {
  //       Object.values(newData).forEach(item => {
  //         item.positions.forEach(coord => {
  //           drawLine(coord)
  //         })
  //       })
  //       // newData.forEach(item => {
  //       //   console.log(item)
  //       //   item.positions.forEach(coord => {
  //       //     drawLine(coord)
  //       //   })
  //       // })
  //     }
  //   },
  //   { immediate: true, deep: true }
  // )
  watch(
    () => props.dynamicData,
    newData => {
      console.log('New dynamicData:', newData)
      if (map.value && Object.keys(newData).length > 0) {
        Object.values(newData).forEach(group => {
          console.log('Processing group:', group)
          drawLine(group.positions)
        })
      }
    },
    { immediate: true, deep: true }
  )
</script>

<style scoped>
  #wrapper {
    height: 100vh;
    width: 100vw;
  }
  #map {
    height: 100%;
    width: 100%;
  }
</style>
