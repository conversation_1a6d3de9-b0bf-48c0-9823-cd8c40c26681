<template>
  <el-form>
    <el-form-item label="测量模式">
      <el-select v-model="form.measureMode" type="number">
        <el-option
          v-for="item in measure_mode"
          :key="Number(item.value)"
          :label="item.label"
          :value="Number(item.value)"
        />
      </el-select>
    </el-form-item>
    <!-- <el-form-item label="极化方式">
      <el-select v-model="form.polarizationType" type="number">
        <el-option
          v-for="item in polarization_type"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="检波方式">
      <el-select v-model="form.detectionMode" type="number">
        <el-option
          v-for="item in detection_mode"
          :key="Number(item.value)"
          :label="item.label"
          :value="Number(item.value)"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="检波速度">
      <el-input v-model="form.detectionSpeed" type="number" />
    </el-form-item> -->
    <el-form-item label="衰减模式">
      <el-select v-model="form.attenuationMode" type="number" @change="attenuationModeChange">
        <el-option
          v-for="item in attenuation_mode"
          :key="Number(item.value)"
          :label="item.label"
          :value="Number(item.value)"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="衰减值">
      <el-input v-model="form.attenuation" type="number">
        <template #append>
          <span class="pl-1 pr-1">dB</span>
        </template>
      </el-input>
    </el-form-item>
    <!-- <el-form-item label="增益模式">
      <el-select v-model="form.gainMode" type="number">
        <el-option
          v-for="item in gain_mode"
          :key="Number(item.value)"
          :label="item.label"
          :value="Number(item.value)"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="增益值">
      <el-input v-model="form.gain" type="number">
        <template #append>
          <span class="pl-1 pr-1">dbm</span>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label="驻留时间">
      <el-input v-model="form.lingerTime" type="number">
        <template #append>
          <span class="pl-1 pr-1">ms</span>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label="门限类型">
      <el-select v-model="form.thresholdType">
        <el-option
          v-for="item in threshold_type"
          :key="Number(item.value)"
          :label="item.label"
          :value="Number(item.value)"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="门限">
      <el-input v-model="form.threshold" type="number">
        <template #append>
          <span class="pl-1 pr-1">dBm</span>
        </template>
      </el-input>
    </el-form-item> -->
    <slot name="formItem" :form="form" />
  </el-form>
</template>
<script setup>
  import { isEmpty } from 'lodash'
  import useScanStore from '@/store/modules/scanMonitor'

  /** 枚举值获取 */
  const { proxy } = getCurrentInstance()
  // 字典项
  const {
    measure_mode,
    polarization_type,
    detection_mode,
    attenuation_mode,
    gain_mode,
    threshold_type
  } = proxy.useDict(
    'measure_mode',
    'polarization_type',
    'detection_mode',
    'attenuation_mode',
    'gain_mode',
    'threshold_type'
  )

  const props = defineProps({
    model: {
      type: Object,
      default: () => ({})
    }
  })

  const scanStore = useScanStore()
  // const spectrum = ref(scanStore.spectrumCopy)
  const form = ref({ ...props.model.taskParams })

  // 使用watchEffect来监听taskParams的变化
  watchEffect(() => {
    form.value = { ...props.model.taskParams }
  })

  const attenuationModeChange = info => {
    if (info === 0) {
      form.value.attenuation = 0
    }
  }

  defineExpose({
    form
  })
</script>
<style scoped>
  .el-select {
    width: 100%;
  }
</style>
