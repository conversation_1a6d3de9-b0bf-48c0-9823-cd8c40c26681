import { chartConfig } from '@/components/SpectrumSignal/chartConfig'

let instance = null // 实例

export function setInstance(item) {
  instance = item
}
export function getInstance(item) {
  return instance
}

export function update(key, val) {
  if (!instance) {
    return
  }
  const keys = key.split('.')
  const targetKey = keys.pop()
  const lastConfig = keys.reduce((accumulator, k) => {
    return accumulator[k]
  }, chartConfig)
  lastConfig[targetKey] = val
  const updateKey = keys[0] ? keys[0] : key
  instance.update({ [updateKey]: chartConfig[updateKey] })
}
