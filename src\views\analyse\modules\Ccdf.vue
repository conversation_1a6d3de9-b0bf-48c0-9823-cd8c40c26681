<template>
  <chart-header title="CCDF 图" name="ccdf" />
  <el-row v-if="ccdfModel.data">
    <el-col :span="24" class="ccdf-show">
      <!-- 添加数据展示 -->
      <data-chart
        :id="CCDF"
        :title="ccdfModel.title"
        :data="ccdfModel.data"
        :config="ccdfModel.config"
        class="data-chart"
        @transferData="transferData"
      />
      <div class="data-show">
        <div class="w-flex">
          <div class="column one-column">
            <div>10%</div>
            <div>1%</div>
            <div>0.1%</div>
            <div>0.01%</div>
            <div>0.001%</div>
            <div>0.0001%</div>
            <div>峰值</div>
          </div>
          <div class="column two-column">
            <div>{{ ccdfArr[0] }}</div>
            <div>{{ ccdfArr[1] }}</div>
            <div>{{ ccdfArr[2] }}</div>
            <div>{{ ccdfArr[3] }}</div>
            <div>{{ ccdfArr[4] }}</div>
            <div>{{ ccdfArr[5] }}</div>
            <div>{{ peakvalue }}</div>
          </div>
          <div class="column three-column">
            <div>--dB</div>
            <div>--dB</div>
            <div>--dB</div>
            <div>--dB</div>
            <div>--dB</div>
            <div>--dB</div>
            <div>--dB</div>
          </div>
        </div>
      </div>
    </el-col>
    <!-- 添加表单验证 -->
    <el-col v-if="false" :span="12" class="ccdf-form">
      <el-form :model="form">
        <el-form-item label="采集模式">
          <el-select v-model="form.acquisitionMode">
            <el-option label="单次" value="0" />
            <el-option label="连续" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="采集时间">
          <el-input
            v-model="form.acquisitionTime"
            class="el-input"
            @input="
              form.acquisitionTime = form.acquisitionTime.replace(
                /[^/0/1/2/3/4/5/6/7/8/9/./m/s/]/gi,
                ''
              )
            "
          />
        </el-form-item>
        <el-form-item label="测量时间">
          <span>1.000ms</span>
        </el-form-item>
        <el-form-item label="平均功率">
          <span>-30.02dB</span>
        </el-form-item>
        <el-button type="info" class="el-button">清除迹线</el-button>
        <el-form-item label="参考差值(dB)">
          <el-input
            v-model="form.referenceDifference"
            class="el-input"
            @input="
              form.referenceDifference = form.referenceDifference.replace(
                /[^/0/1/2/3/4/5/6/7/8/9/./]/gi,
                ''
              )
            "
          />
        </el-form-item>
        <el-button type="info" class="el-button">存储参考电平</el-button>
        <el-form-item label="高斯">
          <el-checkbox v-model="form.gauss" />
        </el-form-item>
        <el-form-item label="参考">
          <el-checkbox v-model="form.reference" />
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
</template>

<script setup>
  import { ref } from 'vue'
  import DataChart from '@/components/DataChart'
  import useChartsStore from '@/store/modules/charts'
  import useChartOptions from '@/common/hooks/chartOptions'
  import ChartHeader from '@/views/analyse/header/ChartHeader'
  import { queryCCDF } from '@/api/charts'
  import { CCDF } from '@/constant/chartConsts'

  const { chartOptions } = useChartOptions('ccdf')
  const form = reactive({
    acquisitionMode: '0',
    acquisitionTime: '200.000ms',
    referenceDifference: '20.000',
    gauss: '',
    reference: ''
  })
  const peakvalue = ref(null)
  const ccdfArr = ref([])
  const transferData = (max, dataList) => {
    if (max.value >= 20) {
      peakvalue.value = '20dB'
    } else {
      peakvalue.value = max.value + 'dB'
    }
    ccdfArr.value = dataList
  }
  const chartsStore = useChartsStore()
  const viewNum = computed(() => chartsStore.static.enabled ? 'all' : chartsStore.viewNum)
  const dataHandler = data => {
    const result = []
    for (let i = 0; i < data.length; i++) {
      result.push([data[i].dbm, data[i].ccdf])
    }
    return result  
  }
  // CCDF 图
  const ccdfModel = ref({})
  const requestCCDFCharstData = async () => {
    const body = chartsStore.getFileInfo()
    const { data } = await queryCCDF(body)
    ccdfModel.value = {
      id: 'ccdfChart',
      title: 'CCDF 图',
      data: dataHandler(data),
      config: {
        type: 'ccdf',
        chart: computed(() => chartOptions.value),
        viewNum,
        hideBar: true
      }
    }
  }

  onMounted(() => {
    requestCCDFCharstData()
  })
</script>
<style scoped lang="scss">
  .data-show {
    position: absolute;
    top: 30px;
    right: 30px;
    z-index: 2;
    background-color: var(--background-color);
    color: black;
    padding: 10px;
    opacity: 1;
  }
  .ccdf-show {
    position: relative;
  }
  .ccdf-form {
    background-color: var(--ccdf-form-bg);
    padding: 20px;
    .el-button {
      margin: 0 96px 18px;
    }
    :deep(.el-form-item__label) {
      width: 96px;
      justify-content: flex-start;
    }
  }
  .w-flex {
    display: flex;
  }
  .column {
    flex-grow: 1;
  }
  .one-column {
    color: var(--text-color);
  }
  .two-column {
    color: red;
    padding: 0 5px;
  }
  .three-column {
    color: purple;
  }
</style>
