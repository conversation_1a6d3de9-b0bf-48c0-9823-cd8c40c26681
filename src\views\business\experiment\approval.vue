<template>
  <el-dialog
    v-model="dia"
    size="800px"
    append-to-body
    :title="id ? '实验项目审批' : '申请实验项目'"
  >
    <c-form
      v-bind="formConfig"
      :key="form.expId"
      ref="refsForm"
      v-model="form"
      v-loading="loading"
      class="mr-4"
    />
    <template #footer>
      <el-button @click="dia = false">取消</el-button>
      <el-button type="primary" @click="sub">保存</el-button>
    </template>
  </el-dialog>
</template>
<script>
  import { checkPermi } from '@/utils/permission'
  import { getExperiment, approvalExperiment } from '@/api/business/experiment'
  let formInfo = [
    {
      title: '项目编码',
      name: 'expCode',
      readonly: true,
      rules: { required: true, message: '请输入实验项目编码' }
    },
    {
      title: '项目名称',
      readonly: true,
      name: 'expName',
      rules: { required: true, message: '请输入项目名称' }
    },

    {
      title: '实验开始时间',
      name: 'expStart',
      readonly: true,
      type: 'date',
      rules: { required: true, message: '请选择实验开始时间' }
    },
    {
      title: '实验结束时间',
      name: 'expEnd',
      readonly: true,
      type: 'date',
      rules: { required: true, message: '请选择实验结束时间' }
    },
    {
      isFull: true,
      title: '实验目的',
      name: 'expDest',
      type: 'textarea',
      readonly: true,
      rules: { required: true, message: '请输入实验目的' }
    },

    {
      title: '负责人',
      readonly: true,
      name: 'expChargeName',
      rules: { required: true, message: '请输入负责人' }
    },
    {
      title: '申请人',
      readonly: true,
      name: 'expApplyName',
      rules: { required: true, message: '请输入申请人' }
    },
    {
      readonly: true,
      title: '申请经费(万元)',
      name: 'expPlanCost',
      rules: { required: true, message: '请输入申请经费' }
    },
    { isFull: true, title: '备注', readonly: true, name: 'remark', type: 'textarea' },
    {
      title: '审核状态',
      name: 'expAduitStatus',
      type: 'dict',
      rules: [{ required: true, message: '请选择审核类型' }],
      readonly: !checkPermi(['business:experiment:audit']),
      dictName: 'exp_aduit_status'
    },
    {
      isFull: true,
      title: '审核意见',
      readonly: !checkPermi(['business:experiment:audit']),
      name: 'expAuditBiz',
      type: 'textarea'
    }
  ]
  export default {
    name: 'ExperimentDetail',
    emits: ['actSuccess'],
    data() {
      return {
        dia: false,
        loading: false,
        id: '',
        form: {},
        formConfig: {
          splice: 2,
          labelWidth: '10em',
          layout: 'block',
          autoEmit: false,
          hideBtn: true,
          formInfo
        }
      }
    },
    methods: {
      show(id = '') {
        this.id = id
        this.dia = true
        this.form = {}
        this.getInfo(id)
      },
      getInfo(id) {
        this.loading = true
        getExperiment(id)
          .then(res => {
            this.form = res.data
          })
          .finally(() => {
            this.loading = false
          })
      },
      sub() {
        this.$refs.refsForm.subForm(form => {
          approvalExperiment(form).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.dia = false
            this.$emit('actSuccess')
          })
        })
      }
    }
  }
</script>
<style></style>
