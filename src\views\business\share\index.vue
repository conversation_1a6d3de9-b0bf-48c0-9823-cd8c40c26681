<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="8em"
    >
      <el-form-item label="用户编码" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否授权" prop="isEmpower">
        <el-input
          v-model="queryParams.isEmpower"
          placeholder="请输入是否授权"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资源密码" prop="resPassword">
        <el-input
          v-model="queryParams.resPassword"
          placeholder="请输入资源密码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="授权人" prop="authorizeBy">
        <el-input
          v-model="queryParams.authorizeBy"
          placeholder="请输入授权人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="授权时间" prop="authorizeTime">
        <el-date-picker
          v-model="queryParams.authorizeTime"
          clearable
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择授权时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:share:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:share:edit']"
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:share:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['business:share:export']"
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar v-model:show-search="showSearch" @queryTable="getList" />
    </el-row>
    <div class="fit-table">
      <el-table
        v-loading="loading"
        border
        stripe
        :data="shareList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="虚拟资源编码" align="center" prop="resCode" />
        <el-table-column label="用户编码" align="center" prop="userId" />
        <el-table-column label="用户姓名" align="center" prop="userName" />
        <el-table-column label="是否授权" align="center" prop="isEmpower" />
        <el-table-column label="资源账号" align="center" prop="resAccount" />
        <el-table-column label="资源密码" align="center" prop="resPassword" />
        <el-table-column label="状态" align="center" prop="status" />
        <el-table-column label="授权人" align="center" prop="authorizeBy" />
        <el-table-column label="授权时间" align="center" prop="authorizeTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.authorizeTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <el-button
              v-hasPermi="['business:share:edit']"
              type="text"
              icon="Edit"
              @click="handleUpdate(scope.row)"
            >
              修改
            </el-button>
            <el-button
              v-hasPermi="['business:share:remove']"
              type="text"
              icon="Delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改虚拟资源分配信息对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="8em">
        <el-form-item label="虚拟资源编码" prop="resCode">
          <el-input v-model="form.resCode" placeholder="请输入虚拟资源编码" />
        </el-form-item>
        <el-form-item label="用户编码" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户编码" />
        </el-form-item>
        <el-form-item label="用户姓名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户姓名" />
        </el-form-item>
        <el-form-item label="是否授权" prop="isEmpower">
          <el-input v-model="form.isEmpower" placeholder="请输入是否授权" />
        </el-form-item>
        <el-form-item label="资源密码" prop="resPassword">
          <el-input v-model="form.resPassword" placeholder="请输入资源密码" />
        </el-form-item>

        <el-form-item label="授权人" prop="authorizeBy">
          <el-input v-model="form.authorizeBy" placeholder="请输入授权人" />
        </el-form-item>
        <el-form-item label="授权时间" prop="authorizeTime">
          <el-date-picker
            v-model="form.authorizeTime"
            clearable
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择授权时间"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { listShare, getShare, delShare, addShare, updateShare } from '@/api/business/share'

  export default {
    name: 'Share',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 虚拟资源分配信息表格数据
        shareList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          resCode: null,
          userId: null,
          userName: null,
          isEmpower: null,
          resAccount: null,
          resPassword: null,
          status: null,
          authorizeBy: null,
          authorizeTime: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {}
      }
    },
    created() {
      this.getList()
    },
    methods: {
      /** 查询虚拟资源分配信息列表 */
      getList() {
        this.loading = true
        listShare(this.queryParams).then(response => {
          this.shareList = response.rows
          this.total = response.total
          this.loading = false
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          shId: null,
          resCode: null,
          userId: null,
          userName: null,
          isEmpower: null,
          resAccount: null,
          resPassword: null,
          status: '0',
          delFlag: null,
          authorizeBy: null,
          authorizeTime: null,
          updateBy: null,
          updateTime: null,
          remark: null
        }
        this.resetForm('form')
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryForm')
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.shId)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset()
        this.open = true
        this.title = '添加虚拟资源分配信息'
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset()
        const shId = row.shId || this.ids
        getShare(shId).then(response => {
          this.form = response.data
          this.open = true
          this.title = '修改虚拟资源分配信息'
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.shId != null) {
              updateShare(this.form).then(response => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
            } else {
              addShare(this.form).then(response => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const shIds = row.shId || this.ids
        this.$modal
          .confirm('是否确认删除虚拟资源分配信息编号为"' + shIds + '"的数据项？')
          .then(function () {
            return delShare(shIds)
          })
          .then(() => {
            this.getList()
            this.$modal.msgSuccess('删除成功')
          })
          .catch(() => {})
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download(
          'business/share/export',
          {
            ...this.queryParams
          },
          `share_${new Date().getTime()}.xlsx`
        )
      }
    }
  }
</script>
