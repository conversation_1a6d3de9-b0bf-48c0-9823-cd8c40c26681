<template>
  <el-table class="ml-2 max-h-[270px] h-[270px]" border :height="270" :data="tableData">
    <el-table-column label="序号" type="index" width="60" align="center" />
    <el-table-column label="信号频率" prop="bwHz" width="200" align="center">
      <template #default="{ row }">
        {{ numToPlot(row.bwHz) }}
      </template>
    </el-table-column>
    <el-table-column label="带宽（KHz）" prop="freqHz" align="center">
      <template #default="{ row }">
        {{ row.freqHz / 1000 }}
      </template>
    </el-table-column>
    <el-table-column label="信号幅度(dBm)" prop="level" align="center" />
    <el-table-column label="告警类型" prop="type" align="center" />
  </el-table>
</template>

<script setup>
  import { numToPlot } from '@/utils/utils'

  const props = defineProps({
    tableData: {
      type: Array,
      default: () => []
    }
  })
</script>
