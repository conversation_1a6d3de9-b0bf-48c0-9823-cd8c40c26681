<template>
  <div class="container">
    <div class="container-header" @click="playbarVisible = !playbarVisible">
      <x-icon v-show="playbarVisible" source="cus" icon="arrowDoubleDown" />
      <x-icon v-show="!playbarVisible" source="cus" icon="arrowDoubleUp" />
    </div>
    <el-row v-show="playbarVisible" class="container-body">
      <el-col :span="10" class="play-list">
        <el-row v-for="item in datList" :key="item.uploadTime" class="dat-item">
          <el-col :span="20">
            {{ item.fileName }}
          </el-col>
          <el-col :span="4" class="play-btn">
            <el-button v-if="isCurrent(item)" text type="success">当前播放 >> </el-button>
            <el-button v-else @click="playCurrent(item)">播放</el-button>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="14" class="play-bar">
        <Slider
          :min="0"
          :max="settings.IQNum"
          :value="chartsStore.start"
          :view-num="chartsStore.viewNum"
          :is-drag="false"
        />
        <el-row>
          <el-col :span="12">
            <div class="play-tool">
              <el-button
                v-show="!interval.timer"
                text
                size="small"
                @click="play"
              >
                <x-icon icon="CaretRight" source="el" size="24" />
              </el-button>
              <el-button v-show="interval.timer" text @click="pause">
                <x-icon icon="pause" size="24" source="cus" />
              </el-button>
              <el-button text size="small" @click="stop">
                <x-icon icon="stop" size="24" source="cus" />
              </el-button>
              <el-button text @click="fastback">
                <x-icon icon="fastback" size="24" source="cus" />
              </el-button>
              <el-button text @click="back">
                <x-icon icon="back" size="24" source="cus" />
              </el-button>
              <el-button text @click="forward">
                <x-icon icon="forward" size="24" source="cus" />
              </el-button>
              <el-checkbox v-model="isLoop" class="ml-4" label="循环播放" />
            </div>
            <div class="upload-file-bar">
              <el-button @click="dialogVisible = true">上传文件</el-button>
              <el-tooltip :content="chartsStore.file.fileName" placement="top">
                <span class="current-paly"> 当前播放： {{ chartsStore.file.fileName }} </span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="12">
            <vxe-table
              class="mt-2"
              border
              :edit-config="{ trigger: 'click', mode: 'row' }"
              :data="tableData"
              :show-header="false"
              cell-class-name="vxe-cell"
            >
              <vxe-table-column field="name" title="name" />
              <vxe-table-column field="value" title="value" :edit-render="{ name: 'input' }" />
            </vxe-table>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
  <Popup :message="dialogVisible" :debugmodemessage="debugmodeList" @childcLick="closeModal" />
</template>

<script setup name="Playbar">
  import * as mtApi from '@/api/singalManage'
  import Slider from '@/components/Slider'
  import useChartsStore from '@/store/modules/charts'
  import usePlayControl from '@/store/modules/playControl'
  import Interval from '@/common/classes/interval'
  import Popup from "@/components/Popup"
  import { FILE_TYPES } from '@/constant/file'

  defineComponent([Slider])

  const props = defineProps({
    reloadCharts: {
      type: Function,
      default: null
    }
  })
  const chartsStore = useChartsStore()
  const { settings } = chartsStore
  const playControl = usePlayControl()
  const isLoop = ref(true)
  const datList = ref([])
  const tableData = ref([
    { name: '步进宽度(采样点数)', value: settings.step, key: 'step' },
    { name: '播放速度(ms)', value: settings.playbackRate, key: 'playbackRate' },
    { name: '显示时间长度(us)', value: settings.swpTime, key: 'swpTime' }
  ])
  const route = useRoute()
  const interval = ref({})
  const dialogVisible = ref(false)
  const playbarVisible = ref(true)
  const debugmodeList = FILE_TYPES

  const requestList = () => {
    mtApi.allSignalListGet({ starttime: '', endtime: '' }).then(rsp => {
      datList.value = rsp.data.rows
    })
  }

  const playCurrent = item => {
    let path = '0'
    if (route.path.includes('radar')) {
      path = '1'
    }
    chartsStore.setFileInfo(item, path)
    props.reloadCharts()
  }

  const play = () => {
    if (playControl.playMode === 'single') {
      return
    }
    // 开始执行播放
    playControl.intervals.forEach(item => {
      item.play(!isLoop.value)
    })
  }

  const pause = () => {
    playControl.intervals.forEach(item => {
      item.pause()
    })
  }

  const stop = () => {
    playControl.intervals.forEach(item => {
      item.stop()
    })
  }

  const fastback = () => {
    interval.value.start = 0
    chartsStore.setStart(0)
  }

  const back = () => {
    playControl.intervals.forEach(item => {
      item.back()
    })
  }

  const forward = () => {
    playControl.intervals.forEach(item => {
      item.next()
    })
  }

  const createInterval = () => {
    const { step, IQNum } = settings
    interval.value = Interval.create(
      chartsStore.actionInterval,
      step,
      chartsStore.viewNum,
      IQNum
    )
    playControl.zeroSpanInterval = interval.value
  }

  const isCurrent = item => {
    return item.id === chartsStore.file.id
  }

  const closeModal = () => {
    dialogVisible.value = false
    requestList()
  }

  watchEffect(() => {
    tableData.value.forEach(item => {
      if (settings[item.key] !== item.value) {
        chartsStore.setConfig(item.key, parseInt(item.value))
      }
    })
    interval.value.total = settings.IQNum
  })
  
  watch(
    () => chartsStore.settings.swpTime,
    () => {
      props.reloadCharts()
    }
  )

  watch(() => isLoop.value, () => {
    pause()
    play()
  })

  onMounted(() => {
    createInterval()
    requestList()
  })

  onUnmounted(() => {
    Interval.remove(interval.value)
    playControl.zeroSpanInterval = null
  })
</script>

<style scoped lang="scss">
  .container {
    border: 1px solid #eee;
    max-width: 5000px;
    .container-header {
      display: flex;
      justify-content: center;
      cursor: pointer;
    }
    .container-body {
      height: 150px;
      padding: 8px;
      margin-top: 8px;
    }
    .play-list {
      overflow-y: auto;
      height: 100%;
    }
    .dat-item {
      border-top: 1px solid #eee;
      border-left: 1px solid #eee;
      &:last-of-type {
        border-bottom: 1px solid #eee;
      }
      .el-col {
        padding: 8px;
        border-right: 1px solid #eee;
        display: flex;
        align-items: center;
        &.play-btn {
          justify-content: center;
        }
      }
    }
    .play-bar {
      padding: 8px;
      position: relative;
      .play-tool {
        margin: 20px 0;
        .el-button {
          padding: 4px 0;
          margin: 0;
        }
      }
      .upload-file-bar {
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
    .upload-file-bar {
      max-width: 400px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    :deep(.vxe-cell) {
      height: 32px;
      line-height: 32px;
    }
  }
</style>
