<template>
  <div key="routine-container" class="online-container app-container">
    <el-row key="routine-main" class="main">
      <el-col :span="18" class="chart-info">
        <ScanSection>
          <!--频谱图  -->
          <div class="charts">
            <AnalyseCharts
              key="routine-chart"
              usekey="routine"
              :data-list="dataList"
              :model="spectrum"
              :device-enum="deviceList"
              :device-list="deviceDataList"
              v-model:checkList="checkList"
              v-model:deviceCode="deviceCode"
            />
          </div>

          <!-- 信号、采集、回放功能按钮模块 -->
          <div class="tabs">
            <BottomSignalTabs
              :signal-list="signalList"
              :warm-list="warmSigList"
              :deviceInfo="codeFindInfo"
              @stop="closeScan"
            />
          </div>
        </ScanSection>
      </el-col>
      <el-col :span="6" class="chart-form">
        <ScanSection class="charts">
          <div class="form">
            <!-- 右侧参数设置模块 -->
            <RightSettings :model="spectrum" @start="startWebsocket" @stop="closeScan">
              <template #default>
                <!-- 离散-->
                <ScatterSetting ref="scatterRef" v-show="spectrum.type" />
                <!-- 连续  -->
                <FreBandSetting ref="freBandRef" v-show="!spectrum.type" />
              </template>
              <template #formItem="{ form }">
                <el-form-item label="设备参数">
                  <el-button :loading="paramsLoading" @click="paramSetting(form)">
                    设备参数设置
                  </el-button>
                </el-form-item>
              </template>
            </RightSettings>
          </div>
        </ScanSection>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Scan">
  import ScanSection from '@/components/SpectrumSignal/ScanSection.vue'
  import AnalyseCharts from './analyseCharts/index.vue'
  import RightSettings from '@/components/SettingsFormPro/index.vue'
  import FreBandSetting from './rightSettings/FreBandSetting.vue'
  import ScatterSetting from './rightSettings/ScatterSetting.vue'
  import BottomSignalTabs from './bottomSignalTabs/index.vue'
  import useScanStore from '@/store/modules/scanMonitor'
  import useLinkWsHook from '@/common/hooks/linkWs'
  import { ElMessage, ElLoading } from 'element-plus'
  import { linkWs } from '@/api/upperComputer/scan'
  import { SCAN_CODE, DISPERSED_CODE, PARAMS_SETTING } from '@/constant/funCodes'
  import { deviceParams } from '@/constant/types'
  import { plotToNum } from '@/utils/utils'
  import { getEquipList } from '@/api/system/equipment'

  const route = useRoute()

  const scanStore = useScanStore()
  const spectrum = scanStore.routineAnalysis

  const checkList = ref(['实时'])

  // 查询参数
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10
  })
  const deviceCode = ref(null) //当前设备编码
  const host = ref(null) //当前设备ip
  const port = ref(null) //当前设备端口
  const deviceList = ref([]) //设备列表数据
  const resData = ref({}) //接收路由传递参数
  const freBandRef = ref(null) // 连续表单实例
  const scatterRef = ref(null) //离散表单实例

  const deviceData = ref({}) //设备参数
  const deviceDataList = ref([]) //数据列表

  // 获取设备列表,渲染主机/子机
  const getList = async () => {
    deviceDataList.value = []
    deviceData.value = []
    await getEquipList(queryParams.value).then(response => {
      deviceDataList.value = response.data.list
      deviceData.value = response.data.list[0]
      deviceList.value = []
      response.data.list.forEach(item => {
        deviceList.value.push({
          label: item.name,
          value: item.code,
          ip: item.ip,
          port: item.port
        })
      })
    })
  }

  const init = async () => {
    await getList()
    dealRouteInfo()
  }

  watch(deviceCode, (newValue, oldValue) => {
    console.log(deviceCode.value, 'routineAnalysis')
    const idx = deviceDataList.value.findIndex(item => item.code === deviceCode.value)
    const result = deviceDataList.value[idx]
    console.log(result, 'result')
    if (resData.value && !oldValue) {
      return
    } else {
      scanStore.setSpectrumData('routineAnalysis', {
        centerFreq: result.deviceScan.centerFreq,
        bandwidth: result.deviceScan.bandwidth,
        startFreq: result.deviceScan.startFreq,
        endFreq: result.deviceScan.endFreq,
        step: result.deviceScan.step.toString(),
        freqSectionNum: result.deviceScan.freqSectionNum,
        freqBandStr: result.deviceScan.freqBandStr,
        type: result.deviceScan.type
      })
      scanStore.updateTaskParams('routineAnalysis', result.devicePara)
      freBandRef.value.dataChangeFun()
      scatterRef.value.dataChangeFun()
    }
  })

  // 计算属性：查找设备信息
  const codeFindInfo = computed(() => {
    if (!deviceCode.value) {
      return {}
    }

    const resIdx = deviceList.value.findIndex(item => item.value === deviceCode.value)
    const resInfo = deviceList.value[resIdx]

    if (resInfo) {
      return resInfo
    } else if (resData.value) {
      return {
        label: resData.value.name,
        value: resData.value.code,
        ip: resData.value.ip,
        port: resData.value.port
      }
    }
    return {}
  })

  provide('codeFindInfo', codeFindInfo)

  const paramsLoading = ref(false)
  const taskFunCode = computed(() => {
    const { type, commandType } = spectrum
    if (commandType && type) {
      return null
    } else if (!commandType && !type) {
      return SCAN_CODE
    } else if (!commandType && type) {
      return DISPERSED_CODE
    } else {
      return null
    }
  })
  const { linkScan, closeScan, pageData } = useLinkWsHook(spectrum, taskFunCode)
  const dataList = computed(() => {
    let initData = {
      current: [],
      average: [],
      max: [],
      min: [],
      limit: [],
      occupancy: []
    }
    if (scanStore.playAnalyzeFfts.length > 0 && spectrum.status === 0) {
      return { current: scanStore.playAnalyzeFfts }
    }
    if (!pageData.value) {
      return initData
    }
    if (taskFunCode.value === SCAN_CODE) {
      const data = pageData.value.subtaskResultBody
      if (!data) {
        return initData
      }
      const length = data.frequencyListFromSrList.length
      const current = new Array(length)
      // const current = checkList.value.includes('实时') ? new Array(length) : null
      const average = checkList.value.includes('平均') ? new Array(length) : null
      const max = checkList.value.includes('最大') ? new Array(length) : null
      const min = checkList.value.includes('最小') ? new Array(length) : null
      const limit = checkList.value.includes('门限') ? new Array(length) : null
      const occupancy = new Array(length)

      for (let i = 0; i < length; i++) {
        const item = data.frequencyListFromSrList[i]
        current[i] = item.level
        if (average) average[i] = item.average
        if (max) max[i] = item.maximum
        if (min) min[i] = item.minimum
        if (limit) limit[i] = item.threshold
        occupancy[i] = item.occupancy
      }
      if (current) initData.current.push(...current)
      if (average) initData.average.push(...average)
      if (max) initData.max.push(...max)
      if (min) initData.min.push(...min)
      if (limit) initData.limit.push(...limit)
      if (occupancy) initData.occupancy.push(...occupancy)
    } else if (taskFunCode.value === DISPERSED_CODE) {
      const data = pageData.value.discreteChannelResult?.frequencyListFromDcList
      if (!data) {
        return initData
      }
      data.sort((a, b) => a.frequency - b.frequency)
      data.forEach(item => {
        initData.current.push([item.frequency, item.fieldStrength])
        initData.average.push([item.frequency, item.average])
        initData.max.push([item.frequency, item.maximum])
        initData.min.push([item.frequency, item.minimum])
        initData.limit.push([item.frequency, item.fieldStrength])
        initData.occupancy.push([item.frequency, item.occupancy])
      })
    }
    return initData
  })
  const signalList = computed(() => {
    if (!pageData.value || !pageData.value.subtaskResultBody) {
      return []
    }
    return pageData.value.subtaskResultBody.signalFromSrList || []
  })

  const lastWarmSigList = ref([])

  const warmSigList = computed(() => {
    const currentList = pageData.value?.warmSigList
    if (currentList && currentList.length > 0) {
      lastWarmSigList.value = currentList // 更新到最后一次已知的有效列表
    }
    return currentList && currentList.length > 0 ? currentList : lastWarmSigList.value
  })
  const paramSetting = async form => {
    let ws = null
    const data = {
      parameterCount: deviceParams.length,
      parameterCode: deviceParams.map(item => item.code),
      parameterValue: deviceParams.map(item => form[item.key])
    }
    paramsLoading.value = true
    host.value = codeFindInfo.value.ip
    port.value = codeFindInfo.value.port
    ws = await linkWs({
      host: host.value,
      port: port.value,
      taskFunCode: PARAMS_SETTING,
      sendTime: 10,
      deviceCode: deviceCode.value,
      data: encodeURIComponent(JSON.stringify(data))
    })
    ws.onmessage = event => {
      paramsLoading.value = false
      try {
        const rsp = JSON.parse(event.data)
        if (rsp.code === 200) {
          ElMessage.success('设备参数设置成功')
        } else {
          ElMessage.error('设备参数设置失败')
        }
      } catch (err) {
        console.log(err)
      }
      ws.close()
    }
  }
  const generateParams = form => {
    if (taskFunCode.value === SCAN_CODE) {
      return {
        dataType: 1,
        // thresholdType: form.thresholdType,
        // threshold: form.threshold,
        thresholdType: spectrum.thresholdType,
        threshold: spectrum.threshold,
        terminationDirection: 90,
        azimuthStep: 10,
        polarization: parseInt(form.polarizationType, 16),
        startFrequency: plotToNum(spectrum.startFreq + freBandRef.value.selectForm.startFreq),
        endFrequency: plotToNum(spectrum.endFreq + freBandRef.value.selectForm.endFreq),
        frequencyStep: spectrum.step
      }
    } else if (taskFunCode.value === DISPERSED_CODE) {
      // const discrete = spectrum.freqBandStr.split(',').map(item => {
      //   const [centerFreq, bandwidth] = item.split('|')
      //   return {
      //     frequency: plotToNum(centerFreq),
      //     analysisBandwidth: plotToNum(bandwidth)
      //   }
      // })
      return {
        dataType: 1,
        // thresholdType: form.thresholdType,
        // threshold: form.threshold,
        thresholdType: spectrum.thresholdType,
        threshold: spectrum.threshold,
        polarization: parseInt(form.polarizationType, 16),
        discrete: spectrum.discrete,
        frequencyCount: spectrum.discrete.length,
        analyseBandwidth: plotToNum(spectrum.bandwidth + scatterRef.value.selectForm.bandwidth)
      }
    }
  }

  const startWebsocket = form => {
    const params = generateParams(form)
    host.value = codeFindInfo.value.ip
    port.value = codeFindInfo.value.port
    linkScan(params, host.value, port.value, deviceCode.value)
  }

  const selectForm = ref({})
  // 使用 watchEffect 确保 selectForm 在数据变化时也能提供
  watchEffect(() => {
    if (freBandRef.value) {
      selectForm.value = freBandRef.value.selectForm
    } else {
      selectForm.value = {}
    }
  })

  provide('selectForm', selectForm)
  provide('freBandRef', freBandRef)

  const dealRouteInfo = () => {
    if (route.query?.data) {
      resData.value = JSON.parse(route.query.data)
      deviceCode.value = resData.value.code
      scanStore.setSpectrumData('routineAnalysis', {
        centerFreq: resData.value.centerFreq,
        bandwidth: resData.value.bandwidth,
        startFreq: resData.value.startFreq,
        endFreq: resData.value.endFreq,
        step: resData.value.step,
        freqSectionNum: resData.value.freqSectionNum,
        freqBandStr: resData.value.freqBandStr,
        type: resData.value.deviceScan.type,
        thresholdType: resData.value.deviceScan.thresholdType,
        threshold: resData.value.deviceScan.threshold
      })
      scanStore.updateTaskParams('routineAnalysis', resData.value.taskParams)
    } else {
      scanStore.setSpectrumData('routineAnalysis', {
        centerFreq: deviceData.value.deviceScan.centerFreq,
        bandwidth: deviceData.value.deviceScan.bandwidth,
        startFreq: deviceData.value.deviceScan.startFreq,
        endFreq: deviceData.value.deviceScan.endFreq,
        step: deviceData.value.deviceScan.step.toString(),
        freqSectionNum: deviceData.value.deviceScan.freqSectionNum,
        freqBandStr: deviceData.value.deviceScan.freqBandStr,
        type: deviceData.value.deviceScan.type,
        thresholdType: deviceData.value.deviceScan.thresholdType,
        threshold: deviceData.value.deviceScan.threshold
      })
      scanStore.updateTaskParams('routineAnalysis', deviceData.value.devicePara)
      deviceCode.value = deviceList.value.length > 0 ? deviceList.value[0].value : null
      host.value = deviceList.value.length > 0 ? deviceList.value[0].ip : null
      port.value = deviceList.value.length > 0 ? deviceList.value[0].port : null
    }
    freBandRef.value.dataChangeFun()
    scatterRef.value.dataChangeFun()
  }

  onMounted(async () => {
    await init()
  })

  /**路由激活 */
  onActivated(async () => {
    await init()
  })
</script>

<style scoped lang="scss">
  @import '@/assets/styles/mixin.scss';

  .online-container {
    @include boxBg('@/assets/images/boxBg.png');
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 1rem;
    overflow-y: auto;

    .main {
      height: 100%;
    }
  }

  .chart-form {
    padding-left: 10px;
  }
</style>
