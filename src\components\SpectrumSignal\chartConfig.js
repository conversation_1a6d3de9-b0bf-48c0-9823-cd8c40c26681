import useScanStore from '@/store/modules/scanMonitor'
import { cloneDeep, isUndefined } from 'lodash'

const { spectrumCopy } = useScanStore()
export const chartConfig = {
  chart: {
    backgroundColor: '#020e1f', // 图表背景色
    type: 'line', // 图表类型：折线图
    height: 400, // 图表高度
    spacingTop: 30, // 顶部间距
    spacingBottom: 0, // 底部间距
    spacingLeft: 0, // 左侧间距
    spacingRight: 10, // 右侧间距
    zoomType: 'xy', // 缩放类型：x 和 y 轴
    events: {
      redraw(ev) {
        // 检查图表中是否存在 spectrumMarkers 数组
        this.specturmMarkers &&
          // 如果存在，则对数组中的每个 marker 进行更新
          this.specturmMarkers.forEach(marker => {
            marker.update()
          })

        // 检查图表中是否存在 topMarker 标记
        this.topMarker && this.topMarker.update()
      }
    }

  },
  exporting: { enabled: false }, // 禁用导出功能
  reflow: true,
  credits: { enabled: false }, // 禁用版权信息
  accessibility: {
    enabled: false // 禁用无障碍功能
  },
  xAxis: {
    title: { text: '频率', style: { color: '#aeadad' } }, // X轴标题
    labels: {
      style: { color: '#aeadad' } // X轴标签样式
    },
    events: {
      setExtremes(ev) {
        const { max, min, target } = ev
        const axisMax = ev.target.dataMax
        const axisMin = ev.target.dataMin
        if (min < axisMax / 2 && max > axisMax / 2) {
          this.chart.zoomInMid = true
        }
        const dataMin = isUndefined(min) ? axisMin : min
        const dataMax = isUndefined(max) ? axisMax : max
        const tickInterval = (dataMax - dataMin) / 10
        target.update({ max: dataMax, min: dataMin, tickInterval })
        if (isUndefined(max)) {
          if (this.chart.zoomInMid) {
            this.chart.zoomInMid = false
            setTimeout(() => {
              this.setExtremes(0, dataMax)
            })
          }
        }
      }
    },
    tickAmount: 11, // 刻度数量
    lineColor: '#aeadad', // 轴线颜色
    lineWidth: 0 // 轴线宽度
  },
  yAxis: {
    title: { text: '幅度(dBm)', style: { color: '#aeadad' } }, // Y轴标题
    min: spectrumCopy.refLevel - 100,
    max: spectrumCopy.refLevel,
    gridLineColor: 'rgba(1, 84, 120, 0.8)', // 网格线颜色
    gridLineDashStyle: 'solid', // 网格线样式
    gridLineWidth: 0.5, // 网格线宽度
    lineColor: 'rgba(1, 84, 120, 1)', // Y轴线颜色
    tickAmount: 11, // 刻度数量
    labels: { style: { color: '#aeadad' } } // Y轴标签样式
  },
  title: { enabled: false, text: '' }, // 禁用标题
  boost: {
    enabled: true,
    useGPUTranslations: true, // 使用 GPU 加速坐标转换
    usePreAllocated: true, // 预分配内存提高性能
    seriesThreshold: 1, // 确保 Boost 在单个系列中始终开启
    allowForce: true // 允许在某些情况下强制使用 Boost
  },
  legend: {
    enabled: false // 禁用图例
    // itemStyle: { color: '#fff' },
    // itemHoverStyle: { color: '#f3f3f3' },
    // navigation: { enabled: false },
    // padding: 0,
    // margin: 2
  },
  series: [
    {
      id: 'current',
      name: '实时', // 实时数据
      color: '#ffff00', // 颜色：黄色
      marker: { enabled: false }, // 禁用标记
      animation: false, // 禁用动画
      enableMouseTracking: false, // 禁用鼠标追踪
      // type: 'line', // 类型：折线图
      data: [], // 数据
      lineWidth: 0.5, // 线宽
      boostThreshold: 500, // 加速阈值
      turboThreshold: 1000, // 启用 Turbo 模式，当数据量大于 1000 时启用
      gapSize: 1 // 间隙大小
    },
    {
      id: 'average',
      name: '平均', // 平均数据
      color: '#f56c6c', // 颜色：红色
      marker: { enabled: false },
      animation: false,
      enableMouseTracking: false,
      // type: 'line',
      data: [],
      lineWidth: 0.5,
      boostThreshold: 500, // 加速阈值
      turboThreshold: 1000, // 启用 Turbo 模式，当数据量大于 1000 时启用
      gapSize: 1
    },
    {
      id: 'max',
      name: '最大', // 最大数据
      color: '#67c23a', // 颜色：绿色
      marker: { enabled: false },
      animation: false,
      enableMouseTracking: false,
      // type: 'line',
      data: [],
      lineWidth: 0.5,
      boostThreshold: 500, // 加速阈值
      turboThreshold: 1000, // 启用 Turbo 模式，当数据量大于 1000 时启用
      gapSize: 1
    },
    {
      id: 'min',
      name: '最小', // 最小数据
      color: '#409eff', // 颜色：蓝色
      marker: { enabled: false },
      animation: false,
      enableMouseTracking: false,
      // type: 'line',
      data: [],
      lineWidth: 0.5,
      boostThreshold: 500, // 加速阈值
      turboThreshold: 1000, // 启用 Turbo 模式，当数据量大于 1000 时启用
      gapSize: 1
    },
    {
      id: 'limit',
      name: '门限', // 门限数据
      color: '#fff', // 颜色：白色
      marker: { enabled: false },
      animation: false,
      enableMouseTracking: false,
      // type: 'line',
      data: [],
      lineWidth: 0.5,
      boostThreshold: 500, // 加速阈值
      turboThreshold: 1000, // 启用 Turbo 模式，当数据量大于 1000 时启用s
      gapSize: 1
    }
  ],
  plotOptions: {
    series: {
      turboThreshold: 500, // 启用 Turbo 模式，当数据量大于 1000 时启用
      boostThreshold: 1,
      marker: { enabled: false },
      animation: false,
      shadow: false, // 关闭阴影
      enableMouseTracking: false, // 如果不需要交互，禁用鼠标跟踪
      states: {
        hover: {
          enabled: false
        }
      }
    },
    column: {
      borderRadius: '25%'
    }
  },
}
