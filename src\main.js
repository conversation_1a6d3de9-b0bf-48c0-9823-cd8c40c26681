import { createApp } from 'vue'
import '@/assets/styles/tailwind.css'
import '@/assets/styles/theme/light-var.css'
import '@/assets/styles/theme/dark-var.css'
import '@/assets/styles/index.scss' // global css
import '@/assets/styles/layout.less'
import '@/assets/styles/var.less'
// import '@/assets/styles/commonTable.less'
import App from './App'
import store from './store'
import router from './router'
import { regComponents } from './components' //注册全局组件
import directive from './directive' // 注册指令
import useDictStore from '@/store/modules/dict'
import plugins from './plugins' // plugins
import './permission' // permission control
// import { App, createApp } = 'vue'
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'
import 'xe-utils'
// import webUploader from 'WebUploader'

// createApp(App).use(useTable).mount('#app')
async function bootstrap() {
  const app = createApp(App)
  app.use(VXETable)
  // app.use(l7)
  app.use(router)
  app.use(store)
  app.use(plugins)
  regComponents(app)
  directive(app)
  const dict = useDictStore()
  await dict.getAllDictList().catch(err => { }) // 获取所有字典
  app.mount('#app')
}

void bootstrap()
