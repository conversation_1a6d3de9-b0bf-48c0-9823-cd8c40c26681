<template>
  <div>
    <div ref="waveform" id="waveform"></div>
    <div>
      <button @click="playPause">{{ isPlaying ? 'Pause' : 'Play' }}</button>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue'
  import WaveSurfer from 'wavesurfer.js'

  const props = defineProps({
    audioData: {
      type: Array,
      default: () => []
    }
  })

  const waveform = ref(null)
  const waveSurfer = ref(null)
  const isPlaying = ref(false)

  const playPause = () => {
    if (waveSurfer.value.isPlaying()) {
      waveSurfer.value.pause()
    } else {
      waveSurfer.value.play()
    }
    isPlaying.value = !isPlaying.value
  }

  const playAudio = audioData => {
    const blob = new Blob([new Uint8Array(audioData)], { type: 'audio/wav' })
    const url = URL.createObjectURL(blob)
    waveSurfer.value.load(url)
    waveSurfer.value.on('ready', () => {
      waveSurfer.value.play()
      isPlaying.value = true
    })
  }

  onMounted(() => {
    waveSurfer.value = WaveSurfer.create({
      container: waveform.value,
      height: 128,
      width: 300,
      splitChannels: false,
      normalize: false,
      waveColor: '#ff4e00',
      progressColor: '#dd5e98',
      cursorColor: '#ddd5e9',
      cursorWidth: 1,
      barWidth: 1,
      barGap: null,
      barRadius: null,
      barHeight: null,
      barAlign: '',
      minPxPerSec: 1,
      fillParent: true,
      mediaControls: true,
      autoplay: true,
      interact: true,
      dragToSeek: false,
      hideScrollbar: false,
      audioRate: 1,
      autoScroll: true,
      autoCenter: true
    })

    watch(
      () => props.audioData,
      newVal => {
        if (newVal.length > 0) {
          playAudio(newVal)
        } else {
          const fakeAudioData = generateFakeAudioData()
          playAudio(fakeAudioData)
        }
      },
      { immediate: true }
    )
  })

  function generateFakeAudioData() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()
    const sampleRate = audioContext.sampleRate
    const duration = 2 // Duration in seconds
    const numChannels = 1
    const frameCount = sampleRate * duration

    const buffer = audioContext.createBuffer(numChannels, frameCount, sampleRate)
    const data = buffer.getChannelData(0)

    // Generate a simple sine wave at 440Hz (A4 note)
    const frequency = 440
    for (let i = 0; i < frameCount; i++) {
      data[i] = Math.sin((2 * Math.PI * frequency * i) / sampleRate)
    }

    // Convert AudioBuffer to WAV byte array
    return bufferToWav(buffer)
  }

  function bufferToWav(buffer) {
    const numOfChan = buffer.numberOfChannels
    const length = buffer.length * numOfChan * 2 + 44
    const bufferArray = new ArrayBuffer(length)
    const view = new DataView(bufferArray)
    let offset = 0

    /* RIFF identifier */
    writeString(view, offset, 'RIFF')
    offset += 4
    /* file length */
    view.setUint32(offset, length - 8, true)
    offset += 4
    /* RIFF type */
    writeString(view, offset, 'WAVE')
    offset += 4
    /* format chunk identifier */
    writeString(view, offset, 'fmt ')
    offset += 4
    /* format chunk length */
    view.setUint32(offset, 16, true)
    offset += 4
    /* sample format (raw) */
    view.setUint16(offset, 1, true)
    offset += 2
    /* channel count */
    view.setUint16(offset, numOfChan, true)
    offset += 2
    /* sample rate */
    view.setUint32(offset, buffer.sampleRate, true)
    offset += 4
    /* byte rate (sample rate * block align) */
    view.setUint32(offset, buffer.sampleRate * numOfChan * 2, true)
    offset += 4
    /* block align (channel count * bytes per sample) */
    view.setUint16(offset, numOfChan * 2, true)
    offset += 2
    /* bits per sample */
    view.setUint16(offset, 16, true)
    offset += 2
    /* data chunk identifier */
    writeString(view, offset, 'data')
    offset += 4
    /* data chunk length */
    view.setUint32(offset, length - offset - 4, true)
    offset += 4

    // Write interleaved data
    for (let i = 0; i < buffer.length; i++) {
      for (let channel = 0; channel < numOfChan; channel++) {
        const sample = buffer.getChannelData(channel)[i]
        const clamped = Math.max(-1, Math.min(1, sample))
        view.setInt16(offset, clamped < 0 ? clamped * 0x8000 : clamped * 0x7fff, true)
        offset += 2
      }
    }

    return new Uint8Array(bufferArray)
  }

  function writeString(view, offset, string) {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i))
    }
  }
</script>

<style scoped>
  #waveform {
    width: 100%;
    height: 128px;
    background-color: black;
  }
</style>
