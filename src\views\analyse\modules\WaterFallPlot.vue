<template>
  <div ref="heatmap" :style="{ height: containerHeight + 'px' }" class="pt-5"></div>
</template>

<script setup>
  import ColorMap from 'colormap'

  const props = defineProps({
    title: {
      type: String,
      default: '瀑布图'
    },
    height: {
      type: Number,
      default: 50 // 代表一个屏幕有多少帧
    },
    minDb: {
      type: Number,
      default: -130
    },
    maxDb: {
      type: Number,
      default: 30
    },
    containerHeight: {
      type: Number,
      default: 300 // 容器高度
    },
    legendWidth: {
      type: Number,
      default: 120
    },
    isOnline: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    }
  })

  const heatmap = ref(null)
  const state = reactive({
    canvasCtx: null,
    fallsCanvasCtx: null,
    legendCanvasCtx: null,
    canvasWidth: 0,
    colormap: []
  })
  const firstRender = ref(true)
  const renderNum = ref(0)
  const plotData = ref([])

  const isDiscrete = computed(() => Array.isArray(props.data[0]))
  const distance = computed(() => (props.data[1] ? props.data[1][0] - props.data[0][0] : 1))
  const len = computed(() => {
    if (isDiscrete.value) {
      const totalDistance = props.data[props.data.length - 1][0] - props.data[0][0]
      return Math.floor(totalDistance / distance.value) + 1
    }
    return props.data.length
  })

  const initComponent = () => {
    if (!heatmap.value) {
      return
    }
    // 先清理之前的画布
    while (heatmap.value.firstChild) {
      heatmap.value.removeChild(heatmap.value.firstChild)
    }

    const { clientWidth, clientHeight } = heatmap.value
    // 初始化颜色图
    const colormap = initColormap()
    // 创建画布
    const { fallsCanvasCtx, canvasCtx, legendCanvasCtx, canvas } = createCanvas(
      clientWidth,
      clientHeight
    )
    // 绘制左边颜色图图例
    drawLegend(canvasCtx, legendCanvasCtx, colormap)
    state.canvasCtx = canvasCtx
    state.colormap = colormap
    state.fallsCanvasCtx = fallsCanvasCtx
    state.legendCanvasCtx = legendCanvasCtx
    state.canvasDom = canvas

    // console.log('组件初始化完成，画布宽度: ', canvas.width, ' 高度: ', canvas.height)
  }

  const initColormap = () => {
    return ColorMap({
      colormap: 'jet',
      nshades: 150,
      format: 'rgba',
      alpha: 1
    })
  }

  const createCanvas = (width, height) => {
    // 创建用来绘制的画布
    const fallsCanvas = document.createElement('canvas')
    fallsCanvas.width = width
    fallsCanvas.height = height
    const fallsCanvasCtx = fallsCanvas.getContext('2d')

    // 创建最终展示的画布
    const canvas = document.createElement('canvas')
    canvas.className = 'main_canvas'
    canvas.height = height - 2
    canvas.width = width
    heatmap.value.appendChild(canvas)
    const canvasCtx = canvas.getContext('2d')

    // 创建图例图层画布
    const legendCanvas = document.createElement('canvas')
    legendCanvas.width = 1
    legendCanvas.height = height
    const legendCanvasCtx = legendCanvas.getContext('2d')

    // console.log('创建画布，宽度: ', width, ' 高度: ', height)

    return {
      fallsCanvasCtx,
      canvasCtx,
      legendCanvasCtx,
      canvas
    }
  }

  // 更新瀑布图 传入要渲染的数据
  const updateChart = start => {
    const data = plotData.value.slice(start, start + 1024)
    updateWaterFallPlot(data)
  }

  const updateWaterFallPlot = data => {
    if (!data.length) {
      state.fallsCanvasCtx.clearRect(0, 0, state.fallsCanvasCtx.canvas.width, props.height)
      state.canvasCtx.clearRect(
        props.legendWidth,
        0,
        state.canvasCtx.canvas.width - props.legendWidth,
        state.canvasCtx.canvas.height
      )
      return
    }

    if (len.value !== state.canvasWidth) {
      state.canvasWidth = len.value
      state.fallsCanvasCtx.canvas.width = len.value
    }

    if (len.value === 0) {
      return
    }

    renderNum.value++
    addWaterfallRow(data)
    drawFallsOnCanvas(len.value)
  }

  // 在用于绘制的画布上绘制图像
  const addWaterfallRow = data => {
    if (!firstRender.value) {
      state.fallsCanvasCtx.drawImage(
        state.fallsCanvasCtx.canvas,
        0,
        0,
        len.value,
        props.height,
        0,
        1,
        len.value,
        props.height
      )
    } else {
      firstRender.value = false
    }

    const imageData = rowToImageData(data)
    state.fallsCanvasCtx.putImageData(imageData, 0, 0)
  }

  // 绘制单行图像
  const rowToImageData = data => {
    const imageData = state.fallsCanvasCtx.createImageData(len.value, 1)
    for (let i = 0; i < imageData.data.length; i += 4) {
      const value = data[i / 4] && data[i / 4].length ? data[i / 4][1] : data[i / 4] || 0
      const cIndex = getCurrentColorIndex(value)
      const color = state.colormap[cIndex]
      imageData.data[i + 0] = color[0]
      imageData.data[i + 1] = color[1]
      imageData.data[i + 2] = color[2]
      imageData.data[i + 3] = 255
    }
    return imageData
  }

  // 将绘制好的图像显示在主页面中
  const drawFallsOnCanvas = len => {
    const canvasWidth = state.canvasCtx.canvas.width
    const canvasHeight = state.canvasCtx.canvas.height
    if (!state.fallsCanvasCtx.canvas.width) return
    state.canvasCtx.drawImage(
      state.fallsCanvasCtx.canvas,
      -1,
      0,
      len + 1,
      props.height,
      props.legendWidth + 5,
      0,
      canvasWidth - props.legendWidth - 25,
      canvasHeight
    )
  }

  // 获取数据对应的颜色图索引
  const getCurrentColorIndex = data => {
    const outMin = 0
    const outMax = state.colormap.length - 1
    if (data <= props.minDb) {
      return outMin
    } else if (data >= props.maxDb) {
      return outMax
    } else {
      return Math.round(((data - props.minDb) / (props.maxDb - props.minDb)) * outMax)
    }
  }

  // 绘制颜色图图例
  const drawLegend = (canvasCtx, legendCanvasCtx, colormap) => {
    const imageData = legendCanvasCtx.createImageData(1, colormap.length)
    // 遍历颜色图集合
    for (let i = 0; i < colormap.length; i++) {
      const color = colormap[i]
      imageData.data[imageData.data.length - i * 4 + 0] = color[0]
      imageData.data[imageData.data.length - i * 4 + 1] = color[1]
      imageData.data[imageData.data.length - i * 4 + 2] = color[2]
      imageData.data[imageData.data.length - i * 4 + 3] = 255
    }
    legendCanvasCtx.putImageData(imageData, 0, 0)
    canvasCtx.drawImage(
      legendCanvasCtx.canvas,
      0, // source x
      0, // source y
      1, // source width
      colormap.length, // source height
      40, // destination x
      0, // destination y
      props.legendWidth / 4, // destination width
      canvasCtx.canvas.height // destination height
    )
    const parts = props.containerHeight < 300 ? 5 : 10
    canvasCtx.font = '12px Arial'
    canvasCtx.textAlign = 'end'
    canvasCtx.fillStyle = '#fff'
    const x = (props.legendWidth * 3) / 4 - 10
    canvasCtx.fillText(props.maxDb, x, 12)
    canvasCtx.fillText(props.minDb, x, props.containerHeight - 6)
    const dur = (props.maxDb - props.minDb) / parts
    for (let i = 1; i < parts; i++) {
      canvasCtx.fillText(
        props.minDb + dur * i,
        x,
        ((props.containerHeight - 22) * (parts - i)) / parts + 12
      )
    }
  }

  const handleResize = () => {
    const { clientWidth, clientHeight } = heatmap.value
    // console.log('调整大小，新的宽度：', clientWidth, '新的高度：', clientHeight)
    initComponent()
    if (!props.isOnline) {
      updateChart(playControl.cycleStart)
    } else {
      updateWaterFallPlot(props.data)
    }
  }

  onMounted(async () => {
    window.addEventListener('resize', handleResize)
    initComponent()
    if (!props.isOnline) {
      await requestChartsData()
      watchEffect(() => {
        updateChart(playControl.cycleStart)
      })
    } else {
      watch(
        () => props.data,
        () => {
          updateWaterFallPlot(props.data)
        }
      )
    }
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
</script>
