<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData'
  import useScanFormStore from '@/store/modules/form/scanForm'

  const fields = ref([
    {
      target: 'overlapType',
      name: '重叠处理',
      isRadio: true,
      options: [
        { label: '平均值', value: 1 },
        { label: '最大值', value: 2 }
      ]
    }
  ])

  const emit = defineEmits(['operation'])
  const model = reactive(new ModelData(fields.value, 'scan'))
  const scanFormStore = useScanFormStore()
  watch(
    () => scanFormStore.overlapType,
    () => {
      model.update()
    },
    { deep: true }
  )
</script>

<template>
  <data-form :fields="fields" :model="model" />
</template>
