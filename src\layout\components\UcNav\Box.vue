<template>
  <div class="uc-box" :style="{ height }">
    <svg class="arrow" width="25" :height="height + 2">
      <path 
        :d="path"
        fill="transparent"
        stroke="#00BFBF"
        stroke-width="2"
      />
    </svg>
    <slot />
  </div>
</template>

<script setup name="UcBox">
import { computed } from 'vue';

const props = defineProps({
  height: {
    type: Number,
    default: 72
  }
})

const path = computed(() => {
  const half = props.height / 2 + 1
  return `M 25,0 L 0,${half} L 25,${props.height + 2}`
})
</script>

<style lang="scss">
.uc-box {
  border: 2px solid #00BFBF;
  border-radius: 5px;
  box-shadow: 5px 5px 5px #027DB4;
  clip-path: polygon(24px 0, 100% 0, 100% 100%, 24px 100%, 0 50%);
  position: relative;
  padding-left: 50px;
  .arrow {
    position: absolute;
    top: -2px;
    left: -2px;
  }
}
</style>
