//导入openlayer
import { Overlay } from 'ol'
import { <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ector as VectorLayer } from 'ol/layer'
import { OSM, Vector as VectorSource } from 'ol/source'
import { Draw, Modify, Snap } from 'ol/interaction'
import { Point, LineString, Polygon, Circle } from 'ol/geom'
import { Circle as CircleStyle, Fill, Stroke, Style } from 'ol/style'
import { getLength } from 'ol/sphere'
import { unByKey } from 'ol/Observable'
import { createBox } from 'ol/interaction/Draw'
//导出方法
const feature = ref(null)
const draw = ref(null)
const measureTooltipElement = ref(null)
const measureTooltip = ref(null)
const listener = ref(null)
const mapMouseMove = ref(null)
const drawLayers = ref([])
const drawElements = ref([])
const source = new VectorSource()
const layer = new VectorLayer({
  source: source,
  //点的样式
  style: new Style({
    fill: new Fill({
      color: 'rg<PERSON>(255, 255, 255, 0.2)'
    }),
    stroke: new Stroke({
      color: '#33cc33',
      width: 2
    }),
    image: new CircleStyle({
      radius: 7,
      fill: new Fill({
        color: '#ffcc33'
      })
    })
  })
})

// 绘制点
export function drawPoint(map) {
  if (draw.value !== null) {
    map.removeInteraction(draw.value)
  }
  map.addLayer(layer)
  draw.value = new Draw({
    source: source,
    type: 'Point'
  })
  map.addInteraction(draw.value)
}

// 绘制线
export function drawLine(map) {
  if (draw.value !== null) {
    map.removeInteraction(draw.value)
  }
  map.addLayer(layer)
  draw.value = new Draw({
    source: source,
    type: 'LineString'
  })
  map.addInteraction(draw.value)
}
// 绘制面
export function drawPolygon(map) {
  if (draw.value !== null) {
    map.removeInteraction(draw.value)
  }
  map.addLayer(layer)

  draw.value = new Draw({
    source: source,
    type: 'Polygon'
  })
  map.addInteraction(draw.value)
}
// 绘制圆
export function drawCircle(map) {
  if (draw.value !== null) {
    map.removeInteraction(draw.value)
  }
  map.addLayer(layer)
  draw.value = new Draw({
    source: source,
    type: 'Circle'
  })
  map.addInteraction(draw.value)
}
// 绘制矩形
export function drawRectangle(map) {
  if (draw.value !== null) {
    map.removeInteraction(draw.value)
  }
  map.addLayer(layer)
  draw.value = new Draw({
    source: source,
    type: 'Circle',
    geometryFunction: createBox()
  })
  map.addInteraction(draw.value)
}
export function clearMap(map) {
  source.clear()
  if (draw.value !== null) {
    map.removeInteraction(draw.value)
  }
}
export function cancalDraw(map) {
  if (draw.value !== null) {
    map.removeInteraction(draw.value)
  }
}
// 测距
export function distance(map) {
  if (draw.value !== null) {
    map.removeInteraction(draw.value)
  }
  draw.value = new Draw({
    source,
    type: 'LineString',
    style: new Style({
      fill: new Fill({
        color: 'rgba(255, 255, 255, 0.2)'
      }),
      stroke: new Stroke({
        color: 'rgba(0, 0, 0, 0.5)',
        lineDash: [10, 10],
        width: 4
      }),
      image: new CircleStyle({
        radius: 5,
        stroke: new Stroke({
          color: 'rgba(0, 0, 0, 0.7)'
        }),
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.2)'
        })
      })
    })
  })

  // 开始监听绘制
  draw.value.on('drawstart', evt => {
    feature.value = evt.feature
    let tooltipCoord = evt.coordinate
    listener.value = feature.value.getGeometry().on('change', evt => {
      const geom = evt.target
      let output = formatLength(geom)
      // let output = formatArea(geom)
      tooltipCoord = geom.getLastCoordinate()
      measureTooltipElement.value.innerHTML = output
      measureTooltip.value.setPosition(tooltipCoord)
    })
  })

  // 双击绘制完成
  draw.value.on('drawend', () => {
    measureTooltipElement.value.className = 'ol-tooltip ol-tooltip-static'
    measureTooltip.value.setOffset([0, -7])
    feature.value = null
    measureTooltipElement.value = null
    createMeasureTooltip(map)
    unByKey(listener.value)
  })

  // 格式化长度
  const formatLength = line => {
    const length = getLength(line)
    let output
    if (length > 100) {
      output = Math.round((length / 1000) * 100) / 100 + ' ' + 'km'
    } else {
      output = Math.round(length * 100) / 100 + ' ' + 'm'
    }
    return output
  }
  // const formatArea = polygon => {
  //   const area = getArea(polygon)
  //   let output
  //   if (area > 10000) {
  //     output = Math.round((area / 1000000) * 100) / 100 + ' ' + 'km<sup>2</sup>'
  //   } else {
  //     output = Math.round(area * 100) / 100 + ' ' + 'm<sup>2</sup>'
  //   }
  //   return output
  // }

  // createHelpTooltip()
  createMeasureTooltip(map)
  map.addLayer(layer)
  drawLayers.value.push(layer)
  map.addInteraction(draw.value)
}
//取消绘制
export function cancal(map) {
  for (let i = 0; i < drawLayers.value.length; i++) {
    map.removeLayer(drawLayers.value[i])
  }
  for (let i = 0; i < drawElements.value.length; i++) {
    map.removeOverlay(drawElements.value[i])
  }
  drawLayers.value = []
  drawElements.value = []
  map.removeInteraction(draw.value)
  unByKey(mapMouseMove)
}
export function createMeasureTooltip(map) {
  if (measureTooltipElement.value) {
    measureTooltipElement.value.parentNode.removeChild(measureTooltipElement.value)
  }
  measureTooltipElement.value = document.createElement('div')
  measureTooltipElement.value.className = 'ol-tooltip ol-tooltip-measure'
  measureTooltip.value = new Overlay({
    element: measureTooltipElement.value,
    offset: [0, -15],
    positioning: 'bottom-center',
    stopEvent: false,
    insertFirst: false
  })
  drawElements.value.push(measureTooltip.value)
  map.addOverlay(measureTooltip.value)
}
//修改type类型 point，LineString，Polygon，Circle
// map.removeInteraction(lineDraw) 取消当前点线面绘制
