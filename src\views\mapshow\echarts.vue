<template>
  <div id="chart" style="width: 350px; height: 220px;" />
</template>
<script setup>
import * as echarts from "echarts"
var dataEchart;
import { ref,onMounted }  from "vue"
onMounted(() => {
  initChart()
})
const initChart = () => {
  dataEchart = echarts.init(document.getElementById("chart"));
  updateChart()
}
const updateChart = () => {
  const options = {
    tooltip: {
      axisPointer: {
        label: {
          show: true
        }
      }
    },
    xAxis: {
      type: 'category',
      data: ['1', '2', '3', '4', '5','6','7','8','9','10'],
    },
    yAxis: {
      type: 'value',
    },
    series: [{
      type: 'line',
      data: [10, 20, 30, 40, 50,30,66,54,45,99],
    }],
    grid:{
      // left:'6%',
    },
    toolbox: {
    show: true,
    feature: {
      dataView: { readOnly: false },
      magicType: { type: ['line', 'bar'] },
      restore: {},
      saveAsImage: {}
    }
  },
  };

  dataEchart.setOption(options);
};
</script>
<style>
</style>

