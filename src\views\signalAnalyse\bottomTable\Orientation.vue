<template>
  <el-table
    border
    :data="tableList"
  >
    <el-table-column
      label="序号"
      type="index"
      width="60"
      align="center"
    />
    <el-table-column
      prop="name"
      label="参数名称"
      align="center"
      width="200"
    />
    <el-table-column prop="value" label="参数值" align="center">
      <template #default="{ row }">
        <el-tooltip placement="top" :content="handleRowValue(row.value)">
          <span class="over-hide">{{ handleRowValue(row.value) }}</span>
        </el-tooltip>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup name="Orientation">
import { paramTypes } from '@/constant/types'
import { round } from 'lodash';

const props = defineProps({
  paramList: {
    type: Object,
    default: () => ({})
  }
})

const tableList = computed(() => {
  return Object.keys(props.paramList).map(key => {
    let val = props.paramList[key] 
    if (key === 'frequency') {
      val = round(val/1e6, 3)
    }
    if (key === 'bandwidth') {
      val = round(val/1000, 3)
    }
    return {
      name: paramTypes[key],
      value: val
    }
  })
})

const handleRowValue = (val) => {
  if (val instanceof Array) {
    return val.join(',')
  }
  return val
}
</script>

<style scoped lang="scss">
.over-hide {
  white-space: nowrap;
  overflow: hidden;
  line-height: 38px;
  padding: 0 8px;
}
</style>