<template>
  <data-form :fields="fields" :model="model" />
  <!-- <el-button style="width: 80%;margin: 0 10%;" @click="emit('operation')">信号分选</el-button> -->
</template>

<script setup>
  import DataForm from '@/components/DataForm'
  import ModelData from '@/common/classes/modelData';
  import { rateUnits } from '@/constant/units'
  import useRadarScanFormStore from '@/store/modules/form/radarScanForm'
  import useChartsStore from '@/store/modules/charts'

  const route = useRoute()
  const emit = defineEmits(['operation'])
  const chartsStore = useChartsStore()
  const file = route.path.includes('RadarAnalyse') ?  chartsStore.getRadarInfo() : chartsStore.getFileInfo()
  const fields = ref([
    {
      target: 'fileCenterFreIn',
      name: '中心频率',
      options: rateUnits,
      appendSelect: true,
    },
    {
      target: 'fileIfbw',
      name: '中频带宽',
      options: rateUnits,
      appendSelect: true,
    },
    {
      target: 'fileSamplingRate',
      name: '采样率',
      options: rateUnits,
      appendSelect: true,
    },
    // {
    //   target: 'fileBitRate',
    //   name: '码速率',
    //   options: rateUnits,
    //   appendSelect: true,
    // },
    { target: 'iqNum', name: 'iQ点数', disabled: true },
    // {
    //   target: 'iqReverse',
    //   name: 'IQ颠倒',
    //   isCheckbox: true,
    //   disabled: computed(() => file.fileType === '1') 
    // },
    // {
    //   target: 'logarithm',
    //   name: '取对数',
    //   isCheckbox: true,
    //   disabled: computed(() => file.fileType === '1') 
    // }
  ])
  const model = reactive(new ModelData(fields.value, 'radarScan'))
  const scanFormStore = useRadarScanFormStore()
  watch(() => scanFormStore, () => {
    model.update()
  }, { deep: true })
</script>
