<template>
  <div>
    <vxe-toolbar class="vxe-toolbar">
      <template #buttons>
        <vxe-input
          v-model="filterOption.fileName"
          type="search"
          placeholder="请输入文件名称"
          clearable
        />
        <vxe-input
          v-model="filterOption.startTime"
          type="date"
          placeholder="请选择起始时间"
          class="time-button"
          clearable
        /> 
        <p class="interval-p"> ~ </p>
        <vxe-input
          v-model="filterOption.endTime"
          clearable
          type="date"
          placeholder="请选择截止时间"
        />
        <vxe-button 
          status="primary" 
          content="查询" 
          :disabled="disabled"
          class="toolbar-button"
          @click="timeSearch(filterOption)"
        />
        <vxe-button
          status="success"
          content="上传"
          class="toolbar-button"
          @click="uploadFile(false)"
        />
        <vxe-button
          status="danger"
          content="批量删除"
          class="toolbar-button"
          @click="deleteAll"
        />
      </template>
    </vxe-toolbar>
    <vxe-table
      ref="radarTable"
      :loading="loading"
      border
      round
      :checkbox-config="{ labelField: 'listId' }"
      :data="list"
      :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
      :keep-source="true"
      class="mytable-scrollbar"
      @edit-closed="saveEdit()"
      @checkbox-change="selectChangeEvent"
      @checkbox-all="selectChangeEvent"
    >
      <vxe-column
        type="checkbox"
        title="序号"
        width="120"
        fixed="left"
      />
      <vxe-column
        field="fileName"
        title="文件名称"
        width="200"
        fixed="left"
      />
      <vxe-column
        field="uploadTime"
        title="上传时间"
        width="150"
        :filters="[
          { data: '', label: '起:' },
          { data: '', label: '止:' }
        ]"
        :filter-method="customDateFilterMethod"
      >
        <template #filter="{ $panel, column }"> 
          <template v-for="(option, i) in column.filters" :key="i">
            <div class="uploadtime-style">
              <span class="time-span">{{ option.label }}</span>
              <vxe-input
                v-model="option.data"
                type="date"
                placeholder="请选择"
                transfer
                clearable
                @input="$panel.changeOption($event, !!option.data, option)"
              />
            </div>
          </template>
        </template>
      </vxe-column>
      <vxe-column
        field="radarNumber"
        title="雷达编号"
        width="120"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.radarNumber"
            type="text"
            class="input-name"
            maxlength="30"
          >
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          >
        </template>
      </vxe-column>
      <vxe-column
        field="collectionTime"
        title="采集时间"
        width="150"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.collectionTime"
            type="text"
            class="input-name"
            @input=" row.collectionTime = Number(row.collectionTime.replace(/[^/0/1/2/3/4/5/6/7/8/9/./,/，/]/ig,''))"
          >
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          >
        </template>
      </vxe-column>
      <vxe-column
        field="fileTypeRadar"
        title="文件类型"
        width="150"
        :filters="[{data:''}]"
        :edit-render="{ name: '$select' }"
      >
        <template #default="{ row }">
          <span>{{ formatFiletype(row.fileTypeRadar) }}</span>
        </template>
        <template #edit="{ row }">
          <select v-model="row.fileTypeRadar" class="select-style" transfer>
            <option
              v-for="(item,index) in filetyperadarList"
              :key="index"
              :value="item.value"
              :label="item.label"
            />
          </select>
        </template>
      </vxe-column>
      <vxe-column
        field="collectionMethod"
        title="采集方式"
        width="150"
        :filters="[{data:''}]"
        :edit-render="{ name: '$select' }"
      >
        <template #default="{ row }">
          <span>{{ formatAcquisitionMode(row.collectionMethod) }}</span>
        </template>
        <template #edit="{ row }">
          <select v-model="row.collectionMethod" class="select-style" transfer>
            <option
              v-for="(item,index) in collectionMethodList"
              :key="index"
              :value="item.value"
              :label="item.label"
            />
          </select>
        </template>
      </vxe-column>
      <vxe-column
        field="intermediateFrequency"
        title="中频"
        width="170"
        :filters="[{data:''}]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.intermediateFrequency"
            class="input-name"
            type="text"
            maxlength="15"
            @input=" row.intermediateFrequency = row.intermediateFrequency.replace(/[^/0/1/2/3/4/5/6/7/8/9/./,/，/k/K/H/h/Z/z/M/m/G/g/]/ig,'')"
            @blur="showData(row.intermediateFrequency)"
          >
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          >
        </template>
      </vxe-column>
      <vxe-column
        field="centerfreqIn"
        title="中心频率"
        width="140"
        :filters="[{data:''}]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.centerfreqIn"
            class="input-name"
            type="text"
            maxlength="15"
            @input=" row.centerfreqIn = row.centerfreqIn.replace(/[^/0/1/2/3/4/5/6/7/8/9/./,/，/k/K/H/h/Z/z/M/m/G/g/]/ig,'')"
            @blur="showData(row.centerfreqIn)"
          >
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          >
        </template>
      </vxe-column>
      <vxe-column
        field="samplerateIn"
        title="采样率"
        width="140"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.samplerateIn"
            class="input-name"
            type="text"
            maxlength="15"
            @input=" row.samplerateIn = row.samplerateIn.replace(/[^/0/1/2/3/4/5/6/7/8/9/./,/，/k/K/H/h/Z/z/M/m/G/g/]/ig,'')"
            @blur="showData(row.samplerateIn)"
          >
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          >
        </template>
      </vxe-column>
      <vxe-column
        field="samplingBw"
        title="采样带宽"
        width="140"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.samplingBw"
            type="text"
            class="input-name"
            @blur="showData(row.samplingBw)"
            @input=" row.samplingBw = row.samplingBw.replace(/[^/0/1/2/3/4/5/6/7/8/9/./，/,/k/K/H/h/Z/z/M/m/G/g/]/ig,'')"
          >
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          >
        </template>
      </vxe-column>
      <vxe-column
        field="samplingNum"
        title="采样点数"
        width="120"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.samplingNum"
            class="input-name"
            type="text"
            @input=" row.samplingNum = Number(row.samplingNum.replace(/[^/0/1/2/3/4/5/6/7/8/9/./,/，/]/ig,''))"
          >
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          >
        </template>
      </vxe-column>
      <vxe-column
        field="quantizationBits"
        title="量化位数"
        width="120"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.quantizationBits"
            type="text"
            class="input-name"
            @input=" row.quantizationBits = Number(row.quantizationBits.replace(/[^/0/1/2/3/4/5/6/7/8/9/./,/，/]/ig,''))"
          >
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          >
        </template>
      </vxe-column>
      <vxe-column
        field="channelsNum"
        title="通道数"
        width="120"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.channelsNum"
            type="text"
            class="input-name"
            @input=" row.channelsNum = Number(row.channelsNum.replace(/[^/0/1/2/3/4/5/6/7/8/9/./,/，/]/ig,''))"
          >
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          >
        </template>
      </vxe-column>
      <vxe-column
        field="pulseCount"
        title="脉冲数"
        width="120"
        :filters="[{ data: '' }]"
        :filter-method="fliterData"
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <input
            v-model="row.pulseCount"
            type="text"
            class="input-name"
            @input=" row.pulseCount = Number(row.pulseCount.replace(/[^/0/1/2/3/4/5/6/7/8/9/./,/，/]/ig,''))"
          >
        </template>
        <template #filter="{ $panel, column }">
          <input
            v-for="(option, index) in column.filters"
            :key="index"
            v-model="option.data"
            type="type"
            @input="$panel.changeOption($event, !!option.data, option)"
          >
        </template>
      </vxe-column>
      <vxe-column
        field="操作"
        title="操作"
        width="260"
        fixed="right"
      >
        <template #default="{ row }">
          <vxe-button
            status="primary"
            content="分析"
            :transfer="true"
            @click="goAnalyseView(row)"
          />
          <vxe-button status="primary" content="下载" @click="downRadarFile(row)" />
          <vxe-button status="primary" content="删除" @click="deleteCurrentLine(row)" />
        </template>
      </vxe-column>
    </vxe-table>
    <radarPopup :message="childValue" :debugmodemessage="debugmodeMessage" @childcLick="closeModal" />
    <!-- 分页 -->
    <p>
      <vxe-pager
        v-model:current-page="curPage"
        v-model:page-size="size"
        class="vxe-page"
        perfect
        :total="total"
        :page-sizes="[10, 20, 50, 100, 200, 500]"
        :layouts="[ 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']"
      />
    </p>
  </div>
</template>
<script setup>
  import {displayUnitConversion, showData, fliterData, downRadarFile, customDateFilterMethod} from '@/api/tool/filemanage'
  import radarPopup  from "@/components/Popup/radarPopup"
  import { onMounted, ref } from 'vue'
  import useChartsStore from '@/store/modules/charts'
  import useDictStore from '@/store/modules/dict'
  import { useRouter } from 'vue-router'
  import { radarSingalPost } from '@/api/radarsingalManage'
  import useList from '@/api/tool/filemanage/tableFunciton'
  const filterOption = ref({});
  const fileId = ref(1)
  const radarTable = ref(null)
  const {list,loading,curPage,size,total,loadData,timeSearch,deleteCurrentLine,deleteAll,selectChangeEvent,saveEdit} = useList(radarSingalPost,filterOption,fileId,radarTable);
  const router = useRouter()
  const disabled = ref(false)
  const debugmodeMessage = ref([])
  const childValue = ref(false)
  const filetyperadarList = ref([])
  const timeout = ref(null)
  //采集方式 
  const collectionMethodList = ref([])
  onMounted(() => {
    timeout.value = setTimeout(() => {
      getdataTypeValue()
      clearTimeout(timeout.value)
    }, 500);
  })
  //获取到对应数据字典的值  
  const getdataTypeValue =  () => {
    const store = useDictStore()
    const filetyperadar = store.dict.filter(item => item.dictType == "file_type_radar" )
    filetyperadar.forEach( item =>{
      filetyperadarList.value.push({label:item.dictLabel,value:Number(item.dictValue)})
    })
    const $table = radarTable.value
    const fileTypeRadarcolumn = $table.getColumnByField('fileTypeRadar')
    if (fileTypeRadarcolumn) {
      $table.setFilter(fileTypeRadarcolumn,filetyperadarList.value)
      $table.updateData()
    }
    const collectionMethod = store.dict.filter(item => item.dictType == "collection_method" )
    collectionMethod.forEach( item =>{
      collectionMethodList.value.push({label:item.dictLabel,value:Number(item.dictValue)})
    })
    const collectionMethodcolumn = $table.getColumnByField('collectionMethod')
    if (collectionMethodcolumn) {
      $table.setFilter(collectionMethodcolumn,collectionMethodList.value)
      $table.updateData()
    }
  }
  const closeModal = status =>{
    childValue.value = status
    loadData()
  }
  const chartsStore = useChartsStore()
  // 跳到分析页面  
  const goAnalyseView = (row) => {
    chartsStore.setRadarInfo(transToStore(row), '1')
    router.push({
      name: 'RadarAnalyse',
    })
  }
  
  // 缓存文件数据转化 
  const transToStore = (row) => {
    const result = {}
    const requiredKeys = [
      'uploadTime',
      'radarNumber',
      'collectionTime',
      'fileTypeRadar',
      'collectionMethod',
      'intermediateFrequency',
      'centerfreqIn',
      'samplerateIn',
      'samplingBw',
      'samplingNum',
      'quantizationBits',
      'channelsNum',
      'pulseCount'
    ]
    Object.keys(row).forEach(key => {
      if (!requiredKeys.includes(key)) {
        return
      }
      if (typeof row[key] === 'string' && row[key].includes('Hz')) {
        result[key] = displayUnitConversion(row[key])
      } else {
        result[key] = row[key]
      }
    })
    result.fileName = row.fileName
    return result
  }
  //上传文件  
  const uploadFile = () => {
    childValue.value = true
  }
  // 文件类型  
  const formatFiletype = value => {
    let txtArr = filetyperadarList.value.filter(item => {
      return value == item.value || value == item.label
    })
    return txtArr.length > 0 ? txtArr[0].label : ''
  }
  // 采集方式
  const formatAcquisitionMode = value => {
    let txtArr = collectionMethodList.value.filter(item => {
      return value == item.value || value == item.label
    })
    return txtArr.length > 0 ? txtArr[0].label : ''
  }

</script>
<style lang="less" scoped>
  .time-button {
    margin: 0 20px;
  }
  .interval-p{
    margin: 0 16px 0 0;
  }
  .toolbar-button{
    margin: 0 0 0 20px;
  }
  .nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .home {
    background-color: none;
    padding: 0;
  }

  .exp-list {
    margin-bottom: 10px;

    .project-desc {
      height: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: pre-line;
      margin-bottom: 10px;
      font-size: 14px;
    }

    li {
      font-size: 12px;
      display: flex;
      color: #aaa;
      text-align: right;

      span {
        float: left;
        margin-right: 2em;
        color: #888;
        text-align: right;
      }

      & > div {
        flex: 1;
      }
    }
  }

.uploadtime-style{
    display: flex
  }
  .time-span{
    line-height: 42px; margin-right: 5px; margin-left: 10px
  }
  /* 增加筛选颜色 */
  .keyword-lighten {
    color: #000;
    background-color: #ffff00;
  }
  .input-name{
    width: 100%;
    height: 34px;
    border-radius: 4px;
    outline: 0;
    margin: 0;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 .6em;
    color: var(--chart-text-color);
    border: 1px solid #dcdfe6;
    background-color:var(--background-color);
    box-shadow: none;
  }
.select-style{
  background-color: var(--background-color);
  border: 1px solid var(--chart-text-color);
  border-radius: 4px;
  padding: 8px;
  color: var(--chart-text-color);
  width: 100%;
  cursor: pointer;
}
:deep(.vxe-select-option--wrapper){
  background-color: var(--scrollbar-color) !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-table--filter-wrapper .vxe-table--filter-body > li:hover){
  background-color: var(--scrollbar-color) !important;
  border: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-table--filter-wrapper .vxe-table--filter-header > li:hover){
  background-color: var(--scrollbar-color) !important;
  border: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-table--filter-wrapper){
  background-color: var(--background-color) !important;
  border: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-select-option--wrapper){
  background-color: var(--background-color) !important;
  border: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}

:deep(.vxe-table--body){
  width: 100% !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-table--header){
  width: 100% !important;
}
:deep(.vxe-pager--goto){
  background-color: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-pager--num-btn){
  background-color: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-pager--next-btn){
  background-color: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-pager--jump-next){
  background-color: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-pager--jump-prev){
  background-color: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-pager--prev-btn){
  background-color: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-input--inner){
  background-color: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
:deep(.vxe-table--header-wrapper){
  background-color: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
.vxe-toolbar{
  background-color: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
.vxe-page{
  background-color: var(--background-color) !important;
  color: var(--chart-text-color) !important;
}
.mytable-scrollbar ::-webkit-scrollbar {
  width: 10px;
  height: 15px;
}
// /*滚动条的轨道*/
.mytable-scrollbar ::-webkit-scrollbar-track-piece{
  background-color: var(--background-color);
}
/*滚动条里面的小方块，能向上向下移动*/
.mytable-scrollbar ::-webkit-scrollbar-thumb {
  background-color: var(--table-scrollbar-color);
  border-radius: 5px;
}
.mytable-scrollbar ::-webkit-scrollbar-thumb:hover {
  background-color: var(--table-scrollbar-color);
}
.mytable-scrollbar ::-webkit-scrollbar-thumb:active {
  background-color: var(--table-scrollbar-color);
}
/*边角，即两个滚动条的交汇处*/
.mytable-scrollbar ::-webkit-scrollbar-corner {
  background-color: var(--background-color);
}
</style>