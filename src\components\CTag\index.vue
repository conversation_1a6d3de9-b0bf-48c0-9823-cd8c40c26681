<template>
  <span v-if="color">
    <el-tooltip v-if="text" placement="bottom-start">
      <template #content><span class="tooltipcontent">{{ text }}</span></template>
      <el-tag :type="parseByColor.color" plain>{{ parseByColor.text }}</el-tag>
    </el-tooltip>
    <el-tag v-else :type="parseByColor.color" plain>{{
      parseByColor.text
    }}</el-tag>
  </span>
</template>
<script>
export default {
  name: "CTag",
  props: {
    color: {
      type: String,
      default: "",
    },
    text: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  computed: {
    parseByColor() {
      return (
        {
          green: { color: "", text: "正常" },
          red: { color: "danger", text: "异常" },
          yellow: { color: "warning", text: "警告" },
        }[this.color] || { color: "", text: "" }
      );
    },
  },
};
</script>
<style>
.tooltipcontent {
  white-space: pre-line;
}
</style>
